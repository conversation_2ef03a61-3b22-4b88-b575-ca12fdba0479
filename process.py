from config import *
import pandas as pd
import numpy as np
from collections import defaultdict
import os


def format_sample(file_paths, output_path):
    text = bio = pola = ''
    items = []
    for file_path in file_paths:
        with open(file_path) as f:
            for line in f.readlines():
                # 单独的空行，表示句子间隔
                if line == '\n':
                    items.append(
                        {'text': text.strip(), 'bio': bio.strip(), 'pola': pola.strip()})
                    text = bio = pola = ''
                    continue
                # 文本、bio标记、情感极性
                t, b, p = line.split(' ')
                text += t + ' '
                bio += b + ' '
                pola += p.strip() + ' '
                # 情感极性修正，2表示好评，改为1
                # p = str(1) if p.strip() == str(2) else p.strip()
                # pola += p + ' '
    df = pd.DataFrame(items)
    df.to_csv(output_path, index=None)


def check_label():
    df = pd.read_csv(TRAIN_FILE_PATH)
    dct = {}
    for index, row in df.iterrows():
        for b, p in zip(row['bio'].split(), row['pola'].split()):
            # 删除异常值
            if b == 'B-ASP' and p == '-1':
                print(index, row)
                df.drop(index=index, inplace=True)
            cnt = dct.get((b, p), 0)
            dct[(b, p)] = cnt+1
    print(dct)
    df.to_csv(TRAIN_FILE_PATH, index=None)


def split_sample():
    file_name = './output/process/atepc.sample.all.csv'
    df = pd.read_csv(file_name)
    df = df.sample(frac=1)  # 随机打乱数据
    df.reset_index(inplace=True, drop=True)
    n = len(df)

    # 划分为训练集(70%)、验证集(15%)和测试集(15%)
    train_size = int(n * 0.7)
    val_size = int(n * 0.15)

    # 保存三个数据集
    df.loc[:train_size, :].to_csv(
        './output/process/atepc.sample.train.csv', index=None)
    df.loc[train_size:train_size+val_size,
           :].to_csv('./output/process/atepc.sample.val.csv', index=None)
    df.loc[train_size+val_size:,
           :].to_csv('./output/process/atepc.sample.test.csv', index=None)

    # 打印每个数据集的大小
    print(f"数据集划分完成:")
    print(f"训练集大小: {len(df.loc[:train_size, :])} 条")
    print(f"验证集大小: {len(df.loc[train_size:train_size+val_size, :])} 条")
    print(f"测试集大小: {len(df.loc[train_size+val_size:, :])} 条")


def check_label_sample():
    df = pd.read_csv('./output/process/atepc.sample.all.csv')
    dct = {}
    drop_indices = set()  # 使用集合避免重复索引
    for index, row in df.iterrows():
        for b, p in zip(row['bio'].split(), row['pola'].split()):
            # 删除异常值
            if (b == 'B-ASP' or b == 'I-ASP') and p not in ['0', '1', '2']:
                drop_indices.add(index)  # 添加到待删除集合
            elif b == 'O' and p != '-1':
                drop_indices.add(index)  # 添加到待删除集合
            elif b == '0':
                drop_indices.add(index)  # 添加到待删除集合
            cnt = dct.get((b, p), 0)
            dct[(b, p)] = cnt+1
    # 统一删除所有异常行
    df.drop(index=list(drop_indices), inplace=True)
    print("Deleted rows:", len(drop_indices))
    print("Label counts:", dct)
    df.to_csv('./output/process/atepc.sample.all.csv', index=None)


def get_sentiment_category(row):
    """
    根据优先级确定一条数据的情感类别：
    1. 如果有Negative(0)，则归为Negative类
    2. 如果没有Negative但有Neutral(1)，则归为Neutral类
    3. 其余情况归为Positive(2)类
    """
    pola_values = set(row['pola'].split())

    # 优先级1：包含Negative(0)
    if '0' in pola_values:
        return 'Negative'
    # 优先级2：包含Neutral(1)
    elif '1' in pola_values:
        return 'Neutral'
    # 优先级3：其余情况视为Positive(2)
    else:
        return 'Positive'


def balanced_split_sample():
    """
    按情感类别平衡划分数据集，保持各集合中情感分布比例一致
    """
    file_name = './output/process/atepc.sample.all.csv'
    df = pd.read_csv(file_name)

    # 创建输出目录
    output_dir = './output/process/balanced'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 为每条数据分配情感类别
    df['sentiment_category'] = df.apply(get_sentiment_category, axis=1)

    # 按情感类别分组
    negative_df = df[df['sentiment_category'] == 'Negative']
    neutral_df = df[df['sentiment_category'] == 'Neutral']
    positive_df = df[df['sentiment_category'] == 'Positive']

    # 打印各类别数量和比例
    total = len(df)
    neg_count = len(negative_df)
    neu_count = len(neutral_df)
    pos_count = len(positive_df)

    print(f"情感分布统计:")
    print(f"Negative: {neg_count} 条 ({neg_count/total:.2%})")
    print(f"Neutral: {neu_count} 条 ({neu_count/total:.2%})")
    print(f"Positive: {pos_count} 条 ({pos_count/total:.2%})")

    # 随机打乱每个类别的数据
    negative_df = negative_df.sample(frac=1, random_state=42)
    neutral_df = neutral_df.sample(frac=1, random_state=42)
    positive_df = positive_df.sample(frac=1, random_state=42)

    # 计算每个类别在各数据集中的数量
    train_ratio, val_ratio, test_ratio = 0.7, 0.15, 0.15

    neg_train = int(neg_count * train_ratio)
    neg_val = int(neg_count * val_ratio)
    neg_test = neg_count - neg_train - neg_val

    neu_train = int(neu_count * train_ratio)
    neu_val = int(neu_count * val_ratio)
    neu_test = neu_count - neu_train - neu_val

    pos_train = int(pos_count * train_ratio)
    pos_val = int(pos_count * val_ratio)
    pos_test = pos_count - pos_train - pos_val

    # 划分数据集
    train_df = pd.concat([
        negative_df.iloc[:neg_train],
        neutral_df.iloc[:neu_train],
        positive_df.iloc[:pos_train]
    ])

    val_df = pd.concat([
        negative_df.iloc[neg_train:neg_train+neg_val],
        neutral_df.iloc[neu_train:neu_train+neu_val],
        positive_df.iloc[pos_train:pos_train+pos_val]
    ])

    test_df = pd.concat([
        negative_df.iloc[neg_train+neg_val:],
        neutral_df.iloc[neu_train+neu_val:],
        positive_df.iloc[pos_train+pos_val:]
    ])

    # 随机打乱每个数据集
    train_df = train_df.sample(frac=1, random_state=42)
    val_df = val_df.sample(frac=1, random_state=42)
    test_df = test_df.sample(frac=1, random_state=42)

    # 删除辅助列
    train_df = train_df.drop(columns=['sentiment_category'])
    val_df = val_df.drop(columns=['sentiment_category'])
    test_df = test_df.drop(columns=['sentiment_category'])

    # 保存数据集
    train_df.to_csv(f'{output_dir}/atepc.sample.train.csv', index=None)
    val_df.to_csv(f'{output_dir}/atepc.sample.val.csv', index=None)
    test_df.to_csv(f'{output_dir}/atepc.sample.test.csv', index=None)

    # 打印各数据集中的情感分布
    print(f"\n按情感类别平衡划分数据集完成:")
    print(f"训练集大小: {len(train_df)} 条")
    print(f"验证集大小: {len(val_df)} 条")
    print(f"测试集大小: {len(test_df)} 条")

    # 验证各数据集中的情感分布
    for name, dataset in [("训练集", train_df), ("验证集", val_df), ("测试集", test_df)]:
        neg = sum(1 for _, row in dataset.iterrows()
                  if '0' in row['pola'].split())
        neu = sum(1 for _, row in dataset.iterrows()
                  if '0' not in row['pola'].split() and '1' in row['pola'].split())
        pos = len(dataset) - neg - neu

        print(f"\n{name}情感分布:")
        print(f"Negative: {neg} 条 ({neg/len(dataset):.2%})")
        print(f"Neutral: {neu} 条 ({neu/len(dataset):.2%})")
        print(f"Positive: {pos} 条 ({pos/len(dataset):.2%})")


if __name__ == '__main__':

    # format_sample([
    #     './input/origin/camera/camera.atepc.train.dat',
    #     './input/origin/car/car.atepc.train.dat',
    #     './input/origin/notebook/notebook.atepc.train.dat',
    #     './input/origin/phone/phone.atepc.train.dat',
    # ], TRAIN_FILE_PATH)

    # format_sample([
    #     './input/origin/camera/camera.atepc.test.dat',
    #     './input/origin/car/car.atepc.test.dat',
    #     './input/origin/notebook/notebook.atepc.test.dat',
    #     './input/origin/phone/phone.atepc.test.dat',
    # ], TEST_FILE_PATH)

    # check_label()

    # format_sample([
    #     './input/origin/course/course.atepc.train.dat'
    # ], './output/process/atepc.sample.all.csv')

    check_label_sample()

    # 使用原始随机划分方法
    # split_sample()

    # 使用按情感类别平衡划分方法
    balanced_split_sample()

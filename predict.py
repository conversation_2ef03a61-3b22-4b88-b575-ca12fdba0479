from utils import *
from model import *
from transformers import BertTokenizer

if __name__ == '__main__':
    model = torch.load(
        TEST_MODEL_DIR+'best_f1_model_epoch-7_f1-0.8690.pth', map_location=DEVICE, weights_only=False)

    with torch.no_grad():

        # text = '课程内容很丰富但是老师讲太快了。'
        # text = '视频讲解简单明了，有理有据，但视频被分割成了很多小视频，不便集中学习，另外，想回顾某以短视频内容时，索引不是很方便'
        text = '课程内容总体上不错，第一次开课没经验出现了些剧情也能理解，老师听取意见取消了填空题，最后是我自己的原因期末考试分数偏低只拿到合格我也坦然接受，建议以后在所有考核都截止后再结课， 以及PPT乱码也要修正下'

        tokenizer = BertTokenizer.from_pretrained(BERT_MODEL_NAME)
        tokens = list(text)
        input_ids = tokenizer.encode(tokens)
        mask = [1] * len(input_ids)

        # 实体部分
        input_ids = torch.tensor(input_ids).unsqueeze(0)
        mask = torch.tensor(mask).unsqueeze(0).bool()

        input_ids = input_ids.to(DEVICE)
        mask = mask.to(DEVICE)

        pred_ent_label = model.get_entity(input_ids, mask)

        # 情感分类
        b_ent_pos, b_ent_pola = get_pola(
            model, input_ids[0], mask[0], pred_ent_label[0])

        if not b_ent_pos:
            print('\t', 'no result.')
        else:
            pred_pair = []
            for ent_pos, pola in zip(b_ent_pos, torch.argmax(b_ent_pola, dim=1)):
                aspect = text[ent_pos[0] - 1:ent_pos[-1]]
                pred_pair.append(
                    {'aspect': aspect, 'sentiment': POLA_MAP[pola], 'position': ent_pos})

            print('\t', text)
            print('\t', pred_pair)

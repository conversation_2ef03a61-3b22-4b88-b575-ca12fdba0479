import torch
TRAIN_FILE_PATH = './output/process/atepc.sample.train.csv'
VAL_FILE_PATH = './output/process/atepc.sample.val.csv'  # 新增验证集路径
# TEST_FILE_PATH = './output/process/atepc.sample.test.csv'
TEST_FILE_PATH = './output/process/balanced/atepc.sample.test.csv'

TEST_MODEL_DIR = '/Users/<USER>/EdUHK/IndependentProject/ABSA/output/models/65_dropout-0-cos-6/'
TEST_TENSORBOARD_DIR = TEST_MODEL_DIR + 'test/'

BIO_O_ID = 0
BIO_B_ID = 1
BIO_I_ID = 2
BIO_MAP = {'O': BIO_O_ID, 'B-ASP': BIO_B_ID, 'I-ASP': BIO_I_ID}
ENT_SIZE = 3

# 实体标签映射，用于混淆矩阵可视化
ENT_MAP = ['O', 'B-ASP', 'I-ASP']

POLA_O_ID = -1
POLA_MAP = ['Negative', 'Neutral', 'Positive']
POLA_DIM = 3
# POLA_MAP = ['Negative', 'Positive']
# POLA_DIM = 2

BERT_PAD_ID = 0
# BERT_MODEL_NAME = "/Users/<USER>/dev/huggingface/bert-base-chinese"
BERT_MODEL_NAME = "google-bert/bert-base-chinese"
BERT_DIM = 768
BERT_MAX_LEN = 512  # BERT模型的最大序列长度

SRD = 3  # Semantic-Relative Distance

BATCH_SIZE = 32
EPOCH = 50
# 早停配置
PATIENCE = 3  # 早停耐心值，连续多少个epoch验证集loss没有改善就停止训练

MODEL_DIR = '/Users/<USER>/dev/Part2_Pytorch_Bert_Atepc/output/models/'

# BERT模型配置 - LoRA参数
LORA_ENABLED = True  # 是否启用LoRA微调
LORA_R = 6  # LoRA的秩参数，决定低秩矩阵的维度
LORA_ALPHA = 12  # LoRA的缩放参数，通常设置为r的2倍
LORA_DROPOUT = 0.2  # LoRA的dropout率
# 指定应用LoRA的模块，对于BERT模型，可以是['query', 'key', 'value', 'dense']
# 使用'dense'而非'output'，因为'output'是一个复合模块
LORA_TARGET_MODULES = ['query', 'key', 'value', 'dense']
LORA_BIAS = 'none'  # LoRA偏置类型，可选值：'none', 'all', 'lora_only'

# 学习率配置
# LR = 1e-4  # 默认学习率 (未使用)
LR_ENT = 3e-4  # 实体识别任务学习率
LR_POLA = 1e-4  # 情感分析任务学习率
LR_BERT = 5e-4  # LoRA参数学习率，降低以提高稳定性

# 权重衰减配置 (AdamW优化器)
# WEIGHT_DECAY = 0.01  # 默认权重衰减率 (未使用)
WEIGHT_DECAY_BERT = 0.01  # LoRA参数权重衰减率
WEIGHT_DECAY_ENT = 0.02  # 实体识别任务权重衰减率，降低以减少过拟合
WEIGHT_DECAY_POLA = 0.001  # 情感分析任务权重衰减率，降低以减少过拟合

# 学习率调度器配置
LR_SCHEDULER_ENABLED = True  # 是否启用学习率调度器
# 对于余弦退火调度器，该设置实际上没有影响，因为我们共享同一个调度器实例
LR_SCHEDULER_SEPARATE = False  # 对于余弦退火，建议设置为False以简化代码
# 学习率调度器类型，可选值: 'plateau' (高原调度器) 或 'cosine' (余弦退火)
LR_SCHEDULER_TYPE = 'cosine'

# 通用学习率调度器配置
# ReduceLROnPlateau调度器配置
LR_FACTOR = 0.5  # 学习率衰减因子
LR_PATIENCE = 3  # 学习率调整耐心值，连续多少个epoch验证集loss没有改善就调整学习率
LR_THRESHOLD = 0.01  # 判断验证集loss是否改善的阈值
LR_MIN = 1e-6  # 学习率下限
LR_COOLDOWN = 1  # 学习率调整后的冷却期(epoch数)
LR_VERBOSE = True  # 是否打印学习率调整信息

# 余弦退火调度器配置
COSINE_T_MAX = 15  # 余弦周期的半周期，单位为epoch，增加以获得更平滑的学习率变化
COSINE_ETA_MIN = 5e-6  # 最小学习率
COSINE_LAST_EPOCH = -1  # 上次运行的最后一个epoch，-1表示从头开始

# 实体识别任务学习率调度器配置
LR_ENT_FACTOR = 0.5  # 实体识别学习率衰减因子
LR_ENT_PATIENCE = 3  # 实体识别学习率调整耐心值
LR_ENT_THRESHOLD = 0.05  # 实体识别判断验证集loss是否改善的阈值
LR_ENT_MIN = 1e-6  # 实体识别学习率下限

# 情感分析任务学习率调度器配置
LR_POLA_FACTOR = 0.5  # 情感分析学习率衰减因子
LR_POLA_PATIENCE = 3  # 情感分析学习率调整耐心值
LR_POLA_THRESHOLD = 0.005  # 情感分析判断验证集loss是否改善的阈值
LR_POLA_MIN = 1e-6  # 情感分析学习率下限

# 任务权重配置 - 用于总体Loss和总体F1的计算
TASK_WEIGHT_ENT = 0.5  # 实体识别任务权重
TASK_WEIGHT_POLA = 0.5  # 情感分析任务权重

# CRF类别权重配置
CRF_CLASS_WEIGHTS_ENABLED = True  # 是否启用CRF类别权重
CRF_CLASS_WEIGHTS = {  # 各类别的权重
    BIO_O_ID: 0.5,     # O标签权重
    BIO_B_ID: 2.0,     # B-ASP标签权重
    BIO_I_ID: 2.0      # I-ASP标签权重
}

# 情感分析类别权重配置
POLA_CLASS_WEIGHTS_ENABLED = True  # 是否启用情感分析类别权重
POLA_CLASS_WEIGHTS = {  # 各类别的权重
    0: 1.0,            # Negative类别权重
    1: 1.0,            # Neutral类别权重
    2: 1.0             # Positive类别权重
}

# Dropout配置 - 简化后的版本
DROPOUT_RATE = 0.2  # 统一的dropout率
DROPOUT_TASK = 0.3  # 任务特定层的dropout率

# Evaluation output paths
CONFUSION_MATRIX_PATH = MODEL_DIR + 'confusion_matrix.png'
CONFUSION_MATRIX_NORM_PATH = MODEL_DIR + 'confusion_matrix_normalized.png'

# TensorBoard配置
TENSORBOARD_DIR = MODEL_DIR

DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

EPS = 1e-10
LCF = 'fusion'  # cdw cdm fusion

# 特征处理配置
# 可选值: 'none' (不使用额外处理), 'attention' (使用BertAttention), 'linear' (使用简单线性层)
FEATURE_PROCESSOR = 'attention'

# 线性特征处理器配置 (当FEATURE_PROCESSOR='linear'时使用)
LINEAR_HIDDEN_SIZE = 384  # 线性层隐藏层大小，通常设置为BERT_DIM的一半

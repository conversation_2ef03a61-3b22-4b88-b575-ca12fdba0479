from utils import *
import torch.utils.data as data
from model import Model
import os
from torch.utils.tensorboard import SummaryWriter


def test(model, loader, writer=None):
    """测试模型并返回所有评估指标"""
    model.eval()
    correct_cnt = pred_cnt = gold_cnt = 0
    total_loss = 0

    # 用于计算情感分析评估指标
    all_true_labels = []
    all_pred_labels = []

    # 用于实体识别混淆矩阵
    all_true_ent_labels = []
    all_pred_ent_labels = []

    with torch.no_grad():
        for b, batch in enumerate(loader):
            input_ids, mask, ent_label, ent_cdm, ent_cdw, pola_label, pairs = batch

            input_ids = input_ids.to(DEVICE)
            mask = mask.to(DEVICE)
            ent_label = ent_label.to(DEVICE)
            ent_cdm = ent_cdm.to(DEVICE)
            ent_cdw = ent_cdw.to(DEVICE)
            pola_label = pola_label.to(DEVICE)

            # 实体部分
            pred_ent_label = model.get_entity(input_ids, mask)

            # 极性部分
            pred_pola = model.get_pola(input_ids, mask, ent_cdm, ent_cdw)
            pred_pola_labels = torch.argmax(pred_pola, dim=1).cpu().numpy()

            # 收集情感分析真实标签和预测标签
            all_true_labels.extend(pola_label.cpu().numpy())
            all_pred_labels.extend(pred_pola_labels)

            # 收集实体识别预测结果
            # 将CRF预测结果和真实标签展平，只保留有效部分（非padding）
            for i, (pred, true, m) in enumerate(zip(pred_ent_label, ent_label, mask)):
                valid_len = m.sum().item()  # 有效长度（非padding部分）
                all_pred_ent_labels.extend(pred[:valid_len])
                all_true_ent_labels.extend(true[:valid_len].cpu().numpy())

            # 损失计算
            loss = model.loss_fn(input_ids, ent_label,
                                 mask, pred_pola, pola_label)
            total_loss += loss.item()

            if b % 20 == 0:
                print('>> batch:', b, 'loss:', loss.item())

            # 计算实体和情感的联合评估指标
            for i in range(len(input_ids)):
                gold_cnt += len(pairs[i])
                b_ent_pos, b_ent_pola = get_pola(
                    model, input_ids[i], mask[i], pred_ent_label[i])
                if not b_ent_pos:
                    continue

                pred_pair = []
                cnt = 0
                for ent, pola in zip(b_ent_pos, torch.argmax(b_ent_pola, dim=1)):
                    pair_item = (ent, pola.item())
                    pred_pair.append(pair_item)
                    if pair_item in pairs[i]:
                        cnt += 1

                correct_cnt += cnt
                pred_cnt += len(pred_pair)

    # 计算平均损失
    avg_loss = total_loss / len(loader)

    # 计算实体和情感的联合评估指标
    joint_precision = round(correct_cnt / (pred_cnt + EPS), 3)
    joint_recall = round(correct_cnt / (gold_cnt + EPS), 3)
    joint_f1 = round(2 / (1 / (joint_precision + EPS) +
                     1 / (joint_recall + EPS)), 3)

    # 使用标签列表，确保包含所有类别
    labels = list(range(POLA_DIM))  # [0, 1, 2] 对应 Negative, Neutral, Positive

    # 计算情感分析评估指标
    sentiment_metrics = calculate_metrics(
        all_true_labels, all_pred_labels, labels=labels, metric_type='sentiment')

    # 计算实体识别评估指标
    entity_metrics = calculate_metrics(
        all_true_ent_labels, all_pred_ent_labels, labels=[0, 1, 2], metric_type='entity')

    # 计算总体Macro F1
    overall_f1 = calculate_overall_f1(entity_metrics, sentiment_metrics)

    # 计算情感分析混淆矩阵
    confusion_matrix = None
    try:
        from sklearn.metrics import confusion_matrix as sk_confusion_matrix
        confusion_matrix = sk_confusion_matrix(
            all_true_labels, all_pred_labels, labels=labels)
    except ImportError:
        print("\n警告: 无法导入 sklearn.metrics.confusion_matrix，情感分析混淆矩阵将不可用")

    # 计算实体识别混淆矩阵
    entity_confusion_matrix = None
    try:
        if 'sk_confusion_matrix' in locals():
            entity_confusion_matrix = sk_confusion_matrix(
                all_true_ent_labels, all_pred_ent_labels, labels=[0, 1, 2])
    except Exception as e:
        print(f"\n警告: 无法计算实体识别混淆矩阵: {str(e)}")

    # 合并所有指标
    result = {
        'loss': avg_loss,
        'joint_precision': joint_precision,
        'joint_recall': joint_recall,
        'joint_f1': joint_f1,
        'overall_f1': overall_f1,  # 总体Macro F1
        'correct_cnt': correct_cnt,
        'pred_cnt': pred_cnt,
        'gold_cnt': gold_cnt,
        'confusion_matrix': confusion_matrix.tolist() if confusion_matrix is not None else None,
        'entity_confusion_matrix': entity_confusion_matrix.tolist() if entity_confusion_matrix is not None else None,
        'all_true_labels': all_true_labels,
        'all_pred_labels': all_pred_labels,
        'all_true_ent_labels': all_true_ent_labels,
        'all_pred_ent_labels': all_pred_ent_labels
    }

    # 将计算的指标添加到结果中
    result.update(sentiment_metrics)
    result.update(entity_metrics)

    # 记录测试指标到TensorBoard
    if writer is not None:
        # 记录全局指标
        writer.add_scalar('test/loss', result['loss'], 0)
        writer.add_scalar('test/accuracy', result['accuracy'], 0)
        writer.add_scalar('test/overall_f1',
                          result['overall_f1'], 0)  # 总体Macro F1
        writer.add_scalar('test/entity_macro_f1',
                          result['entity_macro_f1'], 0)  # 实体识别Macro F1
        writer.add_scalar('test/sentiment_macro_f1',
                          result['sentiment_macro_f1'], 0)  # 情感分析Macro F1
        writer.add_scalar('test/macro_f1', result['macro_f1'], 0)  # 保留原来的指标名称
        writer.add_scalar('test/joint_f1', result['joint_f1'], 0)

        # 记录情感分析混淆矩阵
        if confusion_matrix is not None:
            fig = plot_confusion_matrix_for_tensorboard(
                confusion_matrix, class_names=POLA_MAP)
            writer.add_figure('test/sentiment_confusion_matrix', fig, 0)

            # 归一化的情感分析混淆矩阵
            cm_norm = confusion_matrix.astype(
                'float') / confusion_matrix.sum(axis=1)[:, np.newaxis]
            fig_norm = plot_confusion_matrix_for_tensorboard(
                cm_norm, class_names=POLA_MAP)
            writer.add_figure(
                'test/sentiment_confusion_matrix_normalized', fig_norm, 0)

        # 记录实体识别混淆矩阵
        if entity_confusion_matrix is not None:
            ent_fig = plot_confusion_matrix_for_tensorboard(
                entity_confusion_matrix, class_names=ENT_MAP)
            writer.add_figure('test/entity_confusion_matrix', ent_fig, 0)

            # 归一化的实体识别混淆矩阵
            ent_cm_norm = entity_confusion_matrix.astype(
                'float') / entity_confusion_matrix.sum(axis=1)[:, np.newaxis]
            ent_fig_norm = plot_confusion_matrix_for_tensorboard(
                ent_cm_norm, class_names=ENT_MAP)
            writer.add_figure(
                'test/entity_confusion_matrix_normalized', ent_fig_norm, 0)

    return result


def print_test_metrics(test_metrics):
    """Print test evaluation metrics"""
    print('\n' + '='*50)
    print('\n\t\tTEST EVALUATION METRICS\n')
    print('='*50)

    # 1. Global Metrics
    print('\n1. Global Metrics:')
    print(f'\tLoss: {test_metrics["loss"]:.3f}')
    print(f'\tAccuracy: {test_metrics["accuracy"]}')

    print('\n\tOverall Macro F1 Score:')
    print(f'\t\tOverall F1: {test_metrics["overall_f1"]}')

    print('\n\tEntity Recognition Macro Metrics:')
    print(f'\t\tPrecision: {test_metrics["entity_macro_precision"]}')
    print(f'\t\tRecall: {test_metrics["entity_macro_recall"]}')
    print(f'\t\tF1 Score: {test_metrics["entity_macro_f1"]}')

    print('\n\tSentiment Analysis Macro Metrics:')
    print(f'\t\tPrecision: {test_metrics["macro_precision"]}')
    print(f'\t\tRecall: {test_metrics["macro_recall"]}')
    print(f'\t\tF1 Score: {test_metrics["macro_f1"]}')

    print('\n\tMicro-Average Metrics:')
    print(f'\t\tPrecision: {test_metrics["micro_precision"]}')
    print(f'\t\tRecall: {test_metrics["micro_recall"]}')
    print(f'\t\tF1 Score: {test_metrics["micro_f1"]}')

    print('\n\tJoint Metrics:')
    print(f'\t\tPrecision: {test_metrics["joint_precision"]}')
    print(f'\t\tRecall: {test_metrics["joint_recall"]}')
    print(f'\t\tF1 Score: {test_metrics["joint_f1"]}')
    print(f'\t\tCorrect Count: {test_metrics["correct_cnt"]}')
    print(f'\t\tPredicted Count: {test_metrics["pred_cnt"]}')
    print(f'\t\tGold Count: {test_metrics["gold_cnt"]}')

    # 2. Class-level Metrics
    print('\n2. Class-level Metrics:')
    labels = test_metrics['labels']
    class_precision = test_metrics['class_precision']
    class_recall = test_metrics['class_recall']
    class_f1 = test_metrics['class_f1']
    class_support = test_metrics['class_support']

    print('\n\tClass\tPrecision\tRecall\tF1 Score\tSamples')
    print('\t' + '-'*60)

    for i, label in enumerate(labels):
        label_name = POLA_MAP[label] if label < len(
            POLA_MAP) else f"Class {label}"
        print(
            f'\t{label_name}\t{class_precision[i]:.3f}\t{class_recall[i]:.3f}\t{class_f1[i]:.3f}\t{class_support[i]}')

    # 3. Sentiment Confusion Matrix
    print('\n3. Sentiment Confusion Matrix:')
    if test_metrics['confusion_matrix'] is not None:
        cm = test_metrics['confusion_matrix']
        print('\n\tPredicted Labels')
        print('\t' + ' '.join([f'{POLA_MAP[i]:>10}' for i in labels]))
        print('\t' + '-'*40)

        for i, row in enumerate(cm):
            label_name = POLA_MAP[labels[i]] if labels[i] < len(
                POLA_MAP) else f"Class {labels[i]}"
            print(f'{label_name:>10}\t' +
                  ' '.join([f'{val:>10}' for val in row]))
    else:
        print('\tSentiment confusion matrix not available')

    # 4. Entity Confusion Matrix
    print('\n4. Entity Confusion Matrix:')
    if test_metrics['entity_confusion_matrix'] is not None:
        ent_cm = test_metrics['entity_confusion_matrix']
        ent_labels = [0, 1, 2]  # O, B-ASP, I-ASP
        print('\n\tPredicted Labels')
        print('\t' + ' '.join([f'{ENT_MAP[i]:>10}' for i in ent_labels]))
        print('\t' + '-'*40)

        for i, row in enumerate(ent_cm):
            label_name = ENT_MAP[ent_labels[i]] if ent_labels[i] < len(
                ENT_MAP) else f"Class {ent_labels[i]}"
            print(f'{label_name:>10}\t' +
                  ' '.join([f'{val:>10}' for val in row]))
    else:
        print('\tEntity confusion matrix not available')

    # Visualize confusion matrices
    try:
        # Sentiment confusion matrix
        if test_metrics['confusion_matrix'] is not None:
            # Standard sentiment confusion matrix
            plot_confusion_matrix(
                test_metrics['all_true_labels'],
                test_metrics['all_pred_labels'],
                labels=labels,
                title='Sentiment Classification Confusion Matrix',
                save_path=CONFUSION_MATRIX_PATH
            )

            # Normalized sentiment confusion matrix
            plot_confusion_matrix(
                test_metrics['all_true_labels'],
                test_metrics['all_pred_labels'],
                labels=labels,
                normalize=True,
                title='Sentiment Classification Confusion Matrix (Normalized)',
                save_path=CONFUSION_MATRIX_NORM_PATH
            )

        # Entity confusion matrix
        if test_metrics['entity_confusion_matrix'] is not None:
            # Standard entity confusion matrix
            plot_confusion_matrix(
                test_metrics['all_true_ent_labels'],
                test_metrics['all_pred_ent_labels'],
                labels=[0, 1, 2],
                title='Entity Recognition Confusion Matrix',
                save_path=TEST_TENSORBOARD_DIR + 'entity_confusion_matrix.png'
            )

            # Normalized entity confusion matrix
            plot_confusion_matrix(
                test_metrics['all_true_ent_labels'],
                test_metrics['all_pred_ent_labels'],
                labels=[0, 1, 2],
                normalize=True,
                title='Entity Recognition Confusion Matrix (Normalized)',
                save_path=TEST_TENSORBOARD_DIR + 'entity_confusion_matrix_normalized.png'
            )
    except Exception as e:
        print(f"\nWarning: Could not visualize confusion matrices: {e}")


if __name__ == '__main__':
    try:
        # 创建TensorBoard日志目录
        os.makedirs(TEST_TENSORBOARD_DIR, exist_ok=True)
        writer = SummaryWriter(TEST_TENSORBOARD_DIR)
        print(f"TensorBoard日志已初始化，路径: {TEST_TENSORBOARD_DIR}")
        print(
            f"运行 'tensorboard --logdir={TEST_TENSORBOARD_DIR}' 来启动TensorBoard")

        model = torch.load(TEST_MODEL_DIR + 'best_f1_model_epoch-7_f1-0.8690.pth', map_location=DEVICE)

        dataset = Dataset('test')
        loader = data.DataLoader(
            dataset, batch_size=BATCH_SIZE, shuffle=False, collate_fn=dataset.collate_fn)

        # 测试模型并获取所有指标
        test_metrics = test(model, loader, writer)

        # 打印测试集指标
        print_test_metrics(test_metrics)

        # 添加嵌入向量可视化
        try:
            # 获取模型的嵌入向量
            embeddings = model.bert.embeddings.word_embeddings.weight.data
            metadata = [f"Token_{i}" for i in range(embeddings.size(0))]
            writer.add_embedding(
                embeddings, metadata=metadata, tag='token_embeddings')
            print("Token嵌入向量已添加到TensorBoard")
        except Exception as e:
            print(f"添加嵌入向量失败: {str(e)}")

    except Exception as e:
        print(f"测试过程发生错误: {str(e)}")
        raise
    finally:
        # 清理资源
        if 'writer' in locals():
            writer.close()
            print("TensorBoard写入器已关闭")
        if 'model' in locals():
            del model
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

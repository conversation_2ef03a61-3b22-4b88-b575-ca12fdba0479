text,bio,pola
讲 的 通 俗 易 懂 非 常 适 合 入 门 课 程 来 学 习,O O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
让 我 学 习 到 了 很 多 语 言 程 序 设 计 的 知 识 懂 的 了 怎 么 去 操 作 很 好,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1
内 容 深 入 浅 出 ， 注 重 思 维 锻 炼 和 实 际 运 用 ， 学 习 中 深 有 教 益 ！,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
作 为 编 程 小 白 ， 听 了 嵩 老 师 的 课 后 ， 深 深 的 爱 上 了 P y t h o n ， 接 下 来 将 继 续 学 习 嵩 老 师 其 他 课 程 ， 并 且 自 学 。,O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1
对 于 编 程 小 白 十 分 友 好 ， 相 当 不 错,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O,-1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 很 详 细 。 不 过 对 外 专 业 小 白 来 说 ， 还 是 很 多 不 懂 的 。 能 否 再 加 一 些 程 序 语 言 表 达 、 逻 辑 方 面 的 扫 盲 课 ， 比 如 不 同 子 模 块 的 顺 序 问 题 ， 什 么 样 的 问 题 可 以 转 换 为 什 么 样 的 函 数 等 等 。 谢 谢,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
之 前 有 基 础 ， 不 过 听 了 嵩 老 师 的 课 ， 收 获 确 实 是 非 常 多 ， 老 师 讲 解 和 自 己 看 书 学 习 完 全 是 两 码 事 。 许 多 概 念 更 加 清 晰 我 明 了 了 ， 动 手 能 力 也 比 之 前 强 的 多 ， 谢 谢 老 师 提 供 这 么 优 秀 的 课 程 ， 嵩 老 师 其 他 的 课 程 ， 我 也 会 逐 个 学 习 的 。,O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 循 序 渐 进 ， 由 浅 入 深 ， 讲 解 详 实,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
挺 不 错 老 师 很 讲 细 节 很 用 心 适 合 新 手,O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP,-1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 2 2
这 门 课 让 我 快 速 入 门 P Y T H O N !,O O B-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1
从 基 础 学 起 ， 一 步 步 掌 握 ， 很 n i c e,O B-ASP I-ASP O O O O O O O O O O O O O O,-1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 简 单 易 懂 ， 是 小 白 们 的 不 错 选 择,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
课 程 设 计 ， 组 织 的 好 ， 特 别 是 开 头 说 明 所 学 和 结 尾 总 结 要 点 及 记 忆 内 容 。 极 其 节 省 学 习 和 做 笔 记 时 间 。,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP B-ASP I-ASP O,2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 2 2 -1 -1 2 2 -1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 2 2 -1
老 师 比 较 温 柔 ， 知 识 点 娓 娓 道 来 ， 蛮 享 受 的 。,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 是 我 看 的 第 一 个 有 关 P y t h o n 的 视 频 课 程 ， 老 师 们 讲 的 很 清 楚 ， 给 了 我 很 大 帮 助 。,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
超 出 了 预 期 ， 希 望 能 f i l l t h e k n o w l e d g e g a p,O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2
很 好 的 一 门 课 程 ， 我 这 种 学 过 C 语 言 的 人 ， 学 了 这 门 课 更 加 的 懂 得 了 计 算 思 维 ， 有 利 于 编 程,O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2
1 、 老 师 讲 课 讲 的 太 快 了 2 、 老 师 讲 的 再 有 点 激 情 就 好 了,O O B-ASP I-ASP O B-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O,-1 -1 1 1 -1 0 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
非 常 容 易 理 解 ， 会 在 基 础 上 继 续 追 随 嵩 天 老 师 继 续 学 习 P y t h o n 。,O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1
这 老 师 讲 解 很 细 致 ， 语 速 均 匀 。 课 程 制 定 由 简 单 到 复 杂 ， 容 易 进 入 学 习 状 态 。,O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 1 1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
目 前 为 止 ， 小 白 的 我 还 是 比 较 有 兴 趣 和 信 心 看 下 去 的 ， 希 望 老 师 能 带 领 我 突 破 这 个 领 域 ！,O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
课 程 内 容 条 理 清 晰 ， 深 入 浅 出 ， 干 货 满 满 ， 嵩 老 师 讲 得 超 赞 。,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1
课 程 不 错 ， 既 能 从 基 础 引 进 门 ， 又 不 过 于 基 础 ， 能 穿 插 一 些 实 例 讲 解 ， 增 加 趣 味 性 和 实 用 性 ！,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 -1 2 2 2 -1
作 为 基 础 入 门 课 程 条 理 很 清 晰 ， 授 课 方 式 也 是 循 序 渐 进 ， 理 论 与 实 操 结 合 ， 显 得 不 那 么 无 趣 ， 也 更 容 易 理 解 。,O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嗯 ， 讲 得 深 入 浅 出 ， 让 我 这 个 小 白 听 的 很 明 白 ， 只 不 过 有 些 库 安 装 有 点 过 于 简 单 了 ， 出 错 了 ， 只 能 自 己 在 网 上 慢 慢 找 。,O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 的 思 路 非 常 人 性 化 ， 对 于 初 学 者 非 常 友 好 ， 内 容 详 细 清 楚 明 了 ， 逻 辑 通 顺 ， 使 人 眼 前 一 亮 ， 思 维 发 生 了 前 所 未 有 的 升 华 ， 感 谢 感 谢 ！,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
入 门 优 选 ， 老 师 讲 的 很 容 易 理 解 。 （ 我 是 初 学 者 ）,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1
高 屋 建 瓴 ， 对 于 p y t h o n 入 门 的 小 白 十 分 适 合 。 实 例 也 很 有 趣,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
授 课 技 巧 很 精 细 ， 不 深 不 浅 刚 刚 好 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
希 望 老 师 在 课 堂 上 可 以 提 供 更 多 的 实 例 说 明 ， 谢 谢 。,O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
实 例 很 吸 引 人 ， 不 管 是 对 工 作 还 是 学 习 都 有 很 强 的 实 用 性,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 2
v e r y v e r y g o o d !,O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 适 合 入 门 ， 课 程 设 计 的 也 比 较 人 性 化 ， 课 时 适 当 ， 不 愧 是 国 家 精 品 课 程,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2
很 好 的 p y t h o n 入 门 课 ！ 有 实 例 讲 解 ， 能 更 加 灵 活 运 用 ！ 谢 谢 老 师 !,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 1 1 1 1 1 1 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 得 非 常 好 ！ 记 了 几 十 页 的 笔 记 ， 算 入 门 了 。 能 编 一 些 十 几 行 的 小 程 序 了 哈 哈,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
老 师 讲 的 很 好 ， 是 那 种 给 人 启 发 性 的 讲 解 ， 就 是 顺 着 老 师 思 路 自 己 能 写 代 码 的 ， 很 棒,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1
学 了 这 么 久 ， 说 说 我 的 学 习 感 受 ！,O O O O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1
除 了 分 P 分 的 让 人 难 受 ， 内 容 的 确 是 满 满 的 干 货 。,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O,-1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
对 于 我 这 种 0 基 础 文 科 生 来 说 ， 讲 课 速 度 还 是 稍 微 有 点 快 ， 而 且 各 种 代 码 一 时 半 会 记 不 全 ， 需 要 反 复 查 询 才 可 以,O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 专 门 的 编 程 平 台 ， 这 一 点 挺 厉 害 的,O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 语 言 简 明 、 重 点 清 晰 。 很 棒 ！,B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 课 通 熟 易 懂 ， 非 常 好 理 解 ！,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 还 是 有 点 难 度 的 ， 作 为 小 白 ， 大 致 能 听 懂 ， 但 自 己 编 程 难 度 太 大 。,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1
嵩 天 老 师 讲 得 很 不 错 条 理 清 晰 通 俗 易 懂,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 希 望 自 己 能 坚 持 下 去 ， 这 个 五 星 同 时 给 的 也 是 自 己,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
只 能 说 很 强 ， 和 其 他 老 师 的 长 篇 大 论 ， 不 一 样 。 这 个 带 来 更 多 的 是 思 考 。,O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 深 入 浅 出 很 容 易 理 解 ， 如 果 有 基 础 的 就 更 容 易 看 懂 了 。,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
p y t h o n 1 2 3 的 编 程 练 习 ， 体 验 不 好,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O,1 1 1 1 1 1 1 1 1 -1 1 1 1 1 -1 0 0 -1 -1
内 容 丰 富 ， 充 实 ， 老 师 讲 的 透 彻 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
牙 膏 厂 ， 视 频 分 的 太 短 了 ， 时 间 都 浪 费 在 等 待 中 了 。,B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O,0 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 的 角 度 与 大 部 分 网 络 上 的 角 度 不 同 ， 通 过 实 例 的 整 体 分 析 以 及 部 分 化 精 讲 ， 会 更 好 的 去 理 解,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O,2 2 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
章 节 逻 辑 清 晰 明 确 ， 老 师 讲 课 清 楚 易 懂 ， 是 很 好 的 基 础 课 程 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
嵩 天 老 师 讲 的 确 实 不 错 ， p y t h o n 小 白 听 得 一 点 也 不 吃 力,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 觉 很 棒 ！ 很 有 趣 ！ 老 师 讲 的 很 好,B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP O O O,2 2 -1 -1 -1 -1 2 2 -1 1 1 2 -1 -1 -1
课 程 内 容 丰 富 充 实 ， 通 过 嵩 山 老 师 深 入 浅 出 ， 重 难 点 有 的 放 矢 的 讲 解 ， 课 程 好 掌 握 易 理 解 ， 真 棒 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 非 常 好 ， 老 师 讲 的 深 入 浅 出,O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
课 程 适 合 初 学 者 ， 通 俗 易 懂 ， 讲 的 非 常 好 ！,B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O B-ASP O O O O O,2 2 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1
"老 师 讲 课 逻 辑 清 晰 , 非 常 易 懂",B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O,1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 组 织 合 理 ， 利 用 案 例 分 解 知 识 点 ， 整 体 和 局 部 都 可 以 顾 及 老 师 讲 解 清 楚 明 白 ， 理 解 容 易,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 对 于 0 基 础 来 说 真 的 很 友 好 ( * ^ _ ^ * ),B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
真 心 很 赞 啊 跟 这 门 课 程 是 因 为 学 校 开 了 P Y T H O N 叫 跟 着 听 后 来 因 为 太 忙 了 就 一 段 时 间 么 有 听 现 在 在 赶 进 度 觉 得 讲 的 真 的 是 挺 清 楚 的 哈,O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 的 特 别 详 细 ， 对 这 门 课 产 生 了 浓 厚 的 兴 趣,B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP O O O O O O B-ASP I-ASP,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 2 2
非 常 好 好 ， 字 数 不 够 ， 我 再 说 一 遍 非 常 好,O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
挺 难 的 ， 但 还 想 学 。 老 师 讲 的 不 错 ， 但 有 些 地 方 不 细 ， 一 点 基 础 没 有 的 人 会 有 不 少 地 方 听 不 懂,O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
不 错 ， 内 容 通 俗 易 懂 ， 课 程 设 计 合 理,O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1
嵩 老 师 讲 得 非 常 好 ， 生 动 活 泼 ， 又 确 保 严 谨 。,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 不 错 的 课 程 ， 我 是 零 基 础 小 白 ， 课 程 深 入 浅 出 ， 有 趣 易 懂 ， 老 师 讲 得 很 细 ， 生 怕 大 家 听 不 懂 ， 而 且 每 节 课 都 会 不 经 意 间 插 入 一 点 点 后 续 需 要 学 的 重 点 知 识 ， 让 学 者 在 学 习 后 续 知 识 时 很 轻 松 ， 线 上 题 库 也 很 不 错 的 ， 根 据 我 的 个 人 经 验 ， 搭 配 课 程 配 套 的 教 材 《 p y t h o n 语 言 程 序 设 计 基 础 》 （ 第 二 版 ） 后 学 习 效 果 更 好 ， 一 定 要 选 高 等 教 育 出 版 社 ， 作 者 嵩 天 。 期 待 课 程 的 继 续 学 习 。,O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
思 路 非 常 清 晰 ， 是 我 见 过 讲 的 最 清 楚 的 老 师 。,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 理 论 与 实 践 结 合 ， 应 用 与 拓 展 夯 实 。,B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O,2 2 2 2 -1 2 2 -1 -1 -1 1 1 -1 1 1 2 2 -1
内 容 讲 解 思 路 清 晰 ， 实 例 选 择 具 有 实 践 性,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O,2 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
够 全 面 ， 够 基 础 ， 讲 课 容 易 理 解 ， 学 到 了 很 多,O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O,-1 2 2 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
真 的 是 学 到 了 好 多 东 西 ， 老 手 讲 的 很 容 易 听 懂 。,O O O O O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 2 -1 -1 -1 -1 -1 -1 -1
蛮 好 的 ， 每 个 视 频 不 是 很 长 ， 这 样 就 给 人 看 下 去 的 动 力,O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
非 常 好 非 常 棒 ， 还 想 继 续 听 老 师 的 进 阶 版 课 程,O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2
老 师 的 讲 课 很 精 彩 ， 让 我 受 益 良 多,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 有 价 值 的 课 程 ， 很 适 合 初 学 者,O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1
这 课 程 知 识 以 一 个 个 小 的 知 识 点 呈 现 方 式 ， 新 手 学 习 不 累 ， 这 些 小 小 的 知 识 点 又 以 非 常 严 密 得 逻 辑 组 织 在 一 起 ， 很 棒 的 p y t h o n 入 门 课 程,O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP,-1 2 2 1 1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 2
比 较 满 意 ， 希 望 能 增 加 和 老 师 的 互 动,O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2
嵩 天 老 师 好 可 爱 ！ 对 于 P y t h o n 小 白 来 说 ， 嵩 天 老 师 讲 得 非 常 详 细 ， 教 授 方 法 也 非 常 有 效 ， 让 我 对 P y t h o n 的 兴 趣 和 热 情 只 增 不 减,B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 2 2 -1 -1 -1 -1
有 的 老 师 说 不 清 哪 里 好 ， 但 就 是 喜 欢 他 的 气 质 ， 再 喜 欢 他 的 课 程 ~,O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 内 容 非 常 清 晰 条 理 ， 语 言 简 洁 ， 非 常 感 谢 ！ 也 有 一 些 感 觉 ： 也 许 因 为 内 容 太 过 基 础 ， 有 些 概 念 细 节 性 的 东 西 ， 老 师 表 述 并 不 是 特 别 注 意 。 比 如 在 讲 数 据 类 型 时 谈 到 字 符 串 的 索 引 与 切 片 中 ， < 字 符 串 > [ M ] 表 示 的 应 为 选 取 序 号 为 M 的 字 符 ， 而 不 是 第 M 个 字 符 ； < 字 符 串 > [ M : N ] 表 示 的 应 为 选 取 原 字 符 串 中 序 号 M 到 N - 1 的 子 字 符 串 ， 而 不 是 选 取 第 M 个 字 符 到 第 N - 1 个 字 符 所 形 成 的 子 串,B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 -1 1 1 -1 1 1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
学 习 M o o c 课 一 来 最 有 意 思 的 一 门 课 ， 与 大 量 的 示 例 相 结 合 ， 很 生 动 有 趣 ； 先 易 后 难 ， 符 合 学 习 规 律 。 棒 棒 棒,O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
收 获 满 满 ， 老 师 讲 课 很 棒 ， 学 习 效 果 好 。,B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 2 2 2 2 -1 -1
受 益 匪 浅 ， 知 识 点 很 多 很 密 ， 需 要 花 时 间 消 化,O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 老 师 讲 的 也 很 容 易 理 解,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
作 为 入 门 课 ， 课 程 深 入 浅 出 ， 非 常 好 。 准 备 继 续 学 习 数 据 分 析 课 程 。 加 油 ！,O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
讲 得 浅 显 易 懂 ， 让 我 这 个 门 外 汉 学 习 的 兴 趣 越 来 越 大 。,B-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
课 程 讲 解 系 统 ， 注 重 基 础 入 门 ， 同 时 也 扩 展 知 识 视 野 ， 非 常 好 的 一 门 课 程 。,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O,2 2 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 得 很 好 ， 绿 幕 的 形 式 惊 艳 到 我 了,B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1
讲 授 得 很 有 趣 和 细 致 ， 也 比 较 容 易 懂 。,B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 喜 欢 嵩 天 老 师 的 教 学 方 式 ， 让 我 听 得 入 迷 ， 为 之 有 趣 ， 喜 爱 这 门 课 程 ， 感 谢 这 个 平 台,O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP,-1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2
课 程 内 容 组 织 遵 循 系 统 性 原 则 ， 由 浅 入 深 ， 循 序 渐 进 。 授 课 方 式 生 动 有 趣 ， 深 入 浅 出 。 基 本 清 楚 了 p y t h o n 是 做 什 么 的 ， 也 学 会 了 p y t h o n 的 基 本 语 法 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 2 2 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 -1
e x c i t i n g f a n t a s t i c a n d h a p p y a n d e x p e r i e n c i n g a n d e x p e i r e n c e d,O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 学 完 ， 练 习 题 和 考 试 的 编 程 还 是 不 会,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O,1 1 1 1 -1 -1 -1 0 0 0 -1 0 0 -1 0 0 -1 -1 -1 -1
学 会 了 如 何 用 p y t h o n 编 写 程 序,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 1 1
"讲 得 很 详 细 ， 通 俗 易 懂 , 感 谢 分 享 ！",O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
介 绍 一 下 P y t h o n 编 译 环 境 就 更 好 了 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1
我 觉 得 简 单 易 懂 并 且 是 有 趣 的 ， 以 案 列 来 分 析 介 绍 很 不 错,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
授 课 内 容 很 合 理 学 习 到 了 很 多 东 西,O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
加 油 加 油 ， 如 此 精 品 的 课 程 ， 如 此 细 致 的 教 材,O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2
通 过 课 程 ， 真 正 迈 入 了 编 程 之 门 ， 感 谢 老 师 以 实 例 和 问 题 为 突 破 口 的 授 课 方 式 ， 对 没 有 一 点 编 程 基 础 的 学 子 很 友 好,O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
非 常 好 的 视 频 课 ， 大 众 多 的 同 类 课 中 ， 选 择 此 课 已 学 了 多 遍 了 ， 谢 谢 三 们 老 师 及 团 队 ！,O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1
上 过 南 大 张 莉 老 师 的 P y t h o n 课 程 ， 感 觉 各 有 千 秋 。 不 过 此 门 课 作 为 入 门 真 是 有 点 难 。,O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP O O O O O O O O O O,-1 -1 2 2 1 1 1 1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
寓 教 于 乐 ， 每 次 都 有 感 兴 趣 的 实 践 ， 非 常 吸 引 人 。,O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
讲 得 详 细 ， 很 基 础 ， 没 有 编 程 基 础 的 我 能 跟 得 上 ， 很 受 益 。,B-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O,2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
实 例 与 知 识 点 结 合 ， 不 枯 燥 ， 很 有 趣 味 。,B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 解 细 致 到 位 ， 通 俗 易 懂 ， 内 容 简 要,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
很 棒 的 老 师 ， 深 入 浅 出 ， 把 复 杂 的 知 识 点 讲 解 的 通 俗 易 读 ， 特 别 好 ！,O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 的 教 学 非 常 棒 ， 由 简 入 深 ， 一 直 激 励 和 引 导 我 等 小 白,B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
感 觉 很 好 ， 教 的 不 深 ， 但 是 能 激 发 兴 趣 ， 有 助 于 提 高 编 程 思 维,O O O O O B-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2
"对 编 程 语 法 关 键 字 解 释 太 少 ， 边 例 如 "" { : . 2 f } "" . f o r m a t ( ) 用 法 始 终 没 解 释 ， 只 能 靠 百 度 ， 老 师 挺 好 ， 适 合 有 些 基 础 的 同 学 学 习 。",O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O,-1 0 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1
讲 解 非 常 详 尽 ， 仔 细 。 适 合 我 这 样 的 初 学 者 。,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
只 要 你 耐 心 的 话 ， 老 师 的 课 确 实 十 分 有 效,O O O O O O O O B-ASP I-ASP O B-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 能 听 懂 ， 但 是 实 操 就 有 问 题 ， 还 是 自 己 底 子 太 差 。 老 师 语 速 适 中 ， 语 言 简 练 ， 一 听 即 会 。 老 师 真 心 不 错,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1 1 1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
课 程 循 序 渐 进 ， 通 俗 易 懂 ， 思 路 清 晰,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
老 师 讲 解 清 晰 ， 难 度 合 理 。 适 合 新 手 学 习,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O,1 1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1
"p r i n t ( "" 略 略 略 ~ "" )",O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 重 点 突 出 ， 结 构 合 理 ， 理 论 和 案 例 相 结 合 ， 易 学 易 懂 。,O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
终 于 认 认 真 真 的 完 成 了 该 课 程 的 学 习 ， 着 实 收 益 匪 浅 。 嵩 老 师 的 课 堂 深 入 浅 出 ， 让 我 意 犹 未 尽 。 我 一 直 为 加 油 ， 继 续 努 力 学 习 p y t h o n 1 2 3 的 其 它 课 程 。,O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 2 2 -1
p y t h o n 简 直 太 高 效 了 ， b u t ， 不 太 好 记 啊,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ， 希 望 这 次 可 以 学 完,B-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
该 课 程 设 置 非 常 容 易 懂 ， 例 子 很 清 晰 ！,O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O,-1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 解 的 细 致 入 微 ， 视 频 时 间 控 制 的 蛮 好 的 不 是 上 来 就 是 几 十 分 钟 的 视 频,B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP,1 1 2 2 -1 2 2 2 2 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
授 课 重 点 突 出 ， 例 题 有 趣 新 颖 ， 不 仅 教 知 识 、 还 讲 技 巧 ， 如 果 想 学 p y t h o n ， 这 门 课 真 的 不 容 错 过 ！,O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O,-1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1
懂 得 p y t h o n 语 言 的 基 本 编 写 能 力 ， 赞 ！,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1
老 师 教 的 不 错 ， 非 常 清 楚 ， 感 谢 老 师,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 课 很 详 细 ， 也 很 有 逻 辑 。,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
整 体 挺 好 ， 有 些 地 方 还 是 对 于 初 学 者 不 是 很 友 好 。 我 对 编 程 c 语 言 有 一 定 自 学 ， 完 全 能 跟 上 ， 我 爱 人 文 科 生 和 我 一 起 她 就 很 多 问 题 ， 但 是 也 很 好 给 你 解 释 。,O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
很 喜 欢 老 师 的 授 课 风 格 ， 思 维 清 晰 逻 辑 很 棒 ， 能 跟 着 一 步 步 学 下 去 就 很 适 合 我 。,O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O,-1 -1 -1 2 2 -1 2 2 2 2 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
切 入 点 很 好 ， 轻 重 有 度 ， 知 识 点 普 遍 而 又 有 小 惊 喜 ， 老 师 讲 的 也 非 常 棒 。,B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O,2 2 2 -1 -1 -1 2 2 2 2 -1 1 1 1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 段 且 内 容 多 很 适 合 小 白 学 习,B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O,2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 内 容 充 实 有 趣 ， 是 学 习 P Y T H O N 的 非 常 好 的 基 础 课 。,O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 2 -1
讲 真 的 哪 怕 自 学 了 好 久 看 看 老 师 讲 的 也 会 有 收 获 ！,O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1
讲 解 地 很 有 层 次 和 结 构 ， 值 得 推 荐,B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1
授 课 深 入 浅 出 ， 简 单 易 懂 ， 每 个 内 容 最 后 都 有 总 结 ， 课 前 有 复 习 ， 温 故 知 新 ， 非 常 棒 ！,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1
嵩 老 师 的 课 程 导 言 给 了 我 学 习 的 信 心,B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
"从 易 语 言 入 门 学 p y t h o n 的 ， 对 比 B 站 上 的 一 些 教 程 ， 老 师 讲 得 最 通 俗 易 懂 ， 编 程 最 需 要 的 是 打 好 基 础 , 剩 下 的 就 是 多 看 多 写 。",O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 1 1 1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 设 计 得 很 好 深 入 浅 出 是 非 常 好 的 入 门 课 程 很 喜 欢 不 知 道 还 有 没 有 后 续,O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
总 体 来 讲 通 过 实 例 解 析 还 有 研 读 可 以 更 可 触 及 的 分 析 理 解 程 序 究 竟 做 了 些 什 么 ， 不 愧 是 国 家 精 品 ， 很 惊 艳 了,O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
挺 好 ， 简 单 容 易 上 手 ， 讲 解 很 详 细,O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 非 常 专 业 ， 教 法 新 颖 ， 由 浅 入 深 ， 是 小 白 学 习 的 有 力 抓 手 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 今 年 三 十 五 岁 了 ， 但 我 相 信 ， 我 可 以 学 一 门 能 用 二 十 年 的 语 言 。 英 语 / 统 计 学 / 专 业 知 识 ， 我 还 需 要 点 什 么 ， 那 就 是 p y t h o n 。 这 是 一 种 真 正 的 力 量 。 为 了 部 落 ！,O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 真 是 太 棒 了 ， 给 个 强 烈 大 大 的 赞 ~ ~,B-ASP I-ASP O O O O O O O O O O O O O B-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1
理 论 与 实 例 相 结 合 ， 还 是 很 喜 欢 这 样 的 方 式 ， 就 是 上 课 内 容 可 能 存 在 粒 度 不 够 的 可 能 ， 个 别 知 识 点 不 够 细,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O,2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1
这 门 课 程 真 的 非 常 好 ， 不 仅 有 编 程 所 需 的 基 础 知 识 ， 还 有 具 体 的 实 例 运 用 ， 提 升 了 学 习 的 趣 味 性 ， 让 入 门 者 不 觉 得 枯 燥 ， 除 此 之 外 ， 还 有 关 于 计 算 机 科 学 高 屋 建 瓴 的 认 识 ， 不 仅 有 术 ， 更 有 道 ！ ！,O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
作 为 一 个 小 白 ， 能 够 听 明 白 老 师 讲 的 课 程 ， 非 常 棒 。,O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 通 俗 易 懂 ， 课 程 安 排 很 好 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O,1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
老 师 讲 得 通 俗 易 懂 ， 很 系 统 化 ， 适 合 入 门 者 学 习 ！ !,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1
这 个 老 师 上 课 很 好 ， 讲 解 通 俗 易 懂 ， 内 容 丰 富 。 建 议 加 上 面 向 对 象 的 内 容 ， 让 课 程 更 完 整,O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 -1 -1 2 2 -1 -1 -1
老 师 讲 解 清 晰 ， 案 例 丰 富 ， 很 有 收 获 。,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,1 1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 课 非 常 细 致 ， 举 例 通 俗 易 懂 ， 收 获 很 大 。,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1
很 喜 欢 嵩 天 老 师 的 讲 课 ， 让 我 对 P y t h o n 有 了 很 大 的 兴 趣,O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 设 计 得 太 好 了 。 老 师 讲 课 逻 辑 清 晰 ， 思 维 严 谨 ， 整 个 课 程 设 计 系 统 而 且 全 面 。 把 整 整 一 个 p y t h o n 的 体 系 完 整 而 且 清 晰 的 呈 现 了 出 来 ， 而 且 也 提 供 了 很 系 统 的 练 习 机 会 。,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1
讲 的 非 常 好 ， 细 致 并 且 层 层 递 进 ， 有 收 获,O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
学 习 基 础 的 p y t h o n 知 识 ， 方 便 之 后 的 学 习 和 工 作,O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 1 1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2
教 学 方 式 方 法 很 合 理 ， 很 切 合 我 学 习 认 识 P y t h o n 语 言,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2
老 师 授 课 深 入 浅 出 ， 内 容 安 排 合 理,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
y o u h e n g d a d e y i n g c h u,O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 习 这 门 课 程 ， 让 我 有 了 前 所 未 有 的 收 获 ， 谢 谢 老 师 ！ 谢 谢 北 京 理 工 大 学 ！ 虽 然 没 有 机 会 上 北 京 理 工 ， 但 是 可 以 在 网 上 学 到 老 师 的 精 彩 课 程 ， 还 是 非 常 高 兴 ！ 希 望 对 我 现 在 的 工 作 有 所 帮 助 ， 想 学 数 据 分 析 。,O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1
老 师 讲 的 内 容 非 常 清 晰 易 懂 ， 受 益 匪 浅,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 组 织 十 分 合 理 ， 由 浅 入 深 ， 既 能 鼓 励 零 基 础 入 门 ， 又 有 理 解 后 的 提 升 ， 非 常 好 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
本 课 程 非 常 值 得 学 习 ， 嵩 天 老 师 讲 详 细 ， 学 到 了 在 其 它 地 方 没 有 学 习 到 的 内 容,O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,-1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2
还 可 以 ， 就 是 太 。 。 。 小 学 期 考 试 太 。 。 。 糟 糕 了 。 。 。,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 详 细 ， 每 一 个 步 骤 都 能 知 道 为 什 么 这 样 做 ， 同 一 问 题 也 有 不 同 形 式 的 解 决 方 法 ， 很 棒 ！,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
课 程 可 以 初 步 让 我 了 解 P y t h o n ， 终 于 不 是 编 程 小 白 了,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 讲 的 太 棒 了 。 不 愧 是 精 品 课 程 。 无 论 是 从 课 程 的 设 计 上 ， 还 是 讲 解 、 P P T 等 ， 都 非 常 的 完 美 。,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 2 2 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
关 于 P y t h o n 的 学 习 浅 显 易 懂 ， 老 师 讲 课 形 象 生 动 ， 会 让 人 有 一 种 深 入 浅 出 的 感 觉 ， 感 觉 对 自 己 是 有 不 少 帮 助 的 。,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,-1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
课 程 有 点 过 于 基 础 化 ， 并 且 很 多 地 方 的 细 节 处 理 的 不 到 位 。,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O,0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1
课 程 总 体 非 常 好 ， 我 觉 得 在 保 留 字 的 记 忆 方 面 可 以 根 据 课 程 设 计 进 行 归 类 ， 整 理 成 另 一 张 图 ， 而 不 是 按 字 母 排 序 进 行 罗 列 ， 或 许 更 容 易 记 忆 一 些 。 供 参 考 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
是 目 前 在 M O O C 上 上 课 体 验 最 好 的 课 程 ， 感 谢 嵩 天 老 师 和 课 程 团 队 认 真 负 责 地 打 磨 出 这 样 的 好 课 程 。,O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 1 1 1 1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
太 棒 了 ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！,O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 习 了 接 近 一 半 课 程 ， 能 学 会 一 些 编 程 的 基 础 ， 学 与 练 相 结 合 ， 大 大 提 高 了 学 习 成 果 。,O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP O B-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
很 喜 欢 这 门 课 ， 对 于 小 白 来 说 ， 老 师 能 一 步 一 步 的 分 析 代 码 ， 分 析 代 码 是 什 么 规 则 、 语 法 ， 如 何 写 ， 这 对 于 一 个 小 白 来 说 ， 是 最 好 的 教 程 ， 通 过 课 程 的 学 习 能 初 步 知 道 程 序 是 怎 么 来 的 ， 如 何 写 ， 如 何 写 的 规 范 ， 十 分 感 谢 老 师 ！ 老 师 的 语 速 和 逻 辑 也 非 常 清 晰 明 了 ！ 给 老 师 点 赞 ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！,O O O O O B-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 适 合 初 学 者 入 门 学 习 p y t h o n ， 深 入 浅 出 讲 解 ， 辅 以 习 题 ， 融 会 贯 通 。,O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
我 本 身 一 点 也 不 喜 欢 编 程 ， 本 科 期 间 学 过 的 那 些 语 言 课 我 甚 至 很 反 感 。 看 到 开 设 这 个 P y t h o n 课 程 我 就 想 试 试 ， 没 想 到 嵩 老 师 让 我 对 编 程 产 生 了 兴 趣 ， 真 的 很 神 奇 ！ 讲 的 内 容 很 细 致 ， 有 问 题 还 可 以 及 时 解 决 ， 能 把 那 些 无 聊 的 东 西 讲 得 如 此 有 趣 ， 真 的 很 不 容 易 。 值 得 五 星 ！,O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
有 趣 生 动 ， 让 人 热 爱 上 P y t h o n,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2
层 层 递 进 ， 结 合 案 例 ， 融 汇 知 识 点 ， 重 点 突 出 ， 非 常 好 ， 非 常 感 谢 老 师 ！,O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 的 很 系 统 ， 很 有 条 理 ， 对 于 程 序 小 白 来 说 非 常 受 益 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 很 详 细 ， 涉 及 到 的 知 识 点 被 他 讲 解 的 通 俗 易 懂 ， 除 此 之 外 ， 还 用 实 际 案 例 帮 我 们 提 升 自 信 ， 让 我 们 脚 踏 实 地 ， 一 步 一 个 脚 印 。 D A Y D A Y U P .,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1
对 于 没 有 基 础 的 文 科 生 来 说 也 毫 无 障 碍 ！ 喜 欢 嵩 老 师 温 柔 的 声 音 和 详 细 的 讲 解 。 谢 谢 老 师 们 的 辛 勤 付 出 。,O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
实 例 结 合 教 学 的 方 式 非 常 好 ， 希 望 后 面 推 出 更 多 的 教 学 内 容,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
虽 是 基 础 ， 但 还 是 有 一 定 难 度 ， 没 办 法 ， 即 来 来 了 ， 就 好 好 学 吧 ， 希 望 可 以 把 它 完 美 学 会 ！,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
"我 的 感 受 是 这 门 课 对 小 白 非 常 非 常 友 好 ， 只 要 按 照 课 程 从 前 向 后 听 就 可 以 保 证 后 面 的 内 容 不 会 有 不 知 道 的 地 方 ， 因 为 前 面 没 讲 到 的 后 面 绝 对 不 会 理 所 当 然 的 当 作 听 着 知 道 讲 下 去 ， 稍 微 超 出 听 者 知 识 水 平 的 地 方 都 会 说 明 白 或 者 说 "" 不 明 白 也 没 关 系 "" 。 而 且 没 有 什 么 枯 燥 的 感 觉 ， 而 且 嵩 老 师 特 别 深 入 浅 出 ， 把 握 重 要 信 息 ， 讲 给 学 生",O O O O O O O B-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
授 课 节 奏 挺 好 的 ， 照 顾 到 了 注 意 力 的 一 个 集 中 的 时 间 段 。 其 他 的 话 ， 希 望 的 后 期 的 课 程 答 疑 解 惑 上 做 好 总 结 ， 形 成 文 件 ， 供 后 期 的 学 习 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 教 的 非 常 不 错 ， 希 望 推 出 更 多 深 入 的 内 容,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
深 入 浅 出 ， 但 是 实 例 部 分 有 时 候 会 让 人 感 觉 有 些 难,O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 适 合 入 门 的 学 习 ， 虽 然 自 己 还 是 不 会 写 p y t h o n 的 程 序 ， 但 是 现 在 大 部 分 不 是 很 复 杂 的 程 序 都 可 以 看 懂 了 ， 老 师 们 辛 苦 ！,O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
都 挺 好 ， 但 是 感 觉 分 的 视 频 太 多 ， 有 点 散 散 的 感 觉,O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0
知 识 点 讲 解 的 很 清 晰 ， 背 景 动 画 如 果 能 做 的 更 精 彩 一 点 就 更 好 了 ！,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O,2 2 2 2 2 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
简 单 易 学 对 于 初 学 编 程 者 很 友 好,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1
S o n g t i a n l a o s h i z u i h a o l e,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O,2 2 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
是 我 第 一 门 在 慕 课 上 自 主 看 完 的 课 程 ， 非 常 感 谢 老 师 领 进 门,O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 的 讲 课 思 路 很 清 晰 ， 疑 难 解 答 也 恰 到 好 处 ， 不 会 一 次 性 给 过 多 的 知 识 量 ， 每 个 知 识 点 都 能 得 到 较 深 入 的 理 解 ， 对 新 手 来 说 非 常 好 。,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O,1 1 -1 2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
讲 解 细 致 ， 理 论 清 晰 明 了 ， 实 战 有 趣 实 用 。 感 谢 老 师 们 的 辛 勤 努 力 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 2 -1
能 否 推 荐 一 本 P y t h o n 入 门 教 科 书 ？,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1
深 入 浅 出 ， 先 前 没 有 接 触 过 p y t h o n 的 初 学 者 也 能 很 快 入 门 。,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 -1 -1 -1 -1 2 2 -1
不 错 ， 倒 是 画 面 曝 光 过 高 ， 眼 睛 有 点 不 舒 服,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
很 有 趣 ， 从 案 例 出 发 ， 配 合 练 习 和 考 试 ， 让 人 不 断 复 习,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
课 程 结 构 合 理 ， 逻 辑 清 晰 ， 简 明 易 懂 ， 适 合 没 有 编 程 基 础 的 同 学 学 习,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 安 排 合 理 ， 例 子 代 表 性 强 ， 点 赞,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O,2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
前 面 基 础 讲 得 很 踏 实 ， 后 面 两 章 感 觉 稍 微 有 点 点 快,O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1
很 棒 哦 ~ 学 到 了 很 多 东 西 ， 会 看 后 续 设 计 课 程 的,O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1
内 容 都 是 我 想 要 的 ， 难 度 适 中 整 体 感 觉 不 错,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 2 2 -1 -1
挺 好 的 ， 难 度 适 中 ， 提 问 后 老 师 们 的 解 答 得 特 别 快 。,O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 -1 2 2 -1 -1 -1 -1 -1
特 别 喜 欢 这 门 课 从 一 个 程 序 小 白 ， 看 到 了 程 序 的 轮 廓 感 谢 老 师 ！,O O O O O O B-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 2 2 -1
循 序 渐 进 ， 复 习 很 必 要 ！ 坚 持 3 周 了 ~,O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
可 以 ， 希 望 加 强 学 习 练 习 题 的 批 阅,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 2 2
"老 师 讲 解 的 非 常 棒 , 水 平 也 很 高",B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 课 非 常 简 洁 易 懂 ， 是 我 在 网 上 见 过 的 最 高 质 量 的 课 程 ！ 希 望 这 次 能 在 老 师 的 课 程 帮 助 下 通 过 计 算 机 二 级 p y t h o n 的 考 试 ！ ！,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 -1 -1
课 程 设 计 的 非 常 好 ， 讲 解 也 很 到 位 ， 看 似 复 杂 的 问 题 ， 两 三 句 话 就 能 解 释 的 通 俗 易 懂 ， 理 论 联 系 实 际 ， 举 例 贴 近 生 活 ， 能 够 调 动 学 习 兴 趣 ， 老 师 花 了 很 多 心 思 ， 大 赞,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1
很 惊 讶 能 够 有 如 此 详 细 的 课 程 带 我 走 向 p y t h o n 的 世 界,O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1
太 棒 了 ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！,O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 零 基 础 学 习 P y t h o n 有 很 大 的 帮 助,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2
对 于 了 解 p y t h o n 基 础 知 识 是 一 门 不 错 的 课 程,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP,-1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2
初 学 ， 没 有 基 础 ， 一 二 两 周 还 能 跟 得 上 ， 到 第 三 周 有 点 吃 力 ， 经 常 要 反 复 回 看 ， 停 下 来 好 好 思 考 才 能 明 白 ， 有 些 也 没 弄 懂,O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
在 职 人 员 ， 毫 无 编 程 基 础 ， 为 了 适 应 时 代 发 展 ， 不 是 为 了 多 少 天 掌 握 P y t h o n ， 重 要 的 是 理 解 如 何 用 这 么 语 言 更 好 地 去 跟 电 脑 沟 通 ， 更 好 地 输 出 我 想 要 的 。 老 师 讲 得 很 好 ， 但 对 于 完 全 0 基 础 的 人 来 说 ， 开 始 有 些 懵 ， 习 题 或 者 测 试 的 题 目 涉 及 到 的 语 句 和 函 数 都 没 有 使 用 过 ， 准 备 给 自 己 额 外 补 充 一 下 这 些 知 识 ！ 感 谢 m o o c 提 供 这 么 好 的 学 习 平 台 ， 离 开 校 园 后 ， 已 经 很 少 能 接 触 到 这 么 好 的 老 师 了 ！,O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
当 初 一 知 半 解 的 学 了 其 他 的 p y t h o n 课 程 ， 现 在 才 系 统 的 学 习 了 基 础 知 识,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
这 大 概 是 寻 找 了 这 么 久 ， 遇 到 讲 p y t h o n 最 棒 的 课 程 ！ ！ ！ ！ ！ ！ ！,O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 题 型 设 计 很 新 颖 ， 收 获 很 多 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O,2 2 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1
真 的 太 棒 了 ， 仿 佛 打 开 了 新 世 界 的 大 门,O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2
学 到 现 在 已 经 对 p y t h o n 了 解 很 多 ， 能 够 独 立 思 考 并 理 解 代 码,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
课 程 内 容 通 过 小 案 例 带 入 编 写 思 路 ， 再 通 过 借 助 的 模 块 来 做 使 用 教 学 ， 只 要 能 举 一 反 三 ， 基 本 可 以 掌 握 编 写 原 理 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
i t ` s r i g h t g o o d,O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 教 的 挺 详 细 的 ， 例 子 也 特 别 好 ， 适 合 初 学 者,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2
超 级 有 料 、 教 学 法 极 其 高 超 的 p y t h o n 入 门 课 ！ 大 爱 嵩 老 师 ！,O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O,-1 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 -1 -1 -1 2 2 2 -1
我 觉 得 嵩 天 老 师 讲 课 深 入 浅 出 ， 讲 的 特 别 清 楚 ， 我 会 坚 持 上 完 这 门 课 的 。,O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O O O O B-ASP O O,-1 -1 -1 2 2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1
我 是 零 基 础 小 白 ， 跟 着 北 京 理 工 大 学 的 老 师 学 了 一 段 时 间 ， 感 觉 内 容 丰 富 有 条 理 ， 节 奏 紧 凑 ， 受 益 匪 浅 ， 很 感 谢 老 师 ， 感 谢 慕 课 平 台 。 要 努 力 啊 ， 不 然 对 不 起 这 么 好 的 课 。,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1
作 为 入 门 级 课 程 ， 这 门 课 总 体 结 构 上 概 括 了 p y t h o n 相 关 的 多 方 面 内 容 ， 同 时 也 由 浅 入 深 的 讲 解 了 很 多 语 法 与 函 数 的 使 用 与 规 则 ， 小 白 也 可 以 有 个 基 本 的 知 识 掌 握 。,O O O O O B-ASP I-ASP O O O B-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 2 -1 -1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 1 1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1
课 程 非 常 好 ！ 讲 的 深 入 浅 出 ， 十 分 有 趣,B-ASP I-ASP O O O O B-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 很 清 楚 。 让 听 课 很 有 效 率 。 老 师 费 心 了,B-ASP I-ASP O O O O O O O O O O O B-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 2 2 -1 2 2 -1 -1 -1
课 程 设 计 合 理 ， 老 师 讲 解 清 晰 严 密 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1
挺 喜 欢 的 ， 希 望 后 续 能 学 到 更 深 层 次 的 东 西,O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
能 多 讲 一 些 实 用 性 的 例 子 就 好 了 ， 不 仅 仅 局 限 于 基 础 知 识 。,O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1
课 程 时 间 段 的 分 割 对 零 基 础 的 小 白 非 常 友 好 ， 不 会 学 着 学 着 因 枯 燥 放 弃 ； 老 师 讲 解 的 语 速 缓 慢 便 于 理 解 ， 知 识 点 揉 碎 举 例 便 于 吸 收 ， 学 习 资 料 减 少 复 习 时 间 ， 总 体 棒 棒 哒 ！ ！,B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 具 体 也 很 有 耐 心 点 赞,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
从 有 趣 的 例 子 出 发 ， 学 编 程 也 没 那 么 枯 燥 了 ！ 老 师 讲 的 也 很 生 动 ！ ！,O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
听 过 的 最 好 的 p y t h o n 课 程 ， 嵩 老 师 的 讲 解 很 到 位 ， 比 我 之 前 看 的 其 他 的 P y t h o n 课 程 强 太 多 啦 ！ ！ ！,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
讲 的 通 俗 易 懂 ！ 有 结 合 实 例 的 教 学 理 解 后 减 少 学 习 的 记 忆 难 度 ， 很 棒 ！,B-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1
零 基 础 小 白 ， 竟 然 能 听 的 比 较 明 白 ， 赞 ！ 虽 然 有 很 多 英 文 单 词 看 不 懂 ， 我 相 信 ， 背 下 来 不 是 很 难 的 事,O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 于 小 白 来 说 有 些 内 容 不 好 理 解 ， 但 我 相 信 自 己 能 学 好,O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 1 1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 编 排 细 腻 紧 凑 ， 内 容 丰 富 ， 有 举 一 反 三 模 块 ， 启 发 思 考 ， 课 后 习 题 适 当 ， 整 体 而 言 ， 这 是 不 错 的 p y t h o n 学 习 课 程 。 微 信 群 已 满 ， 无 法 和 其 他 学 员 讨 论 交 流 ， 不 知 道 有 其 他 讨 论 群 分 享 没 ？,O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 2 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
简 单 易 懂 老 师 讲 的 很 好 ~ 上 手 快,O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1
非 常 棒 的 课 程 ， 老 师 口 齿 清 晰 ， 课 程 逻 辑 清 楚 ， 内 容 具 体 而 不 啰 嗦 ， 脉 络 关 联 性 、 延 展 性 好,O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 2 2 2 -1
蛮 好 的 ， ， 至 少 我 理 清 了 P y t h o n ， 可 以 做 实 验 用 了,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1
老 师 讲 课 生 动 形 象 ， 内 容 丰 富 有 趣 ， 我 从 中 学 到 许 多 东 西,B-ASP I-ASP O B-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
特 别 好 ， 希 望 嵩 老 师 开 设 更 多 和 深 度 学 习 有 关 的 课 程,O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2
教 学 内 容 ， 紧 扣 易 学 习 ， 感 觉 很 难 的 内 容 被 有 条 理 的 输 出 出 来 ， 学 习 过 程 有 渐 进 、 紧 扣 的 感 觉 ， 最 大 的 感 受 是 没 有 学 习 疑 惑 导 致 学 习 卡 壳 ； 喜 欢 老 师 的 标 准 、 专 业 ， 隔 着 屏 幕 都 感 觉 到 老 师 的 精 益 精 神 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1
课 程 很 不 错 。 就 是 作 业 有 点 超 纲 ， 得 用 上 循 环 才 能 解 决 。 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
挺 不 错 的 ， 在 A I 帮 助 下 能 够 及 时 解 决 学 习 中 的 问 题,O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1
老 师 讲 的 很 好 ， 受 益 无 穷 ， 谢 谢 老 师 ， 您 辛 苦 啦 ！,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
基 础 上 学 习 很 扎 实 ， 希 望 能 够 尽 快 掌 握 p y,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
学 习 了 P y t h o n 的 基 础 语 法 知 识 和 一 些 库 的 使 用 ， 受 益 颇 多 ， 感 谢 ！,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 1 1 -1 -1 -1 1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
l 老 师 课 程 讲 的 明 白 ， 学 习 很 有 收 获 。,O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,-1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 个 课 程 确 实 很 不 错 ， 整 体 的 课 程 设 计 很 好 ， 每 节 课 都 有 相 应 的 回 顾 复 习 ， 还 有 课 后 作 业 ，,O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 -1
我 很 喜 欢 这 门 课 程 以 及 老 师 的 认 真 讲 解 。,O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 2 2 -1
超 级 棒 ， 但 是 因 为 没 什 么 基 础 ， 感 觉 理 解 起 来 有 点 难 ， 但 是 学 到 后 面 ， 就 感 觉 逐 渐 能 明 白 啦 。 谢 谢 老 师,O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
课 程 内 容 循 序 渐 进 ， 非 常 适 合 新 手,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 课 通 俗 易 懂 ， 内 容 由 易 到 难 ， 是 一 门 很 棒 的 课 程 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 越 来 越 详 细 了 ， 越 来 越 好 了 ， 赞 赞 赞,B-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 非 常 的 细 致 ， 超 级 容 易 听 懂,B-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
深 度 方 面 不 足 ， 但 讲 解 生 动 有 趣 ， 案 例 贴 切 有 实 践 性 ， 拓 展 也 很 丰 富,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O,0 0 0 0 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
通 俗 易 懂 ， 案 例 精 彩 ， 收 获 很 多 ， 非 常 不 错,O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
通 俗 易 懂 ， 且 知 识 点 都 讲 到 了 ， 分 块 也 很 明 确 ， 每 章 会 有 前 情 回 顾,O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 2
很 赞 ， 没 基 础 小 白 学 的 摸 着 点 门 了,O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP O,-1 -1 -1 -1 0 0 1 1 -1 -1 -1 -1 -1 2 -1
嵩 天 老 师 授 课 通 俗 易 懂 ， 非 常 棒 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
不 愧 是 北 京 理 工 大 学 的 老 师 和 团 队 ， 每 个 细 节 都 精 益 求 精 ， 无 论 是 录 像 、 讲 解 、 课 程 设 计 、 测 验 环 节 、 考 试 试 题 都 能 看 出 团 队 付 出 了 极 大 的 心 血 ， 也 充 满 了 换 位 思 考 ， 作 为 初 学 者 ， 本 来 很 担 心 网 上 学 习 的 方 式 难 以 真 正 掌 握 理 解 p y t h o n 语 言 ， 但 第 一 周 的 课 程 听 下 来 ， 不 仅 讲 解 非 常 细 致 ， 试 题 的 难 度 也 非 常 符 合 初 学 者 的 水 平 ， 想 要 提 高 水 平 的 可 以 多 看 看 练 习 题 ， 没 有 时 限 和 分 数 压 力 ， 难 度 明 显 高 了 许 多 ， 这 两 种 模 式 结 合 ， 让 我 受 益 匪 浅 ， 期 待 你 们 的 新 课 程 。,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 2 2 2 2 2 2 -1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 2 2 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
嵩 天 老 师 团 队 出 品 的 p y t h o n 课 程 真 的 太 赞 了 ！ ！ ！ 知 识 结 构 清 晰 ， 理 论 实 践 结 合 ， 还 有 导 学 和 复 习 辅 导 ， 所 有 要 素 加 起 来 ， 简 直 就 是 梦 中 情 “ 课 ” ！,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP O O,2 2 2 2 2 2 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1
第 一 次 在 这 个 平 台 上 学 习 课 程 ， 觉 得 很 棒 ， 以 后 还 会 坚 持 学 习 的 ！,O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 老 师 认 真 负 责 ， 思 路 清 晰 ， 收 获 颇 丰 ， 继 续 努 力 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
请 问 这 个 学 完 能 达 到 计 算 机 二 级 的 水 平 吗 ？,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 1 1 -1 -1
感 觉 非 常 不 错 ， 零 基 础 也 可 以 听 得 懂,O O O O O O O B-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1
讲 的 很 基 础 ， 非 常 适 合 我 这 种 没 有 学 过 的 小 白,O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
由 浅 入 深 ， ， 速 度 较 慢 ， 适 合 自 学 。,O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 2 2 -1
对 零 基 础 比 较 友 好 ， 能 听 懂 ， 老 师 讲 课 比 较 有 耐 心 。 课 程 视 频 分 几 个 小 段 （ 而 不 是 几 十 分 钟 的 视 频 ） ， 这 样 听 起 来 不 会 累 。 课 后 例 题 讲 的 很 详 细 ， 还 有 课 程 小 结 部 分 ， 教 学 模 式 很 喜 欢 。,O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,-1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1
很 棒 ， 学 习 很 快 ， 能 学 到 很 多 知 识,O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 的 很 好 ， 但 是 部 分 章 节 内 容 跳 跃 跨 度 稍 微 有 点 大,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
目 前 完 成 第 一 周 的 学 习 ， 老 师 领 进 门 ， 重 点 还 是 自 己 要 扩 展 对 程 序 的 理 解 ， 期 待 第 二 周 。,O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 1 1 1 -1 1 1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 -1
很 好 ， 坚 持 跟 学 完 ， 一 定 有 收 获 ！,O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 里 是 对 该 评 论 的 正 确 标 注 ： 清 晰 思 路 ， 非 常 好 ， 如 果 有 思 维 导 图 框 架 就 更 好 了,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1
循 序 渐 进 ， 无 编 程 基 础 的 小 白 也 能 听 得 懂 。 老 师 的 讲 解 非 常 清 晰 ， 我 自 己 买 了 书 ， 边 听 边 做 自 己 ， 已 经 慢 慢 能 看 懂 一 些 代 码 。 感 谢 老 师 。,O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1
老 师 讲 解 得 很 详 细 ， 一 开 始 因 为 没 有 书 本 ， 有 些 练 习 题 不 会 做 ， 感 觉 有 点 懵 ， 后 面 逐 渐 就 好 的 。 以 后 还 是 得 多 练 习 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
z h e n d e c h a o j i b a n g d e,O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 谢 老 师 ， 每 节 课 都 有 不 同 收 获 ， 不 光 学 习 了 知 识 ， 还 学 会 了 很 多 方 法 ， 提 供 了 很 多 思 路 ， 谢 谢 老 师 和 助 教 的 解 答 ， 真 的 收 获 很 多 ， 谢 谢 ！,O O B-ASP I-ASP O O O B-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
逻 辑 清 晰 ， 充 满 趣 味 ， 收 获 很 多 以 前 不 知 道 的 内 容 ， 学 习 了 一 种 新 的 思 维 方 式 。,B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
这 个 课 让 人 收 获 很 大 ， 学 会 了 很 多 不 同 的 处 理 方 法 ， 加 油,O O B-ASP O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
从 浅 入 深 ， 讲 解 独 道 ， 学 习 很 快 乐,O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
视 频 制 作 的 很 用 心 ， 课 程 进 度 安 排 合 理 ， 老 师 讲 解 生 动 详 细 ， 练 习 题 和 作 业 有 解 析 好 评 ！,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 1 1 -1 -1 -1
老 师 的 授 课 方 式 思 路 很 清 晰 ， 而 且 循 序 渐 渐 很 容 易 接 受 。,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O,1 1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 堂 讲 解 都 是 重 要 的 点 ， 个 人 感 觉 是 引 入 ， 接 下 来 还 是 需 要 自 己 做 练 习 ， 多 练 习 熟 练 和 掌 握 ， 接 下 来 继 续 练 习,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP,1 1 1 1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
非 常 好 ！ ！ ！ ！ ！ 感 谢 老 师 的 详 细 的 讲 解,O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2
老 师 讲 的 深 入 浅 出 ， 希 望 能 好 好 利 用 资 源 坚 持 学 下 去,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
特 别 棒 ， 真 的 超 乎 想 象 ， 课 程 有 趣 简 单 易 懂 ！ ！,O O O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
知 识 脉 络 介 绍 得 很 清 楚 ， 学 习 起 来 也 很 有 趣,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
学 习 使 我 快 乐 ！ ！ ！ 能 学 习 到 自 己 感 兴 趣 的 知 识 ， 提 高 知 识 储 备 能 力,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 2 2
课 程 非 常 棒 ！ 让 零 基 础 的 人 也 能 轻 松 理 解 ， 感 谢 嵩 老 师,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2
清 晰 明 了 ， 收 获 很 大 ， 谢 谢 老 师 们 ！,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1
能 够 系 统 滴 学 习 p y t h o n 语 法 知 识 ， 收 获 挺 多 的 。,O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 1 1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1
讲 授 十 分 清 晰 细 致 ， 非 常 适 合 编 程 小 白 。,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1
课 程 内 容 安 排 合 理 ， 不 是 传 统 的 p y t h o n 知 识 点 的 罗 列 ； 实 例 选 择 恰 当 ， 代 码 简 洁 、 正 确 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 0 0 0 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
2 0 2 2 年 ， 新 的 开 始 ， 虎 虎 生 威 ！,O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
真 的 是 很 棒 的 一 门 课 ， 小 点 分 的 很 详 细 ， 忘 了 哪 点 直 接 点 章 节 就 能 找 到 真 的 是 很 棒 的 一 门 课 ， 小 点 分 的 很 详 细 ， 忘 了 哪 点 直 接 点 章 节 就 能 找 到,O O O O O O O O B-ASP O B-ASP I-ASP O O O O O O O O O B-ASP O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP O B-ASP I-ASP O O O O O O O O O B-ASP O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 的 很 通 俗 ， 虽 然 时 间 很 短 ， 但 实 际 上 讲 的 内 容 比 较 详 细 ， 可 以 快 速 入 门 P y t h o n,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
经 常 看 到 P y t h o n 的 广 告 ， 没 想 到 自 己 也 能 学 习 起 来 ， 跟 着 名 师 学 习 的 感 觉 不 错 ， 提 升 太 多 了 ， 自 己 还 需 好 好 努 力 ！ 感 谢 老 师 们 精 心 制 作 的 课 程 ， 太 值 得 学 习 了 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
"由 浅 入 深 , 讲 解 了 许 多 p y t h o n 语 法 内 容 . 但 有 些 部 分 仍 然 不 够 透 彻 , 如 果 有 其 他 语 言 的 基 础 会 更 容 易 理 解 本 课 知 识 .",O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 2 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1
我 觉 得 老 师 讲 的 特 别 好 ， 很 仔 细,O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
挺 好 的 ， 教 的 能 够 补 充 课 堂 所 交 不 了 的,O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 很 清 晰 ， 非 常 适 合 初 学 者,B-ASP I-ASP B-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2
老 师 课 讲 的 很 好 ， 通 俗 易 懂 ， 像 我 这 样 小 白 也 能 听 的 明 白,B-ASP I-ASP B-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
课 程 安 排 节 奏 很 好 ， 循 序 渐 进 ， 入 门 的 基 础 课 程 。,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1
很 棒 的 p y t h o n 入 门 课 程 ， 有 很 多 有 趣 的 案 例,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP,-1 -1 -1 1 1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2
用 有 用 的 程 序 串 联 常 用 的 知 识 点 ， 容 易 激 发 兴 趣 和 使 用 知 识 点,O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1
条 理 清 晰 讲 解 很 好 不 愧 精 品 课 程,O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2
一 个 个 小 视 频 的 方 式 让 人 觉 得 轻 松 ， 更 多 的 实 例 教 学 更 能 让 我 们 明 白 如 何 实 际 运 用 ， 真 的 让 我 很 惊 喜 这 门 课 ！,O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP O,-1 -1 -1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1
讲 的 很 详 细 ， 可 以 随 时 返 回 不 太 理 解 的 知 识 点 重 复 学 习 很 不 错,O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1
内 容 精 良 ， 节 奏 安 排 走 心 ， 老 师 演 讲 很 吸 引 人 ， 配 套 练 习 很 有 用 ！,B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
讲 解 十 分 通 俗 易 懂 ， 比 在 课 室 上 课 时 的 枯 燥 内 容 好 多 了 ！,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1
课 程 内 容 组 织 合 理 ， 讲 解 清 晰 易 懂,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
这 为 了 让 小 孩 学 习 ， 二 十 年 后 重 新 拾 起 ， 课 程 很 不 错 ， 深 入 浅 出 ， 受 益 匪 浅 ， 就 是 武 功 丢 了 很 多 年 ， 感 觉 自 己 效 率 低 了 点,O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
嵩 天 老 师 是 永 远 的 男 神 ， 外 瑞 顾 得 。,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
需 要 反 复 观 看 才 能 理 解 更 多 的 是 需 要 去 实 践 非 常 有 意 思 但 对 于 我 这 种 小 白 来 说 好 难,O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
内 容 浅 显 易 懂 ， 老 师 讲 的 很 精 彩 ， 新 的 篇 章 会 回 顾 老 的 内 容 ， 学 习 效 率 很 高,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1
开 阔 了 视 野 如 果 能 课 堂 提 问 老 师 解 答 就 更 好 了,O O O B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 将 p y t h o n 内 容 讲 解 得 通 俗 易 懂,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1
这 挺 好 的 一 门 课 ， 像 老 师 所 讲 ， 现 在 已 经 进 入 人 工 智 能 时 代 。 多 学 点 知 识 ， 紧 跟 时 代 脚 步 。,O O O O O O B-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
当 年 差 点 考 上 这 里 ， 现 在 知 道 有 这 么 好 的 老 师 后 悔 当 年 没 努 力 了,O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
这 老 师 讲 的 很 好 ， 逻 辑 清 晰 ， 课 件 漂 亮 ， 实 例 有 趣 。 强 烈 推 荐 ！,O B-ASP I-ASP O O O O O O B-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O,-1 1 1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
入 门 了 新 的 计 算 机 编 程 语 言 ， 感 谢 老 师 的 精 心 讲 授 。,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1
老 师 的 课 程 逻 辑 十 分 清 晰 ， 很 适 合 入 门 级 的 同 学 们 学 习 ， 收 获 了 很 多 ， 接 着 好 好 努 力 ！,B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 是 一 个 p y t h o n 小 白 ， 这 门 课 程 特 别 适 合 看 ， 不 会 听 不 懂 ， 里 面 选 的 案 例 也 特 别 好 ， 很 有 针 对 性 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
学 习 一 门 新 的 语 言 ， 很 开 心 ！ 很 有 成 就 感 ！ 希 望 对 我 的 工 作 有 所 帮 助 ！,O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
很 棒 ， 要 是 内 容 每 年 更 新 就 更 好 了,O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 由 浅 入 深 并 且 有 丰 富 的 实 例 来 练 习 ， 我 会 好 好 的 坚 持 学 下 去 的,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 得 很 细 致 ， 对 于 初 学 者 来 说 还 是 比 较 容 易 理 解,O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 开 始 喜 欢 p y t h o n 语 言 程 序 设 计 。 老 师 讲 的 易 懂 但 我 的 电 脑 知 识 不 足 ， 遇 到 很 多 烦 恼 事 ， 常 常 出 错 ， 但 我 会 慢 慢 克 服 ， 补 齐 不 足 。 谢 谢 老 师 让 我 认 识 p y t h o n 语 言 。 谢 谢,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1
每 周 的 什 么 时 间 更 新 课 程 呢 ？ 第 四 周 课 程 还 没 更 新 有 点 慢 了,O O O O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 1 1 1 0 0 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 深 入 浅 出 ， 实 例 都 很 方 便 理 解 ， 非 常 好 的 入 门 课 程 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
很 感 谢 有 这 个 平 台 ， 让 没 有 机 会 上 大 学 的 我 能 聆 听 教 授 教 诲,O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
让 我 懂 得 了 比 你 优 秀 的 人 比 你 更 努 力 ！,O O O O O O O O O O B-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1
我 觉 得 老 师 讲 的 很 容 易 理 解 但 是 还 是 自 己 要 勤 加 练 习 纸 上 得 来 终 觉 浅,O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
以 小 见 大 ， 沉 浸 式 体 验 ， 你 值 得 学 习,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1
不 愧 是 国 家 精 品 课 程 ， 讲 的 清 晰 易 懂 ， 零 基 础 也 能 听 得 懂 ， 课 程 中 的 案 例 看 似 简 单 ， 但 是 设 计 的 知 识 点 不 少 ， 可 以 看 出 老 师 们 非 常 用 心 ！,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O,-1 -1 -1 2 2 2 2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1
我 真 的 太 喜 欢 这 个 老 师 了 ， 太 n i c e 了 ！ ！ ！,O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 非 常 好 ， 很 容 易 听 懂 ， 因 为 是 前 几 天 才 看 到 这 个 课 的 ， 这 几 天 学 起 来 就 跟 追 剧 似 的 ， 按 目 前 学 习 到 的 内 容 觉 得 自 己 掌 握 的 还 可 以 ， 争 取 尽 快 追 到 老 师 的 最 新 一 课,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2
老 师 说 的 特 别 清 楚 ， 好 理 解 ， 非 常 好,B-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 通 俗 易 懂 ， 课 程 节 奏 也 很 棒 ， 期 待 老 师 的 其 他 课 程 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1
老 师 教 的 非 常 好 ， 很 细 致 也 很 清 楚 ， 要 是 课 下 交 流 再 多 点 就 好 啦,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 我 已 工 作 ， 做 为 业 内 提 升 。,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 课 比 较 清 晰 ， 对 于 P Y T H O N 的 讲 解 比 较 细 致,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1
嵩 老 师 讲 的 很 好 ， 形 象 生 动 ， 对 学 习 p y t h o n 打 好 基 础 有 很 大 帮 助 。,B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 2 -1 -1 -1 2 2 -1
很 好 很 好 ， 国 家 精 品 课 堂 就 是 好 啊 ！,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1
各 位 老 师 ： 你 们 辛 苦 了 ， 课 程 从 简 单 程 序 入 手 讲 解 比 较 好 ， 因 为 个 人 的 英 语 水 平 太 差 有 时 说 的 英 语 听 不 懂 ， 最 好 讲 的 时 候 在 屏 幕 上 显 示 所 说 的 主 要 内 容 ， 听 不 懂 能 看 懂 。,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 吸 引 人 ， 从 有 趣 的 例 子 讲 解 编 程 ， 注 重 知 识 的 趣 味 性 、 重 复 性 、 条 理 性 ， 同 时 对 我 有 很 大 的 鼓 励 ， 数 据 化 天 天 向 上 的 力 量 ， 对 于 我 这 个 新 手 来 说 觉 得 超 赞 ！ 支 持 ！,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 -1 1 1 1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
T h e b e s t c o u r s e t h a t I h a v e e v e n l e a r n t o f P y t h o n,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
总 体 高 于 市 面 质 量 ， 但 是 还 是 有 一 点 遐 思 ， 老 师 理 解 能 力 和 表 达 能 力 有 问 题 ， 需 要 我 们 自 行 百 度 。,O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 1 1 0 0 0 0 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1
嵩 老 师 讲 的 很 棒 ， 课 程 内 容 十 分 条 理 ， 是 门 很 优 秀 的 课 程,B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP,2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
"p o w ( 3 , p o w ( 3 , 9 9 ) ) # 请 在 I D L E 内 输 入 后 回 车",B-ASP I-ASP I-ASP O B-ASP O B-ASP I-ASP I-ASP O B-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,1 1 1 -1 1 -1 1 1 1 -1 1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
感 谢 中 国 慕 课 ， 感 谢 所 有 付 出 的 老 师 和 工 作 人 员 。 让 我 能 够 选 择 自 己 舒 适 的 方 式 学 习 知 识 、,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1
对 于 完 全 业 余 自 学 的 文 科 女 生 非 常 友 好 呢 ， 感 谢 老 师 温 和 耐 心 地 讲 解 ~ ❤,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1
一 个 电 子 证 书 就 要 1 0 0 块 ， 太 贵 了 吧,O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 1 1 1 1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1
重 新 燃 起 学 习 的 兴 趣 ， 编 程 也 是 一 件 很 有 意 思 的 事 情,O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
"花 了 一 个 月 基 本 学 完 了 嵩 天 老 师 的 课 程 ， 这 是 我 看 过 的 最 好 的 中 文 p y t h o n 入 门 课 程 ， 没 有 之 一 。 课 程 设 计 得 很 科 学 ， 知 识 十 分 系 统 ， 老 师 讲 课 真 得 用 心 ， 真 的 感 谢 北 理 工 这 个 m o o c 团 队 。 和 这 个 课 程 相 识 恨 晚 ， 学 完 p y t h o n 觉 得 学 习 恨 晚 ， 它 的 简 洁 和 强 大 效 率 真 的 让 我 折 服 。 我 之 前 好 几 年 想 入 门 p y t h o n 都 没 成 功 。 现 在 安 装 了 S p y d e r 和 a n a c o n d a , 一 步 一 步 练 习 ， 终 于 顺 利 入 门 了 ， 重 要 的 是 边 学 边 练 习 ， 练 习 ， 练 习 。 支 持 我 不 断 学 习 的 动 力 是 在 i p y t h o n 框 能 够 输 入 验 证 ， 进 而 理 解 基 本 概 念 和 程 序 设 计 语 法 ， 形 成 正 反 馈 。 我 还 学 习 了 北 理 工 的 数 据 分 析 课 程 和 爬 虫 课 程 。 希 望 能 够 进 阶 成 一 名 编 程 高 手 ， 首 先 完 成 我 的 研 究 问 题 ， 中 期 深 入 学 习 数 据 结 构 ， s q l , 机 器 学 习 和 大 数 据 ， 形 成 计 算 思 维 ， 重 点 专 注 于 空 间 数 据 科 学 能 源 ， 再 通 过 两 年 的 自 学 ， 能 在 能 源 集 团 或 咨 询 公 司 找 到 一 份 和 数 据 分 析 相 关 的 工 作 。",O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 1 1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 -1 1 1 1 1 -1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
我 还 在 学 ， 感 觉 还 是 不 错 的 ， 可 操 作 性 强 ， 比 较 细 致 ， 对 零 基 础 的 也 较 为 友 好,O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1
我 是 材 料 专 业 的 一 名 研 究 生 ， 非 常 喜 欢 学 习 课 外 知 识 ， 来 拓 宽 自 己 的 知 识 范 围 和 领 域 。 学 习 了 嵩 老 师 的 课 收 获 很 大 ， 很 喜 欢 老 师 的 讲 课 风 格 和 内 容 ， 从 中 学 到 了 许 多 有 趣 、 有 用 的 知 识 。 最 后 感 谢 人 生 中 每 一 位 让 我 进 步 的 人 ！,O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O B-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP O,-1 -1 1 1 1 1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 2 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1
内 容 很 详 细 ， 细 致 ， 讲 解 清 楚 ， 适 合 初 学 者 入 门 学 习,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1
I l o v e t h e t e a c h i n g s t y l e o f t h e t e a c h e r . T h a n k s s o m u c h ! ! ! !,O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 太 清 楚 了 ， 逻 辑 很 清 晰 ， 这 才 是 编 程 老 师 应 该 有 的 样 子 。 不 像 我 当 时 的 老 师 ， 把 编 程 当 成 是 一 件 学 生 理 所 当 然 就 会 的 东 西 。 要 是 所 有 的 老 师 都 像 本 课 的 老 师 这 样 ， C 语 言 就 没 人 会 挂 科 了,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1
从 我 刚 开 始 个 人 认 为 的 一 般 般 ， 逐 渐 转 变 为 称 赞 ， 慕 课 就 是 顶 尖 。,O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 解 清 晰 明 了 ， 课 程 内 容 难 易 适 中 ， 课 程 进 程 循 序 渐 进 ， 利 于 编 程 小 白 理 解 掌 握 。,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
知 识 点 深 入 浅 出 ， 浅 显 易 懂 。 又 借 助 课 堂 小 例 子 强 化 对 知 识 的 理 解 。 真 的 是 大 爱,B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 将 复 杂 的 概 念 讲 的 浅 显 易 懂 ， 非 常 适 合 初 学 者 ， 点 赞 。,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1
老 师 讲 的 真 好 ， 是 我 听 到 的 最 有 质 量 的 课 ， 条 里 清 晰 ， 深 浅 恰 当 ， 我 喜 欢 。,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
每 天 地 铁 上 用 手 机 听 课 ， 空 闲 时 间 练 习 一 下 ， 收 获 很 多 ， 感 谢 嵩 老 师 的 讲 解 ， 谢 谢,O O O O O O O O O B-ASP O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1
总 体 来 说 课 程 内 容 详 实 ， 不 足 之 处 在 于 对 于 非 计 算 机 来 说 ， 某 些 例 子 并 不 好 在 上 课 的 过 程 中 理 解 ， 特 别 是 代 码 讲 解 部 分 。,O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 -1
p y t h o n 是 最 优 美 的 语 言 （ 狗 头 ）,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
P y t h o n 1 2 3 里 的 练 习 题 有 些 用 到 的 知 识 视 频 里 没 有 讲 ， 对 于 初 学 者 来 说 不 太 友 好,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O,1 1 1 1 1 1 1 1 1 -1 -1 0 0 0 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1
老 师 的 授 课 逻 辑 清 晰 ， 课 程 由 简 入 深 ， 由 点 带 面 ， 感 觉 无 论 是 否 编 程 小 白 ， 都 应 该 能 理 解 ， 期 待 嵩 老 师 推 出 更 多 更 广 的 p y t h o n 课 程 。 谢 谢 嵩 老 师 及 幕 后 人 员 的 用 心 付 出 ！ ！ ！,B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,1 1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 2 2 2 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1
很 不 错 ， 非 常 适 合 想 学 习 p y t h o n 的 同 学 们 。,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 -1
p y t h o n 的 入 门 课 程 ， 通 过 本 次 学 习 ， 收 获 很 大,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O,1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1
非 常 棒 的 课 程 ， 非 常 棒 的 老 师 ！ ！ 学 过 几 次 p y t h o n 都 半 途 而 废 ， 只 有 这 次 ， 熬 夜 也 要 学 完 章 节 才 想 睡 . . . . . .,O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 很 棒 ！ 计 算 机 小 白 的 我 也 可 以 逐 渐 理 解 了 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 专 业 ， 以 前 都 没 接 触 过 ， 兴 趣 过 来 的 ， 学 习 有 些 吃 力 ， 我 会 尽 力 坚 持,O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 仔 细 ， 实 例 应 用 很 有 帮 助,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
内 容 通 俗 易 懂 ， 很 适 合 小 白 ， 嵩 老 师 很 是 帅 气 哦 ， 很 喜 欢,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
每 个 视 频 简 短 ， 方 便 学 习 ， 内 容 丰 富 ， 非 常 好 。,O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1
讲 解 很 清 晰 ， 比 较 感 兴 趣 。 但 是 也 发 现 即 便 按 照 老 师 讲 的 编 程 序 ， 还 会 出 现 小 问 题 ， 继 续 努 力 中 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1
看 着 感 觉 好 简 单 ， 一 看 就 懂 ， 一 听 就 会 ， 脑 子 ： 我 懂 了 ， 手 ： 不 ， 你 什 么 都 不 会 。,O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
h x g a h c v h a g c v a c v s h,O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
喜 欢 老 师 讲 课 ， 但 是 很 多 内 容 还 记 不 住 ， 还 有 些 是 不 理 解 的 内 容 。,O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 1 1 2 2 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
深 入 浅 出 ， 经 典 例 题 ， 举 一 反 三 。,O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
很 帅 气 的 老 师 ， 很 棒 的 课 程 ( ๑ • ̀ ㅂ • ́ ) و ✧ ( ๑ • ̀ ㅂ • ́ ) و ✧ ♥ ♥,O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 上 课 逻 辑 很 清 晰 ， 上 课 的 内 容 很 具 有 启 发 性 ， 激 起 了 对 于 p y t h o n 学 习 的 热 情 。 谢 谢 老 师 的 帮 助,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2
很 棒 的 课 程 ， 适 合 各 种 学 习 目 的 的 人 群,O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
不 知 道 是 不 是 基 础 课 程 的 原 因 ， 老 师 讲 解 的 很 细 致,O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1
可 以 看 出 来 课 程 组 织 是 非 常 用 心 的 ， 很 多 商 业 化 的 课 程 也 不 会 做 这 么 好 ， 老 师 基 本 没 有 什 么 废 话 ， 讲 解 重 点 突 出 、 通 俗 易 懂 ， 视 频 画 质 不 说 像 艺 术 作 品 一 样 精 美 ， 最 起 码 是 干 净 有 序 的 ， 每 单 元 有 导 论 、 有 小 结 、 有 作 业 、 有 理 论 也 有 实 操 ， 跟 着 课 程 走 的 话 学 习 效 果 很 好 。 我 是 没 什 么 太 多 编 程 基 础 的 刚 毕 业 学 生 ， 感 觉 这 门 课 程 很 适 合 用 作 P Y T H O N 入 门 。 感 谢 老 师 的 精 彩 分 享 ， 感 谢 幕 后 的 组 织 策 划 的 辛 勤 付 出 ， 感 谢 M O O C 平 台 。,O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 1 1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1
很 棒 ， 能 够 学 到 很 多 知 识 ， 逻 辑 清 晰,O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1
每 一 课 单 看 都 是 不 错 的 ， 就 是 不 成 体 系,O O B-ASP O O O O O O O O O O O O B-ASP I-ASP,-1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0
上 学 真 的 要 好 好 读 书 ， 不 过 现 在 真 的 国 家 进 步 了 ， 信 息 发 达 了 ， 很 前 很 难 接 触 到 这 么 好 的 学 习 平 台 。,O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
课 程 内 容 循 序 渐 进 ， 结 合 课 后 测 试 和 作 业 ， 开 始 入 门 p y t h o n 啦 ! 加 油 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1
老 师 讲 解 的 非 常 详 细 ， 而 且 声 音 很 清 晰 ， 速 度 也 很 合 适,B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1
课 程 设 置 较 为 基 础 ， 配 合 实 例 ， 对 知 识 理 解 更 加 深 入 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O,1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
p y t h o n 入 门 来 听 嵩 天 老 师 的 课 非 常 好,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O,1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 2 -1 -1 -1
精 简 ， 核 心 都 讲 了 ， 希 望 能 详 细 一 点 ， 时 长 长 一 点 都 没 关 系 ， 老 师 讲 的 很 好,B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP O O O,2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 -1 -1 -1
很 棒 ！ 质 量 很 高 ， 真 正 感 觉 到 了 循 序 渐 进 的 感 觉 。 希 望 能 学 到 更 多 的 东 西 ！ ！ ！ ！ 感 谢 各 位 老 师 的 辛 苦 教 学 ！ ！,O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1
老 师 讲 的 很 细 致 很 全 面 ， 跟 着 老 师 的 节 奏 真 的 能 学 到 东 西,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 详 实 ， 案 例 丰 富 ， 贴 近 实 际 应 用 ， 尤 其 是 学 习 及 测 试 平 台 P y t h o n 1 2 3 功 能 完 善 ， 推 荐 推 荐 ！,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 2 2 2 2 2 2 2 1 1 1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 不 错 ， 对 于 我 这 种 年 纪 大 了 ， 记 性 不 好 而 且 又 没 有 基 础 的 人 来 说 ， 能 看 懂 已 经 很 不 错 了 ， 要 是 老 师 能 把 逻 辑 上 的 东 西 讲 通 透 点 的 话 ， 那 我 们 会 更 容 易 学 习 。 比 如 说 ， 我 们 看 代 码 可 以 理 解 程 序 的 运 行 过 程 ， 但 是 在 编 写 的 时 候 ， 就 感 觉 有 些 定 义 的 变 量 不 知 道 怎 么 去 定 义 或 者 怎 么 运 用 了 。 不 过 慢 慢 来 ， 我 相 信 天 天 向 上 的 力 量 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
本 来 以 为 只 是 一 般 的 教 学 培 训 课 ， 一 路 学 下 来 感 觉 干 货 很 多 ， 不 但 从 教 学 理 念 ， 而 且 从 教 学 技 能 都 有 了 很 大 的 提 高 ！,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
作 为 p y t h o n 入 门 课 程 还 是 很 不 错 的 ， 讲 的 很 细 致 ， 基 本 框 架 结 构 清 晰 ， 结 合 实 例 加 深 了 理 解 。 值 得 推 荐,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 -1 1 1 1 1 1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
课 程 很 棒 ， 感 谢 老 师 和 平 台 提 供 这 么 好 的 学 习 机 会 ！ 课 程 很 棒 ，,B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1
课 程 内 容 编 排 得 很 好 ， 由 浅 入 深 ， 容 易 上 手 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 不 错 ， 就 是 视 频 短 不 停 切 换 视 频 有 点 多,O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1
课 程 深 入 浅 出 ， 对 编 程 小 白 也 很 友 好 ， 非 常 喜 欢 。,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 很 棒 ， 能 够 引 导 我 们 去 学 习 ， 同 时 让 我 们 在 学 的 过 程 中 获 得 成 就 感 ， 以 继 续 学 习,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1
想 当 经 典 ， 从 整 体 到 局 部 ， 每 个 都 讲 得 非 常 详 细 ， 只 是 有 些 知 识 点 听 不 大 懂 ， 比 如 递 归 等,O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 -1
非 常 棒 的 课 程 设 计 ， 老 师 很 专 业 ， 这 种 授 课 方 式 即 学 原 理 又 增 知 识,O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP,-1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 1 1 -1 -1 1 1
从 小 白 变 成 了 小 白 ， 还 有 很 长 的 路 要 走,O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
真 的 很 羡 慕 现 在 的 学 生 ， 有 这 么 好 的 老 师 ， 这 么 精 彩 的 课 程 ， 这 么 棒 的 平 台 ！,O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
在 哪 可 以 找 到 一 些 编 程 练 习 题 来 做 呀 苦 恼,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1
非 常 棒 ， 全 网 最 好 的 p y t h o n 教 程,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2
我 较 自 卑 ， 是 来 蹭 课 的 ， 因 为 没 有 上 过 大 学 ， 一 直 想 听 大 学 老 师 讲 课 ， 难 得 有 这 个 机 会 ， 感 谢 M O O C ， 感 谢 嵩 天 。 在 未 听 嵩 老 师 讲 课 前 ， 也 有 听 一 些 职 业 培 训 的 讲 课 ， 比 如 阿 里 大 学 、 腾 讯 课 堂 ， 他 们 跟 嵩 老 师 一 比 较 ， 差 的 不 是 一 点 半 点 ， 他 们 喜 欢 搞 个 大 的 名 堂 在 那 里 ， 说 是 某 某 公 司 的 技 术 总 监 、 创 始 人 ， 但 是 ， 课 讲 的 很 渣 。 他 们 也 明 白 ， 面 对 着 一 群 小 白 ， 但 却 忘 记 自 己 是 小 白 时 ， 是 怎 么 入 门 学 习 的 。 讲 函 数 ， 嵩 老 师 把 基 本 知 识 讲 的 详 细 ， 职 业 培 训 只 会 告 诉 你 在 这 里 用 这 个 函 数 就 对 了 ， 函 数 的 参 数 也 没 有 细 讲 。 感 谢 M O O C ， 感 谢 嵩 老 师 ， 能 听 到 这 么 优 秀 的 课 程 。,O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O B-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 个 课 程 设 计 得 很 好 。 连 我 作 为 外 国 人 都 能 明 白 。 而 且 ， 我 超 级 喜 欢 老 师 讲 课 的 方 式 ， 很 清 晰 ， 很 有 逻 辑 ， 很 清 楚 。 谢 谢 你 们 给 我 们 学 习 P y t h o n 的 机 会 。,O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1
老 师 讲 解 的 很 透 彻 ， 课 程 安 排 的 也 很 科 学 ， 但 是 我 就 是 为 什 么 按 照 P P T 原 样 输 入 的 代 码 总 是 提 示 出 现 语 法 错 误 。,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 棒 每 个 知 识 点 都 很 详 细 ， 没 一 块 儿 内 容 老 师 讲 解 很 到 位 。 非 常 感 谢 老 师 ！,O O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
真 的 真 的 太 好 学 习 了 ， 系 统 的 梳 理 了 一 遍 P y t h o n 基 础,O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2
嵩 天 老 师 的 课 思 路 清 晰 ， 有 料 有 挑 战 ， 很 棒 ！,B-ASP I-ASP I-ASP I-ASP O B-ASP B-ASP I-ASP O O O O B-ASP O B-ASP I-ASP O O O O,1 1 1 1 -1 2 2 2 -1 -1 -1 -1 2 -1 2 2 -1 -1 -1 -1
这 个 课 程 很 好 ， 嵩 天 老 师 讲 的 也 很 不 错,O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 课 有 一 种 让 人 想 继 续 听 想 继 续 学 的 感 觉,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
通 俗 易 懂 ， 并 且 让 人 有 动 力 学 下 去 ， 感 觉 自 己 棒 棒 的,O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 安 排 科 学 严 谨 ， 老 师 讲 授 生 动 有 趣 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 讲 解 细 致 、 耐 心 ， 老 师 辛 苦 了 ！,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 教 学 很 棒 ， 但 是 觉 得 自 己 挺 笨 的,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 解 很 通 透 ， 基 本 都 是 干 活 ， 用 时 少 ， 效 果 好 ， 值 得 推 广 大 家 都 学 习 ， 提 升 自 动 化 的 一 条 正 确 之 路 ， 感 谢 嵩 老 师 的 讲 授 。,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O,2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 -1 2 2 -1
老 师 讲 的 很 好 很 清 楚 ， 逻 辑 结 果 也 很 好 ， 非 常 开 心 可 以 接 触 到 老 师 的 课 程,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2
老 师 的 讲 解 风 格 与 讲 解 逻 辑 是 很 清 晰 很 容 易 接 受 的 。,B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,1 1 -1 1 1 2 2 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 详 细 ， 单 是 还 是 会 有 些 跳 过 的 细 节 不 理 解 含 义 。,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
老 师 的 课 讲 得 非 常 精 彩 ， 课 程 设 计 很 用 心 ， 非 常 喜 欢 。,B-ASP I-ASP O B-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O,1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
简 单 易 懂 ， 介 绍 清 晰 ， 循 序 渐 进 ， 引 人 入 胜 ， 激 发 好 奇 。,O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
非 常 好 的 课 程 ， 老 师 的 讲 解 很 清 晰 、 很 有 条 理 。,O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
嵩 天 老 师 讲 的 很 清 晰 很 精 彩 ， 是 见 过 的 最 优 秀 的 编 程 教 学,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
不 多 说 ， 我 1 8 年 遇 到 的 最 良 心 的 课 程 没 有 之 一,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
讲 演 结 合 ， 还 有 练 习 ， 非 常 适 合 0 基 础 的 初 学 者 入 门 p y t h o n,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 1 1 1 1 1 1
嵩 天 老 师 讲 的 很 好 ， 思 路 清 晰 ， 知 识 细 致 。,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1
我 很 喜 欢 这 门 课 程 ， 也 感 谢 老 师 的 辛 苦 付 出 。,O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1
设 计 的 很 科 学 ， 每 次 学 的 时 候 都 不 会 想 停 下 来 的 感 觉,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 太 有 才 了 ， 相 见 恨 晚 ， 好 好 学 习 吧,B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
断 续 续 的 学 习 ， 觉 得 有 很 多 的 语 法 记 不 住 ， 因 为 没 用 心,O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
能 够 更 加 深 刻 的 理 解 p y t h o n,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
非 常 好 ， 老 师 讲 得 很 清 楚 。 人 也 很 帅 ！,O O O O B-ASP I-ASP O O O O O O B-ASP O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1
可 以 很 好 的 学 习 自 己 感 兴 趣 的 课 程 ， 很 方 便 ！,O O O O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
对 于 编 程 小 白 挺 友 好 的 ~ 会 继 续 学 下 去 的,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O,-1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 好 好 啊 ， 我 爱 这 个 课 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1
老 师 讲 的 细 致 易 懂 对 初 学 者 来 说 非 常 棒,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 非 常 具 体 ， 这 本 课 不 是 内 容 最 全 的 ， 但 是 非 常 系 统 ， 有 利 于 大 家 打 好 基 础 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
很 好 啊 ！ 正 在 学 习 ， 只 不 过 时 间 有 限 。,O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 于 小 白 来 说 ， 虽 然 有 点 难 ， 但 我 有 信 心 学 好 这 么 课 程 ！ 有 这 么 们 一 门 P y t h o n 课 程 ， 简 直 是 我 们 的 福 利 呀 ~,O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 的 很 客 观 ， 到 位 ， 能 学 到 东 西 ， 感 谢 老 师 的 付 出,O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2
条 理 清 晰 ， 通 俗 易 懂 ， 理 论 与 实 践 相 结 合 ， 帮 助 学 生 快 速 掌 握,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
课 程 深 入 浅 出 ， 容 易 理 解 ， 易 使 人 掌 握 所 讲 知 识 ， 是 不 可 多 得 的 好 课 程 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 个 课 程 组 织 既 有 基 础 知 识 介 绍 ， 又 通 过 有 趣 实 例 的 讲 解 ， 以 及 方 法 论 和 举 一 反 三 ， 深 入 浅 出 ， 真 不 愧 为 国 家 精 品 课 程 。,O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1
嵩 天 老 师 讲 解 的 太 好 了 ， 循 序 渐 进 ， 实 践 与 理 论 相 统 一 ， 非 常 感 谢 ~,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 的 很 好 ， 例 题 很 有 代 表 性 和 启 发 性 ， 感 谢 嵩 老 师 的 付 出 ！,B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 -1 2 2 2 -1 -1 -1 2 2 2 -1 2 2 -1
"对 初 学 者 来 说 , 挺 好 的 , 简 洁 易 懂",O B-ASP I-ASP I-ASP O O O O O O O O O O O,-1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
P y t h o n 新 手 3 群 【 8 8 1 2 2 8 5 8 1 】 群 内 有 安 装 包 和 学 习 视 频 资 料 ， 零 基 础 ， 进 阶 ， 实 战 免 费 的 在 线 直 播 免 费 课 程 ， 希 望 可 以 帮 助 你 快 速 了 解 P y t h o n ， 欢 迎 加 入 群 获 取 永 久 免 费 听 课 权 限,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP,1 1 1 1 1 1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 2 2 2 2
课 程 很 好 教 学 很 棒 我 们 要 加 油 好 好 学 习,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 课 风 格 太 喜 欢 了 ， 因 为 学 业 需 要 来 自 学 p y t h o n ， 以 前 有 过 一 点 编 程 基 础 ， 现 在 听 老 师 讲 课 快 要 爱 上 p y t h o n 了 ， 一 定 可 以 学 得 很 好 的 ！,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"易 懂 扎 实 的 入 门 课 程 , 非 常 感 谢",O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
很 好 的 一 门 课 程 ， 课 程 由 浅 入 深 ， 条 例 清 晰 ， 让 人 可 以 在 短 的 时 间 内 很 好 的 理 解 知 识 。,O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
感 觉 老 师 太 厉 害 了 ， 而 且 很 帅 。 大 智 若 愚 的 感 觉,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
一 直 想 学 好 编 程 ， 无 奈 很 多 课 程 太 垃 圾 了 ， 无 法 让 我 提 起 兴 趣 也 g e t 不 到 我 的 点 。 这 个 课 程 简 直 精 品 ， 没 有 拖 泥 带 水 ， 每 个 程 序 实 例 都 非 常 经 典 ， 受 益 匪 浅 ， 这 个 课 程 让 我 有 了 深 厚 的 代 码 计 算 机 思 维 基 础 ， 感 谢 老 师,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 -1 -1 -1 2 2
嵩 天 老 师 的 讲 课 真 的 很 能 让 受 众 接 受 ， 专 业 又 易 懂 ， 跟 着 学 下 来 ， 收 获 颇 多 ， 而 且 课 程 的 准 备 确 实 是 挺 符 合 认 知 。 后 面 还 会 继 续 跟 着 学 习 同 一 系 列 p y t h o n 的 应 用 课 程 。,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 2 2 2 2 -1
老 师 讲 得 非 常 有 逻 辑 并 且 细 致 。 获 益 匪 浅,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 种 线 上 授 课 形 式 非 常 好 ， 为 想 学 习 P Y T H O N 的 小 白 提 供 了 很 好 的 学 习 机 会 ， 老 师 授 课 非 常 棒 ， 讲 得 很 清 楚 ， 易 学 易 懂 。,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 好 ， 主 要 是 能 让 我 练 一 下 手 ， 而 不 至 于 是 光 学 不 练,O O O O O O O O O B-ASP O O I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 由 短 视 频 组 成 ， 不 易 产 生 疲 劳 感 。 每 章 都 有 复 习 与 概 述 ， 学 起 来 很 轻 松 。 例 子 ， 扩 展 知 识 精 挑 细 选 ， 不 仅 仅 只 是 教 学 p y t h o n ， 也 在 潜 移 默 化 的 培 养 好 的 思 想 与 习 惯 ， 鼓 励 我 们 坚 持 下 去 ， 比 如 天 天 向 上 的 例 子 ， 感 受 到 了 老 师 的 良 苦 用 心 ！ 这 是 我 迄 今 为 止 最 满 意 的 一 门 网 课 ， 感 谢 老 师 ！,O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 2 2 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
每 个 知 识 点 讲 解 清 晰 易 懂 ， 能 构 筑 起 对 P Y T H O N 这 门 语 言 最 基 础 、 最 重 要 的 知 识 框 架 体 系,O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2
老 师 讲 得 特 别 好 ， 很 推 荐 入 门 很 不 错,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
理 论 与 实 践 结 合 ， 留 有 余 地 ， 给 以 创 新 ， 想 象 时 间 空 间,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP,2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1
老 师 很 耐 心 。 语 言 清 晰 。 听 的 很 清 楚 。 从 2 5 2 全 面 了 解 了 P Y T H O N 。 从 起 始 到 发 展 到 如 何 学 习 再 到 很 多 第 三 方 库 。 嵩 老 师 都 一 一 讲 了 很 多 。 感 谢 嵩 老 师 感 谢 北 京 理 工 大 学 的 教 师 团 体 。,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 1 1 -1 1 1 -1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 2 2 2 2 -1 2 2 2 2 -1
老 师 讲 的 好 ， 学 习 进 步 快 ， 课 下 再 实 践 ， 学 会 没 问 题 。,B-ASP I-ASP O O O O O O O O O O B-ASP O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 好 ， 形 式 设 计 得 也 容 易 入 手,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
电 子 视 频 材 料 很 好 ， 讲 课 深 入 浅 出 ， 知 识 和 案 例 结 合 容 易 懂 。 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O,2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1
掌 握 计 算 机 思 维 ， 对 事 物 的 理 解 更 清 晰,O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O,-1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 很 耐 心 ， 没 有 错 过 细 节 ， 还 有 知 识 点 采 用 引 导 教 学 ， 不 死 板 ， 通 俗 易 懂 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 太 棒 啦 ！ 翁 恺 老 师 是 我 的 C 语 言 启 蒙 老 师 ， 嵩 天 老 师 是 我 的 P y t h o n 启 蒙 老 师 ， 真 的 感 谢 中 国 大 学 M O O C ， 在 这 里 认 认 真 真 学 ， 真 的 可 以 学 到 很 多 新 东 西 。,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 1 1 1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
非 常 好 ， 比 学 校 老 师 讲 的 好 太 多 了,O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
老 师 讲 的 真 的 很 好 ~ ~ ！ 要 是 当 年 老 师 这 么 讲 我 会 觉 得 代 码 很 有 趣 . . . ( * ^ ▽ ^ * ),B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 但 感 觉 视 频 分 段 有 点 多 呀,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 老 师 讲 的 太 好 啦 ， 让 我 这 个 几 度 学 习 p y t h o n 却 学 不 下 去 的 小 白 ， 终 于 学 进 去 了 ， 感 谢 老 师 ， 会 继 续 学 习 的 ， 等 学 完 这 门 课 ， 去 学 习 下 老 师 的 其 他 课 程 。,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1
老 师 讲 的 不 错 ， 循 序 渐 进 ， 夯 实 基 础 ！,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
学 了 很 多 课 了 ， 这 是 最 开 心 的 一 课 ， 满 足 了 我 对 课 程 学 习 的 最 高 期 待 。 理 由 ： 1 . 老 师 科 研 、 教 育 都 拿 手 ， 知 识 点 学 和 应 用 信 手 拈 来 ， 学 有 所 终 ； 2 . 老 师 讲 课 不 疾 不 徐 ， 体 系 完 整 。 甚 至 课 前 、 课 后 、 复 习 、 习 题 . . . . 事 无 巨 细 ， 一 个 完 整 的 授 课 模 板 ； 3 . 老 师 科 研 应 用 老 道 ， 基 础 知 识 的 学 习 及 其 成 长 路 径 一 目 了 然 ， 激 发 了 学 习 的 纵 深 感 ； 4 . 老 师 的 讲 课 内 容 共 字 里 行 间 抒 发 了 对 学 生 的 期 待 和 祖 国 的 热 爱 ， 课 程 思 政 潜 移 默 化 渗 透 期 间,O O O O B-ASP O O O O O O O O O B-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 2 2 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 1 1 -1 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
让 我 入 门 P y t h o n 语 言 。 期 望 能 出 比 较 系 统 和 全 面 的 计 算 机 二 级 P y t h o n 语 言 教 程 。 目 前 的 P y t h o n 二 级 教 程 缺 失 视 频 课 程 的 重 点 知 识 点 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 -1 0 0 0 0 0 -1
我 能 通 过 编 写 一 些 代 码 解 决 工 作 上 遇 到 的 问 题 了 。,O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1
课 程 设 置 很 合 理 ， 同 时 由 易 到 难 ， 比 较 好 接 受,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 形 象 ， 语 言 有 逻 辑 ， 辛 苦 啦 ！,B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 非 常 仔 细 ， 例 子 清 晰 易 懂,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
能 体 会 到 老 师 的 用 心 ， 内 容 有 些 地 方 挺 暖 心 的,O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 2 2 -1 2 2 -1 -1 2 2 -1 -1 -1 -1
11 月 左 右 学 习 的 ， 那 时 候 每 天 早 上 都 要 学 p y t h o n ， 嵩 老 师 讲 的 很 基 础 ， 比 较 容 易 听 懂 ， 但 是 如 果 想 真 正 掌 握 这 门 程 序 设 计 语 言 ， 我 还 需 要 自 己 再 多 多 下 功 夫 学 习 和 实 践 。 这 是 我 用 M O O C 拿 到 证 书 的 第 一 门 课 程 ， 非 常 感 谢 北 理 各 位 老 师 和 同 学 的 付 出 ， 你 们 做 的 很 棒 ！,O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
知 识 点 和 编 程 思 维 讲 得 十 分 清 楚 ， 例 题 选 择 很 精 当 ， 循 序 渐 进 ， 易 于 掌 握 。,B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O,2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1
很 喜 欢 课 程 的 组 织 形 式 ， 有 基 础 知 识 ， 有 实 例 探 究 ， 课 程 进 度 合 理 ， 学 习 很 高 效 ！,O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 -1 2 2 -1 2 2 2 2 -1 -1 1 1 1 1 -1 -1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
嵩 天 老 师 实 例 化 教 学 ， 课 程 设 计 的 非 常 棒,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 2 2 2 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1
老 师 讲 得 很 好 ， 对 p y t h o n 有 以 初 步 认 识 ， 争 取 学 好 ， 加 油 ！,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 清 晰 ， 细 的 知 识 点 都 讲 得 清 楚 明 白,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1
内 容 丰 富 、 授 课 方 式 新 颖 ， 练 习 与 作 业 设 计 合 理,B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O,2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1
生 动 有 趣 ， 带 我 这 个 小 白 p y t h o n 入 门 ， 一 窥 不 一 样 的 世 界,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 的 课 程 由 浅 入 深 ， 即 全 面 又 重 点 突 出 。,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
练 习 很 充 分 ， 基 本 不 需 要 课 外 练 习,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0
老 师 讲 得 很 好 ， 但 是 零 基 础 还 是 很 困 难 ， 作 业 的 很 多 函 数 的 用 法 都 是 需 要 自 己 去 查 找 的 ， 然 后 因 为 不 懂 还 是 不 能 很 好 的 使 用 。 建 议 如 果 可 以 对 于 每 个 习 题 中 可 能 要 用 到 的 语 法 ， 函 数 都 有 专 门 的 配 套 详 细 说 明 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 2 2 -1 2 2 2 2 2 2 -1
内 容 秩 序 渐 进 ， 实 例 针 对 性 强 ， 并 有 举 一 反 三 不 断 提 升 自 己 ， 感 觉 学 习 起 来 不 困 难 ， 很 快 就 能 掌 握 ， 希 望 能 通 过 学 习 帮 助 自 己 利 用 p y t h o n 编 程 来 解 决 自 己 工 作 上 碰 到 的 问 题 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
评 价 内 容 至 少 输 入 5 个 字 评 价 内 容 至 少 输 入 5 个 字,O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
授 课 方 式 好 ， 为 终 身 学 习 提 供 了 平 台 ， 没 有 机 会 进 入 名 牌 大 学 也 能 听 到 名 校 老 师 的 课,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 1 1 -1 1
讲 的 很 清 晰 ， 详 细 ， 也 有 例 子 ， 容 易 结 合 例 子 更 好 地 理 解,B-ASP O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
P y t h o n 比 我 当 年 学 的 B a s i c 、 F o r t r a n 语 言 简 单 、 强 大 ； 老 师 讲 解 地 很 清 楚 。 赞 ！,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 深 入 浅 出 ， 有 条 理 ， 非 常 容 易 懂,B-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 于 还 在 入 门 探 索 的 我 有 点 难 度 ， 只 能 加 油 了,O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
课 程 逻 辑 清 晰 ， 知 识 架 构 清 楚 ， 且 难 度 适 合 初 学 者 。,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 教 的 还 是 挺 不 错 的 ， 只 是 我 加 入 课 程 较 晚 ， 课 程 老 早 就 结 束 了 ， 现 在 就 是 想 先 学 一 下 加 深 对 P y t h o n 语 言 的 了 解,B-ASP I-ASP B-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1
我 是 业 余 爱 好 者 ， 5 月 份 才 发 现 嵩 老 师 的 课 程 ， 感 谢 嵩 老 师 的 无 私 授 课 ， 见 识 到 了 高 水 平 教 学 ， 赞 赞 赞 ~,O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
通 过 案 例 分 析 讲 解 P y t h o n 基 础 语 句 ， 简 明 易 懂 ， 理 解 起 来 十 分 轻 松 ！,O O B-ASP I-ASP O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 1 1 -1 -1 2 2 1 1 1 1 1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 简 单 明 了 ， 由 浅 入 深 ， 值 得 学 习 。,B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 学 习 节 奏 紧 凑 ， 易 于 接 受 ！ 棒,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
"很 好 ！ 内 容 丰 富 ， 形 式 新 颖 ， 表 达 清 晰 ， 知 识 系 统 , ， 准 备 充 分 ， 团 队 强 大 。",O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O,-1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1
课 程 简 洁 明 了 ， 讲 解 丰 富 生 动 ， 是 一 门 不 可 多 得 的 精 品 课 程 ！,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
太 可 了 ~ ~ ~ 学 得 非 常 仔 细 了 。 逻 辑 清 楚 。,O O O O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
课 程 设 置 合 理 ， 配 合 教 材 ， 棒 棒 的,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
基 础 介 绍 的 很 全 面 ， 给 p y t h o n 学 习 开 了 个 好 头,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP,1 1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 2 2
老 师 深 入 浅 出 ， 让 我 这 个 小 白 也 跟 完 了 ， 配 合 习 题 理 解 更 多 ， 马 上 去 找 嵩 天 老 师 的 进 阶 课 程 去 了,B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2 2 2 -1 -1
操 作 演 示 没 有 语 音 讲 解 说 明 ， 屏 幕 比 较 小 ， 有 时 候 具 体 点 击 了 哪 个 按 钮 看 不 清 楚 ， 建 议 以 后 录 视 频 时 采 用 局 部 放 大 功 能,B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,0 0 0 0 -1 -1 0 0 0 0 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 1 1 1 1 1 1
课 程 很 好 学 到 很 多 技 能 ， 但 还 是 希 望 看 到 很 多 免 费 公 开 的 课 程,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
还 好 吧 ， 希 望 课 程 时 间 单 节 加 长 点 。,O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 1 1 -1 -1 -1 -1
课 程 很 棒 ， 老 师 讲 解 详 细 ， 收 获 很 多 ， 推 荐,B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1
不 抽 时 间 复 习 和 练 习 实 例 的 话 ， 要 掌 握 本 课 程 的 知 识 还 是 很 难 的,O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 0 0 -1 -1 -1 -1 -1
北 理 毕 业 生 ， 老 师 讲 解 十 分 细 致 ，,B-ASP I-ASP B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O,2 2 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
P y t h o n 1 2 3 平 台 设 计 不 错,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O,1 1 1 1 1 1 1 1 1 2 2 2 2 -1 -1
之 前 学 习 过 基 础 课 程 ， 现 在 在 听 一 次 巩 固 巩 固 挺 好 的,O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
先 购 买 了 嵩 天 老 师 的 书 来 看 ， 然 后 再 学 习 ， 效 果 不 错 。,O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 2 2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
授 课 深 入 浅 出 ， 对 课 程 的 定 位 很 清 晰 ， 尤 其 适 合 初 学 者 ， 真 正 的 做 到 了 m o o c,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
老 师 讲 课 非 常 清 晰 ， 举 例 生 动 ， 课 后 举 一 反 三 可 以 深 入 理 解,B-ASP I-ASP O O O O O O O O B-ASP O O O B-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 清 晰 ， 逻 辑 性 强 ， 每 个 视 频 短 小 精 悍 ， 增 强 了 学 习 成 就 感,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP,1 1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2
老 师 讲 的 特 别 好 。 p p t 做 的 也 很 好 ， p y t h o n 1 2 3 很 好 用,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 -1 -1 -1
挺 好 ， 多 年 没 有 听 这 么 专 业 的 课 了 ， 老 师 的 课 件 组 织 逻 辑 清 晰 ， 严 密 ， 深 入 浅 出 ， 通 俗 易 懂 ， 也 比 较 有 趣 ， 谢 谢 老 师 的 精 彩 讲 解 ！,O O O O O O O O O O O O O B-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1
课 程 的 知 识 特 别 易 懂 ， 蒿 老 师 讲 的 特 别 特 别 好,B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 讲 解 能 够 学 懂 ， 感 谢 嵩 天 老 师 。,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
讲 的 很 透 彻 ， 对 于 初 学 者 来 说 能 学 到 很 多 东 西 。,B-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O,2 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
已 经 有 了 p y t h o n 使 用 经 历 ， 但 是 老 师 的 课 程 还 是 非 常 的 实 用 。 打 好 基 础 ， 弥 补 之 前 基 础 知 识 框 架 的 漏 洞 。 非 常 推 荐 大 家 学 习,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 丰 富 实 用 ， 嵩 老 师 讲 解 通 俗 易 通 ， 赞 ！ ！ ！,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 生 动 有 趣 ， 课 件 制 作 精 致 ， 实 例 丰 富,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1
p y t h o n 很 强 大 ， 值 得 学 习 ， 老 师 讲 的 好 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1
老 师 带 我 入 门 了 P y t h o n ， 我 要 继 续 学 习 老 师 的 其 他 P y t h o n 课 程 ， 立 一 个 f l a g 吧 ， 加 油 ！,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1
勾 起 了 对 p y t h o n 的 向 往 啊 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1
超 级 好 ！ 老 师 讲 课 和 各 种 都 特 别 符 合 我 的 需 求 ， 甚 至 超 出 预 期 ， 就 是 希 望 作 业 老 师 能 够 考 虑 到 我 们 这 些 小 白 ， 不 要 出 超 出 那 周 学 习 内 容 的 编 程 题 Q A Q Q,O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1
内 容 比 较 基 础 ， 但 是 全 面 ， 老 师 讲 的 也 深 入 浅 出 ， 后 期 课 程 推 进 较 快 ， 需 要 多 练 练 才 行 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 组 织 形 式 很 有 自 己 的 想 法 ， 学 完 之 后 也 能 获 得 完 整 的 体 系 ， 很 棒 的 课 。 唯 一 就 是 有 些 小 节 的 内 容 太 短 了 是 否 可 以 考 虑 合 并 呢 ？,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
还 不 错 ， 就 是 有 点 快 ， 举 例 有 点 少,O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 我 觉 得 老 师 课 前 引 入 环 节 做 得 很 好 ， 能 够 很 好 地 启 发 学 生 兴 趣 ， 之 前 在 学 校 上 的 大 学 计 算 机 课 还 有 C 等 课 程 ， 整 堂 课 兴 趣 不 高 ， 所 以 感 谢 老 师 带 来 的 精 彩 授 课 。 我 会 好 好 学 习 的 ！,O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP O B-ASP I-ASP O O O B-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 0 0 0 0 0 0 -1 -1 1 -1 0 0 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 门 课 老 师 讲 的 特 别 好 ， 通 俗 易 懂 ， 超 级 赞 ！,O O B-ASP B-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ！ 我 这 个 超 级 小 白 听 着 很 有 意 思 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
感 谢 ， 讲 的 很 清 晰 ， 很 适 合 零 基 础 的 学 生 入 门 。,O O O B-ASP O O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1
我 是 入 门 级 学 员 ， 嵩 天 老 师 讲 的 很 好 ！,O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1
收 获 很 多 增 强 了 自 己 的 逻 辑 思 考 能 力,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2
老 师 用 实 例 来 进 行 语 法 的 讲 解 ， 很 容 易 掌 握,B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O,2 2 -1 1 1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1
基 础 知 识 讲 解 得 很 详 细 易 懂 ， 就 是 希 望 老 师 给 多 点 o j 题 练 习 。,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 2 2 2 -1
确 实 是 一 门 很 基 础 扎 实 的 入 门 级 ， 对 于 从 没 有 接 触 过 编 程 的 人 来 说 ， 竟 然 也 可 以 听 懂 。 这 是 很 不 错 的 。 唯 一 感 到 有 些 遗 憾 的 是 ， 感 觉 老 师 还 能 讲 的 更 详 细 些 ， 多 讲 一 些 关 于 编 程 的 一 些 常 识 类 的 东 西 。 因 为 有 些 东 西 ， 如 果 一 点 都 不 清 楚 的 话 ， 上 手 出 现 的 问 题 往 往 都 是 想 不 到 的 。 这 里 再 次 感 谢 嵩 天 老 师 的 辛 苦 教 学 。 最 后 附 上 一 句 老 师 常 说 的 话 p r i n t ( ' 人 生 苦 短 ， 我 学 p y t h o n ' ),O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1
讲 的 特 别 清 楚 ， 感 觉 都 很 容 易 听 明 白 ， 太 棒 了 ！ ！ ！,O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 课 程 设 置 合 理 ， 丰 富 ， 搭 配 老 师 搭 建 的 P y t h o n 1 2 3 平 台 使 用 受 益 良 多,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
太 棒 了 ， 可 以 跟 着 老 师 系 统 地 学 习 啦 ！,O O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
v e r y v e r y g o o d,O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
由 浅 入 深 并 拓 展 ， 收 获 颇 多 ， 适 合 新 手 。,O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 的 非 常 棒 ， 学 习 很 多 ， 收 获 很 多 ， 每 个 知 识 点 讲 解 非 常 到 位 细 致 ， 打 算 继 续 听 老 师 讲 的 其 他 课 程 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 -1
老 师 讲 解 的 很 清 楚 ！ 案 例 很 合 适 ！,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
既 有 实 例 又 有 充 分 的 理 论 知 识 ， 学 习 过 程 很 愉 快,O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 1 1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1
在 学 习 过 程 中 ， 我 需 要 不 断 地 思 考 和 分 析 ， 才 能 理 解 和 掌 握 知 识 。 这 培 养 了 我 逻 辑 思 维 能 力 和 解 决 问 题 的 能 力 ， 让 我 能 够 在 遇 到 问 题 时 ， 能 够 迅 速 理 清 思 路 ， 找 到 解 决 办 法,O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2
非 常 好 的 平 台 ， 万 分 感 谢 嵩 老 师 的 精 彩 授 课 ！ 带 我 感 受 到 编 程 的 乐 趣 和 编 程 发 展 的 新 视 野 ！ 努 力 学 习 。 。 。 。 。 。,O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 1 1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 太 好 了 ！ 作 为 p y t h o n 入 门 课 ， 真 心 不 错 。 每 周 课 后 的 练 习 做 完 ， 对 于 巩 固 课 堂 内 容 挺 有 帮 助 。,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1
内 容 充 实 ， 讲 解 清 晰 ， 学 习 轻 松 愉 快 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1
嵩 老 师 讲 得 太 好 了 。 将 一 门 专 业 性 极 强 的 课 程 用 简 洁 的 语 言 、 新 颖 的 表 达 ， 轻 快 而 流 利 的 讲 授 。 让 学 生 轻 松 有 趣 地 投 入 学 习 ， 自 发 自 愿 的 跟 着 学 习 。 嵩 老 师 是 中 国 第 一 好 先 生 ！,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 2 2 2 2 -1
老 师 讲 的 生 动 有 趣 ， 非 常 棒 ， 会 继 续 跟 下 去 的 ！,O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"2 0 1 9 难 忘 的 夏 天 p r i n t ( "" h e l l o w o r l d "" )",O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 2 2 1 1 1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1
嵩 天 老 师 讲 课 深 入 浅 出 ， 把 很 多 基 本 原 理 给 解 释 清 楚 了 ， 而 不 仅 仅 教 你 怎 么 去 应 用 ， 这 点 很 重 要 ， 很 多 课 程 这 点 做 的 不 够 好 。 希 望 嵩 天 老 师 有 时 间 的 话 ， 可 以 再 开 一 个 课 程 ， 讲 解 一 下 p y t h o n 高 级 用 法 ， 例 如 面 向 对 象 ， 生 成 器 ， 装 饰 器 等 ， 自 学 的 时 候 感 觉 不 太 好 理 解 。 谢 谢 嵩 老 师,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP,2 2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2
感 谢 嵩 老 师 及 团 队 的 辛 苦 付 出 ！ ！ ！ 上 学 期 间 没 机 会 接 触 慕 课 ， 这 是 我 学 习 的 第 一 门 慕 课 ， 老 师 讲 的 很 好 ， 特 别 是 课 程 的 设 计 编 排 很 好 ， 就 像 老 师 讲 的 那 样 ， 很 注 重 用 户 体 验 ！ 对 于 P y t h o n 我 有 种 相 见 恨 晚 的 感 觉 ， 感 谢 老 师 让 我 可 以 看 到 更 大 的 P y t h o n 世 界 ！ 我 会 继 续 努 力 ！ 也 希 望 这 门 课 在 后 续 的 版 本 中 越 来 越 完 美 ！ 加 油 ！,O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP O O O O O O O O O O O O O O O O,-1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
前 面 讲 的 很 清 楚 ， 到 后 面 节 奏 有 加 快 ， 听 起 来 比 较 吃 力 。,O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
快 考 试 了 ！ 抱 佛 脚 ！ 平 心 而 论 ， 老 师 讲 的 不 错,O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O,-1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
讲 的 详 细 ， 适 合 我 这 样 的 小 白,B-ASP O O O O O O O O O O B-ASP I-ASP,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
简 单 易 懂 让 我 领 略 到 了 P y t h o n 简 约 之 美,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2
老 师 讲 的 超 级 棒 ！ 让 我 一 个 小 白 慢 慢 喜 欢 上 了 编 程 ！,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 解 清 楚 、 难 度 不 大 ， 学 习 轻 松 ， 讲 解 内 容 很 能 激 发 我 的 学 习 兴 趣,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP,1 1 2 2 -1 -1 -1 0 0 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2
老 师 讲 得 很 好 就 是 对 于 一 个 小 白 来 说 有 些 细 节 方 面 不 太 好 理 解 望 自 己 继 续 努 力 不 要 放 弃,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
太 棒 了 ， 学 到 许 多 有 趣 的 知 识 。 自 己 在 学 习 中 探 索 也 很 有 成 就 感,O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2
g o o o o o o o o o o o o o d,O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 清 晰 易 懂 ， 逻 辑 ， 结 构 清 晰 ， 学 到 很 多,B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 细 致 ， 初 学 者 表 示 老 师 真 的 太 棒 了,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1
非 常 棒 ！ 课 程 内 容 精 简 而 不 失 水 准 ， 有 其 他 语 言 基 础 的 ， 几 乎 一 整 天 就 能 刷 完 ， 能 够 短 时 间 内 实 现 P y t h o n 入 门,O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2
老 师 教 的 很 好 ， 把 基 础 打 得 很 牢 实,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ， 声 音 抑 扬 顿 挫 也 让 人 很 舒 服 ， 难 得 的 好 课,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2
讲 解 系 统 ， 生 动 ， 条 理 清 晰 ， 学 习 顺 利,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1
课 程 精 细 ， 好 学 易 懂 ， 关 键 还 免 费 ！ 为 M O O C 平 台 点 赞 ！ ！ ！,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 太 好 了 ， 简 单 明 了 ， 让 我 有 了 一 直 学 下 去 的 动 力,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
"授 课 方 式 让 我 们 觉 得 很 全 面 , 又 通 俗 易 懂 , 且 会 引 导 我 们 去 扩 展 了 解 更 多 的 知 识 .",B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
嵩 天 老 师 讲 的 很 清 晰 ， 我 也 是 学 有 所 得 嵩 天,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2
老 师 讲 的 比 较 清 晰 ， 不 想 别 的 老 师 那 样 就 会 念 P P T ， 老 师 会 给 你 分 析 其 中 的 一 些 问 题 ， 同 时 有 时 候 也 会 和 同 学 一 起 编 写 代 码 。,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
课 程 体 系 很 完 整 ， 老 师 讲 解 也 比 较 耐 心 。,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1
不 错 的 课 程 ， 老 师 讲 解 的 也 很 详 细,O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O,-1 -1 -1 2 2 -1 1 1 2 2 -1 -1 -1 -1 -1
受 益 匪 浅 ， 感 谢 提 供 给 我 这 样 无 法 在 线 下 成 为 名 校 学 生 这 样 一 次 机 会,O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 1 1 -1 -1 -1 -1 2 2
"p r i n t ( "" 超 级 棒 "" )",B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
案 例 非 常 合 适 ， 难 度 也 不 大 ， 有 些 算 法 很 巧 妙 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
课 程 制 作 安 排 非 常 棒 ！ ！ ！ M O O C 就 是 时 代 给 我 们 的 礼 物 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
嵩 老 师 讲 得 非 常 好 ， 每 节 课 举 的 实 例 也 很 到 位 。 给 4 颗 星 ， 相 信 还 有 提 高 的 空 间 。,B-ASP I-ASP I-ASP O O O O O O O O B-ASP O O B-ASP I-ASP O O O O O O O O B-ASP O O O O O O O O B-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
学 习 的 很 轻 松 ， 嵩 老 师 讲 课 深 入 浅 出 、 很 容 易 理 解 。,O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 们 课 程 让 我 觉 得 学 习 ， P Y t h o n 十 分 有 趣,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
嵩 老 师 知 识 渊 博 ， 课 讲 得 深 入 浅 出 ， 让 我 体 会 到 了 编 程 的 乐 趣 ， 非 常 感 谢 ！,B-ASP I-ASP I-ASP O O O O O B-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O,2 2 2 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
嵩 天 老 师 的 课 程 讲 述 很 细 致 ， 实 例 也 比 较 有 趣,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O O O O O,1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
感 觉 这 个 的 难 度 是 不 适 合 第 一 次 接 触 编 程 的 人 ， 最 好 是 学 过 C 或 者 接 触 过 p y t h o n 的 或 者 其 他 编 程 语 言 。 课 程 安 排 有 点 点 混 乱 （ 仅 在 我 看 来 ） ， 不 过 课 程 内 容 还 是 不 错 的 ， 很 好 。 深 入 学 习 p y t h o n 的 话 学 这 门 课 程 挺 好 的 。,O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
以 视 频 的 方 式 授 课 过 程 中 可 能 会 遇 到 一 点 疑 惑 困 扰 ， 但 整 体 感 觉 良 好,O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O,-1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 2 2 2 2 -1 -1
理 论 和 案 例 结 合 让 课 程 更 加 有 趣 ， 对 新 手 也 很 友 好,B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O,2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
特 别 棒 ！ 讲 得 通 俗 易 懂 ， 教 学 规 划 和 实 例 够 新 颖 ， 还 有 练 习 题 和 作 业 ， 有 c 语 言 基 础 花 一 周 通 关 不 是 问 题 ， 绝 对 是 高 质 量 极 速 入 门 系 列 P y t h o n 教 学 ！ 这 么 多 好 评 果 然 不 是 盖 的 ~,O O O O B-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
对 于 非 计 算 机 专 业 的 同 学 也 很 友 好 。,O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 细 致 ， 需 要 花 时 间 好 好 消 化 一 下 。 零 基 础 放 心 冲 ！,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1
太 喜 欢 了 ， 老 师 讲 得 非 常 仔 细 ， 然 后 形 式 也 非 常 有 意 思,O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
1 . 课 程 整 体 涉 及 生 动 有 趣 ， 结 构 化 强 ， 便 于 新 学 者 掌 握 2 . 每 单 元 的 课 前 复 习 ， 加 深 巩 固,O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
老 师 的 课 真 的 是 很 棒 ， 讲 的 例 子 也 很 好 。,B-ASP I-ASP O B-ASP O O O O O O O O B-ASP I-ASP O O O O,1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
这 个 课 程 很 有 意 思 ， 承 前 启 后 ； 对 未 来 p y t h o n 学 习 有 很 大 帮 助,O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 2 2
课 件 简 洁 明 了 ， 老 师 讲 的 也 很 清 楚,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
这 个 课 程 的 设 计 很 有 意 思 ， 趣 味 入 门 到 逐 步 引 入 各 种 问 题 ， 通 过 思 考 这 些 问 题 ， 对 我 们 理 解 这 门 课 程 ， 学 习 相 关 的 知 识 非 常 有 帮 助 ， 而 且 资 料 也 很 全 面 ， 在 课 上 听 不 太 懂 的 回 过 头 通 过 资 料 看 ， 理 解 ， 尝 试 ， 再 一 次 加 深 了 各 种 知 识 的 理 解 及 运 用 。,O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1
1 8 年 学 过 嵩 老 师 的 课 ， 时 隔 三 年 基 础 语 法 都 忘 完 了 ， 快 速 过 一 遍 很 快 就 回 来 了 ， 嵩 老 师 还 是 一 如 既 往 的 帅 哈 哈 哈,O O O O O B-ASP I-ASP I-ASP O B-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 1 1 1 -1 2 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 简 单 明 了 同 时 又 能 引 发 思 考 ， 非 常 喜 欢 这 样 的 课 程 ， 虽 然 我 之 前 学 习 c 语 言 时 留 下 了 阴 影 ， 但 是 这 一 次 p y t h o n 的 学 习 我 又 提 起 了 兴 趣 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 设 置 合 理 ， 讲 解 基 础 规 范 。 总 的 来 说 就 是 ： 质 量 高 ， 很 专 业 。,B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 2 2 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
课 程 讲 解 通 俗 易 懂 ， 多 加 练 习 才 是 王 道,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
作 为 第 一 次 接 触 p y t h o n ， 感 觉 还 不 错,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1
感 觉 很 不 错 ， 由 浅 入 深 ， 循 序 渐 进 ， 实 例 也 很 有 趣,O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
有 点 意 思 ， 但 是 动 起 手 来 还 是 有 点 难 度 的,O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
课 程 设 计 合 理 ， 老 师 讲 解 思 路 清 晰 ， 深 入 浅 出 ， 非 常 适 合 P Y T H O N 入 门 的 学 员,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1
"T h a t ' s v e r y g o o d l e s s o n , t e a c h m e a l o t .",O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 非 常 好 的 一 门 课 ， 深 入 浅 出 ， 值 得 每 一 个 人 在 这 门 课 上 付 出 。,O O O O O O O O B-ASP O O O O O O O O O O O O O O O B-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1
学 到 了 很 多 ， 感 谢 老 师 ， 希 望 以 后 还 有 这 样 的 课,O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2
非 常 好 ， 浅 显 易 懂 ， 还 有 实 例 分 析,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
老 师 讲 的 非 常 好 ， 课 程 内 容 也 是 十 分 “ 小 白 ” ， 适 合 像 我 这 种 一 点 编 程 没 有 接 触 过 的 小 白 学 习 ， 最 后 一 句 话 好 好 学 习 ， 天 天 向 上 。,B-ASP I-ASP B-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O,1 1 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 授 课 思 路 清 晰 ， 课 程 安 排 合 理 ， 及 时 复 习 环 节 挺 好 。 课 程 中 理 论 结 合 练 习 ， 课 后 也 提 供 了 许 多 练 习 的 机 会 ， 课 题 组 还 能 及 时 回 答 学 员 学 习 中 碰 到 的 问 题 。 感 谢 老 师 为 我 们 提 供 这 么 优 秀 的 学 习 资 源 ， 谢 谢 ！,B-ASP I-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
太 棒 了 ， 太 牛 逼 了 ， 我 太 喜 欢 了 ， 太 好 了 ， v e r y g o o d ！,O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 课 很 细 致 ， 很 有 条 理 ， 将 语 法 和 实 例 结 合 起 来 避 免 了 “ 纸 上 谈 兵 ” ， 对 于 初 学 者 很 适 合 ， 对 于 有 其 他 程 序 语 言 基 础 的 ， 也 可 以 选 择 倍 速 观 看 ， 很 方 便 。,B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 的 内 容 非 常 好 ， 对 新 手 很 友 好,B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O,1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
收 获 很 大 ， 值 得 付 出 ！ 继 续 加 油 再 接 再 厉 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
真 的 挺 有 趣 ， 比 干 巴 巴 的 讲 知 识 点 强 太 多 了 。 非 常 适 合 没 有 经 验 的 小 白 去 学 习 ！ ！ ！,O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 但 是 写 代 码 的 时 候 查 学 过 的 函 数 和 方 法 不 方 便,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1
教 的 挺 好 ， 实 例 也 很 经 典 ， 但 我 有 部 分 内 容 学 过 了,B-ASP O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O,2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1
可 以 的 我 已 经 学 到 了 很 多 东 西 ！,O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
在 最 近 完 成 的 P y t h o n 课 程 中 ， 我 获 得 了 非 常 宝 贵 的 学 习 体 验 。 这 门 课 程 不 仅 仅 是 一 次 简 单 的 编 程 学 习 ， 它 更 像 是 一 次 深 入 探 索 编 程 世 界 和 自 我 挑 战 的 旅 程 。,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
很 不 错 ， 学 到 东 西 了 ， 而 且 课 后 给 出 的 P P T 很 利 于 学 习 后 笔 记 的 补 充,O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 1 1
老 师 讲 的 很 细 很 系 统 ， 课 程 知 识 导 引 明 晰 ， 很 有 方 向 感 。 与 练 习 网 站 结 合 ， 加 深 理 解 与 实 践,O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2
P y t h o n 小 白 ， 听 了 嵩 天 老 师 的 课 让 我 初 步 有 了 P y t h o n 的 思 维,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,1 1 1 1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2
"带 我 很 轻 松 地 入 门 了 p y t h o n , 并 培 养 了 兴 趣",O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2
老 师 讲 课 的 方 式 生 动 易 懂 ！ 很 喜 欢 上 嵩 天 老 师 课 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP O,1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 2 -1
我 觉 得 老 师 讲 的 很 详 细 也 很 清 楚,O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 组 织 条 理 清 晰 ， 学 习 目 标 明 确 ， 受 益 匪 浅,B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 2 2 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 深 入 浅 出 ， 0 基 础 也 很 好 理 解,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1
老 师 讲 的 很 细 吗 ， 编 程 小 白 也 能 听 明 白,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 收 获 很 大 ， 希 望 我 能 成 为 P y t h o n 高 手 ， 能 熟 练 运 用 各 类 常 用 的 库 及 里 面 的 函 数 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 1 1 -1
很 棒 ， 对 一 门 编 程 语 言 的 利 用 更 加 深 刻 了,O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
是 入 门 级 非 常 好 的 国 家 精 品 课 程 。 感 谢 老 师 们 的 精 彩 讲 解 。,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 1 1 1 -1 -1 -1 2 2 -1
课 程 编 排 合 理 ， 认 真 听 讲 没 基 础 也 可 以 学 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 生 动 有 趣 ， 内 容 设 计 上 环 环 相 扣 ， 由 浅 入 深 ， 带 领 我 们 体 会 p y t h o n 的 魅 力 ， 许 多 实 例 的 设 计 上 也 是 让 我 成 就 感 满 满 。,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
本 科 期 间 学 过 C 语 言 （ 飘 过 的 那 种 ） ， 所 以 基 本 上 也 是 一 个 编 程 小 白 ， 学 这 门 课 一 来 是 想 基 本 了 解 一 种 计 算 机 语 言 ， 拓 展 自 己 的 眼 界 ， 其 次 就 是 希 望 现 在 所 学 对 自 己 以 后 的 发 展 有 帮 助 。 老 师 讲 的 还 是 不 错 的 ， 有 自 己 的 一 套 教 学 思 路 ， 知 识 点 讲 解 、 实 例 讲 解 、 作 业 ， 能 够 帮 助 学 习 者 快 速 掌 握 知 识 ， 但 是 后 面 几 周 的 难 度 慢 慢 有 点 大 了 ， 希 望 自 己 能 坚 持 住 ， 学 完 就 是 胜 利 。,O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 2 -1 2 2 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
细 致 有 趣 ， 学 习 过 程 中 很 有 成 就 感 ！,O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 2 2 2 -1
套 路 很 正 规 ， 深 度 正 常 ， 对 零 基 础 不 友 好 ， 对 野 路 子 很 好 ， 作 业 练 习 真 香 ， 对 学 习 能 力 要 求 高 一 点 ， 强 烈 推 荐 自 学 人 士 学 习,B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1
老 师 讲 的 很 好 ， 通 俗 易 懂 ， 对 于 大 学 学 习 过 c 语 言 的 我 来 说 接 受 起 来 很 容 易 ， 感 谢 老 师 的 精 彩 讲 解,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2
呜 呜 呜 我 的 C 语 言 老 师 要 这 么 讲 我 的 计 概 也 不 会 拉 绩 点 了 2 3 3 3,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
老 师 讲 课 很 好 ， 详 略 得 当 ， 挺 有 意 思 的,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
h a o h a o a o h a h a h a h a o h o h a,O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
满 分 课 程 ， 对 于 初 学 者 来 说 作 为 p y t h o n 入 门 课 程 非 常 合 适 ， 没 有 学 编 程 的 枯 燥 ， 而 是 从 一 个 个 实 例 中 发 现 了 很 多 乐 趣 ， 非 常 感 谢 嵩 老 师 团 队 ， 谢 谢 ！ 受 益 颇 多,O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1
课 程 设 置 循 序 渐 进 ， 课 前 的 复 习 环 节 很 棒,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O,2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1
初 步 了 解 P y t h o n 知 识 ， 感 谢 ~,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1
课 程 不 仅 仅 是 对 理 论 部 分 进 行 讲 解 ， 而 且 穿 插 有 实 例 。 可 以 看 出 ， 实 例 部 分 是 经 过 精 心 挑 选 的 ， 难 度 适 中 ， 但 是 对 知 识 点 的 掌 握 很 有 帮 助 ， 很 赞,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 好 的 一 门 课 ， 很 喜 欢 这 个 老 师 讲 课 的 风 格,O O O O O B-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP,-1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2
挺 好 ， 还 想 继 续 听 嵩 天 老 师 讲 课,O O O O O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 2
挺 好 的 ， 有 实 例 还 可 以 随 时 练 习 ， 感 谢 老 师 和 平 台,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 2 2
对 于 编 程 小 白 来 说 很 友 好 ， 如 果 之 前 有 过 一 定 基 础 ， 有 些 实 例 可 以 选 学,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1
感 觉 慕 课 以 小 节 的 形 式 开 授 很 适 合 我 ， 每 看 完 一 个 视 频 都 能 获 得 成 就 感 。,O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O,-1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 -1
零 基 础 的 ， 从 这 个 入 口 ， 感 觉 很 亲 切 ， 很 实 在 ， 可 以 提 高 信 心 提 高 兴 趣 来 学 习 。,O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1
p y t h o n 相 比 其 它 编 程 相 对 简 单 易 学 ， 我 还 会 坚 持 学 下 去 ， 但 是 我 现 在 很 迷 茫 ， 从 目 前 学 习 到 知 识 来 看 ， 也 只 是 在 解 决 数 学 问 题 ， 减 少 繁 琐 的 数 学 计 算 而 已 ， 对 于 我 以 后 的 工 作 和 生 活 中 能 运 用 到 哪 里 去 ， 我 真 的 不 知 到 ， 所 以 我 还 是 很 迷 茫,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 0 0 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
每 一 课 都 很 用 心 ， 对 于 基 础 知 识 讲 的 很 到 位,O O B-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1
挺 好 的 ， 细 腻 ， 深 奥 ， 循 序 渐 进 。,O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 、 课 程 、 课 件 都 非 常 的 专 业 和 用 心 ， 自 学 完 全 没 有 压 力 。 简 直 是 宝 藏 网 站 ， 值 得 一 生 收 藏,B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
真 心 学 到 很 多 有 用 的 东 西 ， 平 台 真 好,O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1
讲 的 很 清 晰 。 对 于 我 一 个 零 基 础 的 人 ， 还 可 以 慢 慢 接 受 。 大 大 的 厉 害 。,B-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 于 初 学 p y t h o n 的 同 学 而 言 ， 整 个 课 程 体 系 真 的 很 不 错,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
很 好 的 为 一 个 p y t h o n 小 白 打 开 了 p y t h o n 的 大 门,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 1 1 1 1 1 1 -1 2 2
简 单 易 懂 通 透 的 了 解 到 了 计 算 机 编 程 的 基 本 概 念,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 2 2 2 2
嵩 老 师 的 课 深 入 浅 出 ， 非 常 适 合 初 学 者 ， 感 谢 提 供 了 这 样 一 个 平 台 ！,B-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
通 俗 易 懂 ， 逻 辑 清 晰 ， 用 例 经 典 ， 训 练 高 效 。 非 常 好 的 入 门 课 程,O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
超 出 预 期 ， 可 太 棒 了 ！ 课 程 老 师 都 非 常 好 ！,O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
谢 谢 老 师 的 讲 课 ， 深 入 浅 出 的 讲 课 风 格 ， 深 深 影 响 了 我 ， 我 要 把 学 到 的 知 识 分 享 给 需 要 的 人 ！,O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
讲 的 很 好 ， 可 能 就 是 对 小 白 来 说 有 点 难 以 理 解,O O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ， 我 是 一 个 非 计 算 机 专 业 的 已 经 工 作 的 学 生 ， 学 习 该 课 程 收 获 很 大,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 -1 -1
课 程 设 计 得 好 ， 老 师 讲 得 好 ， 超 级 棒 ！,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
呜 呜 呜 ， 忘 记 绑 定 课 程 与 P y t h o n 1 2 3 了 咋 办 呀,O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1
很 喜 欢 嵩 天 老 师 的 课 ， 几 个 实 践 项 目 都 设 计 得 特 别 好 ， 从 一 开 始 完 全 读 不 懂 温 度 转 换 小 程 序 到 现 在 基 本 能 跟 上 节 奏 ， 好 久 没 这 么 认 真 地 学 一 个 东 西 了 。,O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 2 2 2 2 -1 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 习 到 好 多 知 识 ， 满 满 的 干 货 ， 开 阔 思 路 ， 很 棒 ！,O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
快 速 掌 握 p y t h o n 基 础 语 法 体 系 ， 更 快 开 始 专 用 库 的 学 习 ， 讲 的 非 常 好,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O B-ASP O O O O,-1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 2 -1 -1 -1 -1
第 一 次 学 编 程 ， 从 知 乎 上 了 解 到 嵩 天 团 队 的 P y t h o n 基 础 课 就 来 了 ， 学 了 两 个 月 ， 真 的 很 感 谢 老 师 的 细 心 讲 解 ， 从 整 体 到 局 部 给 我 过 了 一 遍 P y t h o n 的 世 界 。,O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1
这 对 新 手 菜 鸟 ， 尤 其 是 没 有 任 何 敲 代 码 经 验 的 人 来 说 ， 课 程 真 是 太 友 好 了 ！ ！ 课 程 设 置 非 常 合 理 ， 循 序 渐 进 ， 没 有 太 多 纯 理 论 ， 不 容 易 晕 。 每 个 技 术 点 都 结 合 实 例 ， 能 听 得 懂 ， 基 本 可 以 照 着 改 改 用 。 配 套 练 习 特 别 好 ， 能 够 加 深 对 课 程 内 容 的 理 解 ， 同 时 补 充 用 法 ， 解 决 了 我 听 得 懂 ， 不 会 用 的 难 题 。 内 容 能 够 满 足 入 门 P Y T H O N 的 需 求 ， 课 程 思 路 非 常 清 晰 ， 学 习 之 后 我 能 够 理 清 P Y T H O N 技 术 要 点 和 基 本 用 法 逻 辑 ， 非 常 赞 。 部 分 用 法 老 师 也 会 提 示 后 面 会 具 体 学 习 到 ， 学 起 来 就 不 会 那 么 焦 虑 。 课 件 风 格 明 快 ， 老 师 长 得 帅 ， 很 养 眼 ~ ~ 特 别 点 赞 最 开 始 的 复 习 听 写 、 默 写 环 节 和 一 起 敲 代 码 环 节 ， 课 程 参 与 感 特 别 好 ， 就 让 人 很 想 学 啊 ！ ！ ！ 对 克 服 惰 性 、 了 解 学 习 方 法 、 熟 悉 编 写 代 码 有 非 常 大 的 帮 助 ， 真 是 解 决 了 过 去 不 知 道 怎 么 敲 代 码 的 实 际 困 难 （ 小 白 是 真 的 没 见 过 人 敲 代 码 … … ） 希 望 老 师 能 提 供 一 些 代 码 规 范 ， 注 意 到 有 些 地 方 空 格 了 ， 有 些 地 方 没 有 ， 不 知 道 是 不 是 规 定 ？ 还 是 就 看 着 顺 眼 敲 ？ 总 之 ， 非 常 非 常 喜 欢 这 个 课 程 ， 通 过 学 习 这 个 课 程 ， 我 第 一 次 根 据 工 作 需 求 编 写 了 程 序 并 成 功 减 少 了 一 周 的 重 复 工 作 量 ， 超 级 推 荐 ！ ！ ！ （ 还 想 写 ， 可 惜 超 字 数 了 ， 感 谢 老 师 ， 感 谢 课 程 ， 感 谢 平 台 ）,O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。 一 般 一 般 ， 为 什 么 视 频 要 做 成 一 两 分 钟 的 很 反 感 这 么 短 的 视 频 长 度,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0
效 果 非 常 好 ， 激 发 了 学 习 的 兴 趣 ！,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
讲 真 ， 这 个 暑 假 的 学 习 收 获 还 算 满 意 的 。 因 为 一 天 工 作 平 均 1 2 小 时 ， 作 息 不 太 规 律 ， 本 人 有 比 较 贪 睡 ， 所 以 只 能 抽 点 时 间 空 闲 的 时 候 看 看 ， 视 频 配 合 书 籍 学 起 来 不 要 太 爽 ！ 课 堂 内 容 生 动 有 趣 ， 特 别 喜 欢 看 老 师 一 本 正 经 地 将 一 些 有 趣 的 例 子 ， 因 为 有 时 候 学 习 隔 的 时 间 太 久 ， 所 以 会 再 看 多 一 遍 ， 听 说 有 的 同 学 已 经 刷 了 4 遍 了 ， 咱 也 不 能 落 下 ！,O O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 1 1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
利 用 空 余 时 间 不 断 提 高 自 己 很 好,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 生 动 容 易 理 解 ， 也 非 常 系 统 全 面,B-ASP I-ASP O O O O O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
喜 欢 嵩 老 师 的 课 程 ， 每 个 课 程 学 习 受 益 匪 浅 ， 继 续 学 习 ， 不 断 提 升 自 己 ！,O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O,-1 -1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
逻 辑 清 晰 重 点 突 出 与 编 程 实 例 结 合 培 养 动 手 能 力 答 疑 及 时 微 信 群 创 建 共 同 学 习 氛 围 ， 非 常 喜 欢,B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 1 1 1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
非 常 友 好 ， 而 且 学 起 来 没 感 觉 很 累 ， 特 别 推 荐 ！,O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 地 很 好 ， 也 算 是 薅 社 会 主 义 羊 毛 。 谢 谢 嵩 老 师 ， 谢 谢 平 台 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1
没 有 编 程 基 础 有 点 难 希 望 讲 的 再 细 致 一 些,O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP O O O O O O,-1 -1 1 1 1 1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1
通 俗 易 懂 ， 非 常 适 合 初 学 者 ！ 会 一 直 关 注 嵩 老 师 的 课 程 . .,O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 -1
很 喜 欢 该 老 师 教 学 排 版 风 格 ， 个 人 认 为 非 常 适 合 我 自 己 ， 授 课 方 式 循 序 渐 进 ， 受 益 匪 浅 。,O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 老 师 讲 解 的 很 好 ， 很 幽 默,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 最 好 的 地 方 就 是 能 成 一 个 体 系 ， 学 完 这 个 ， 还 有 后 续 的 内 容,O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
课 程 讲 授 内 容 全 面 、 且 清 晰 ， 结 合 案 例 令 人 受 益 匪 浅,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
感 觉 老 师 讲 的 挺 好 的 ， 很 适 合 初 学 者 ， 灌 输 的 难 度 分 成 很 多 小 份 ， 及 时 的 带 领 我 们 复 习 。 节 奏 不 紧 不 缓,O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1
老 师 授 课 方 式 非 常 正 式 ， 从 中 受 到 很 多 启 发 。,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
H 很 棒 很 不 错 ， 国 家 的 希 望 。 如 果 能 一 直 做 下 去 ， 就 太 好 了 。,B-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O,2 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 授 课 通 俗 易 懂 ， 循 序 渐 进 ， 而 且 P y t h o n 这 门 课 也 不 是 我 想 象 中 的 那 么 难 学 ， 基 本 上 都 很 容 易 理 解 ， 我 相 信 我 也 可 以 学 好 P y t h o n,B-ASP I-ASP O B-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
和 之 前 学 习 的 课 程 枯 燥 感 不 一 样 ， 这 些 老 师 的 讲 解 ， 真 的 非 常 容 易 让 学 生 听 进 去 ， 听 进 去 是 学 进 去 的 前 提,O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
老 师 所 讲 内 容 通 俗 易 懂 ， 例 子 生 动 形 象 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
挺 好 的 入 门 课 。 。 。 。 。 。 。 。 。 。 。 。 。 。 。,O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
抱 着 学 习 量 化 交 易 来 学 的 课 程 ， 总 体 来 说 通 俗 易 懂 ， 而 且 内 容 很 系 统 化 ， 视 频 质 量 也 高 ， 本 来 想 打 4 星 半 ， 唯 一 的 不 足 是 我 作 为 小 白 还 有 一 点 点 代 码 的 细 节 有 时 候 会 有 疑 惑 ， 总 的 来 说 值 得 感 谢 ， 5 星 好 评 。,O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
V e r y n i c e c l a s s f o r o u r g r e e n h a n d t o s t u d y !,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
希 望 能 更 加 的 运 用 在 实 际 工 作 中 中,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1
真 好 ！ 老 师 讲 得 浅 显 易 懂 ， 学 到 好 多 ！ 老 师 辛 苦 了 ！,O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
非 常 喜 欢 ！ 由 浅 入 深 ！ 有 理 论 有 实 践 ！ 可 以 指 导 日 常 工 作 ！,O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1
从 各 方 面 来 讲 都 是 十 分 细 致 详 细 的 ， 非 常 好 的 课 程,O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
真 的 好 都 是 干 货 ， 我 真 是 太 喜 欢 了,O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
学 起 来 很 容 易 ， 可 能 因 为 看 过 相 关 的 书 ， 对 p y t h o n 认 识 更 加 深 刻 ， 查 漏 补 缺 了 。,O O O O O O O O O O O O O O O O B-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 安 排 很 合 理 ， 难 度 由 浅 入 深 ， 层 层 递 进,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
教 授 内 容 浅 显 易 懂 ， 难 易 适 中 ， 非 常 适 合 作 为 入 门 教 程 ！,O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
特 别 棒 的 p y t h o n 入 门 课 ， 强 推 ！ ！ ！ 结 合 实 例 深 入 浅 出 讲 解 嵩 天 老 师 棒 ！ ！ ！,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
用 1 0 天 时 间 学 习 完 P y t h o n 语 言 程 序 设 计 ， 前 三 周 的 课 在 M o o c 平 台 上 学 习 的 ， 后 面 的 等 不 上 更 新 ， 在 B 站 上 进 行 了 学 习 ， 如 饥 似 渴 ， 嵩 天 老 师 讲 得 太 好 了 ， 人 生 第 一 门 在 线 课 程 ， 受 益 匪 浅 。 感 谢 嵩 天 老 师 。,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
非 常 好 的 课 程 ， 老 师 讲 得 非 常 好 。,O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1
课 程 很 适 合 P y t h o n 入 门 学 习 ， 语 法 学 习 加 编 程 练 习 ， 效 果 很 好,B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O,2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 -1 2 2 -1 -1
嵩 天 老 师 真 的 讲 的 很 好 ， 我 自 己 想 学 p y t h o n 很 久 了 ， 但 是 每 一 次 都 是 坚 持 了 几 天 就 放 弃 了 ， 感 觉 很 无 聊 ， 但 是 嵩 天 老 师 讲 的 生 动 易 懂 ， 不 懂 编 程 的 学 起 来 也 不 算 费 劲 贵 ， 另 外 再 p y t h o n 1 2 3 里 面 还 有 一 些 基 础 练 习 题 ， 可 以 巩 固 我 们 的 知 识 。 而 且 我 发 现 当 我 把 基 础 语 法 体 系 学 完 后 ， 好 像 去 学 习 其 他 专 题 的 内 容 ， 比 如 爬 虫 专 题 ， 感 觉 更 有 兴 趣 了 ， 学 的 也 更 快 了 ， 希 望 老 师 不 要 关 闭 这 些 课 ， 真 的 是 宝 藏 课 程 ， 宝 藏 老 师 啊 。 但 是 不 知 道 老 师 会 不 会 继 续 出 高 阶 语 法 的 课 ， 面 向 对 象 的 ， 我 在 慕 课 伤 好 像 没 找 到 啊 。 总 之 ， 感 谢 嵩 天 老 师 ， 让 我 在 p y t h o n 编 程 终 于 真 正 开 始 迈 出 了 步 伐 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 -1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 授 得 非 常 详 细 清 楚 ， 学 习 了 有 收 获 谢 谢 老 师,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2
嵩 天 老 师 讲 解 的 太 好 了 ， 团 队 也 棒 ， 制 作 了 这 么 精 美 的 视 频 课 件 ， 练 习 也 是 看 得 出 是 精 心 设 计 的 ， 谢 谢 全 体 人 员 的 辛 苦 付 出 !,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1
内 容 组 织 合 理 ， 符 合 教 育 认 知 规 律 ， 有 复 习 ， 有 总 结 ， 有 实 例 ， 有 练 习 ， 受 益 匪 浅,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 1 1 -1 -1 1 1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1
好 的 课 程 、 非 常 精 进 专 业 的 老 师 ， 还 有 可 以 下 载 的 课 件 文 档 课 后 复 习 ， 完 美 完 美 完 美 ， 重 要 的 事 说 三 遍 ！ 致 敬 致 敬 蒿 天 老 师 和 团 队 老 师 们 ！,O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 2 -1
之 前 听 过 很 多 课 ， 都 是 从 入 门 到 放 弃 ； 但 是 这 门 课 让 人 有 学 下 去 的 动 力 ！,O O O O O O B-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
嵩 天 老 师 讲 的 超 好 ， 入 门 可 用 ， 逻 辑 结 构 都 很 清 晰 ， 举 例 很 贴 切 ， 实 例 练 习 的 量 可 能 有 点 少,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 0 0 0 0 -1 0 -1 -1 -1 -1 -1
老 师 讲 的 特 别 好 。 很 喜 欢 老 师 的 课 。 希 望 自 己 能 学 好,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 好 ， 能 深 入 点 就 更 好 了,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 习 课 程 对 于 编 程 入 门 非 常 有 用 ！,O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
没 想 到 p y t h o n 这 么 有 意 思 呢 ！,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O,-1 -1 -1 2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1
老 师 讲 的 很 好 啊 特 别 是 每 节 课 都 有 复 习 和 总 结 以 及 布 置 了 练 习 每 次 学 习 后 都 会 给 自 己 反 馈 很 可 啦 ！ 准 备 去 跟 着 这 个 老 师 学 爬 虫 和 数 据 分 析 了,B-ASP I-ASP O O O O O O O O O O B-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 -1 1 1 1 1 -1
赞 赞 赞 赞 ， 不 过 小 白 上 手 还 是 有 难 度 ， 建 议 调 整 一 下 课 程 顺 序 ， 先 学 语 法,O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 1 1
挺 不 错 ， 是 我 坚 持 学 习 的 一 门 网 课,O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
这 个 课 相 当 不 错 ， 很 有 意 思 ， 比 较 系 统 ， 不 用 为 后 面 要 学 啥 子 而 感 到 密 码 。,O O B-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
嵩 老 师 讲 的 v 太 好 了 ， 课 件 也 非 常 好 ！,B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
感 觉 讲 得 很 好 ， 逻 辑 严 谨 ， 条 理 清 晰,O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1
老 师 讲 得 太 好 了 ， 第 一 次 感 觉 程 序 设 计 如 此 简 单,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
老 师 讲 的 特 别 好 ， 深 入 浅 出 ！ 手 工 点 赞 ~,B-ASP I-ASP O O O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
生 动 有 趣 ， 但 是 对 与 小 小 白 来 说 ， 课 程 里 面 的 代 码 、 函 数 ， 还 是 有 很 多 没 理 解,O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
新 手 一 个 命 令 符 和 功 能 都 不 知 道 ， 能 不 能 把 本 科 用 到 的 先 罗 列 一 点 出 来,O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 0 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
慕 课 是 一 个 好 平 台 ， 做 到 了 即 点 即 看 即 学 ， 北 理 工 的 p y t h o n 资 源 和 配 套 平 台 真 的 很 不 错 ， 系 统 地 学 习 了 p y t h o n ， 以 及 一 些 第 三 方 库 。,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 1 1 1 1 1 1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1
挺 好 的 。 学 会 了 很 多 知 识 ， 尤 其 是 一 些 程 序 例 题 ， 挺 好,O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
北 理 工 的 这 个 P y t h o n 课 程 设 计 很 科 学 啊 ， 没 有 一 丝 累 赘 的 感 觉 ， 精 品 精 致 。,B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O,2 2 2 -1 -1 -1 1 1 1 1 1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
以 生 活 自 动 化 作 为 引 入 可 能 更 适 合 普 及 ， 一 家 之 言 。 当 然 这 样 就 缺 少 了 教 学 的 系 统 性 。,O B-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O,-1 2 2 2 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 0 -1
授 课 内 容 趣 味 多 彩 ， 讲 师 热 情 洋 溢 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 的 课 程 逻 辑 清 晰 内 容 明 白 讲 解 易 懂 好 课 ！,B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP O,1 1 -1 2 2 2 2 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 2 -1
2 5 1 1 的 访 谈 法 院 和 对 方 同 意 后,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O,1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
超 出 预 期 ， 通 过 学 习 ， 才 知 道 自 己 的 差 距,O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP,-1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0
基 础 很 详 实 ， 对 新 入 门 的 人 来 讲 ， 相 当 棒 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 件 总 是 播 放 失 败 [ c o d e ： 1 0 ] ， 每 次 都 要 刷 新 页 面 ， 一 小 节 课 能 刷 新 N 回,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP O O O O O,0 0 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1
学 习 起 来 并 不 枯 燥 ， 感 觉 很 有 趣 ， 比 较 注 重 使 用 而 不 是 考 试 枯 燥 的 知 识 点,O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 0 0 -1 -1 -1 0 0 0
学 习 了 很 多 计 算 机 知 识 ， 感 觉 受 益 匪 浅 ！,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
听 了 不 少 语 言 课 ， 这 个 最 易 懂 ， 真 的,O O O O B-ASP I-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 的 课 讲 的 很 好 ， 理 论 实 践 兼 备 ， 轻 松 提 升 。 看 网 上 有 的 妈 妈 评 论 说 ， 有 孩 子 小 学 生 考 级 ， 都 能 听 懂 跟 上 课 程 。 看 这 门 课 程 之 前 ， 有 看 B 站 的 一 些 p y t h o n 视 频 ， 但 比 较 注 重 知 识 点 的 罗 列 ， 听 的 过 程 很 容 易 半 途 放 弃 ， 嵩 天 老 师 的 课 程 就 完 全 不 会 有 这 方 面 的 担 忧 。 比 较 适 合 刚 入 门 的 零 基 础 初 学 者 ， p p t 也 制 作 精 良 ， 都 保 存 了 ， 敲 代 码 的 时 候 查 询 很 方 便 ！,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O,1 1 1 1 -1 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
简 洁 明 了 ， 通 俗 易 懂 ， 是 入 门 的 好 课 程 ， 好 多 年 不 接 触 编 程 听 起 来 都 能 反 应 的 额 过 来 ， 感 谢 M O C C ， 感 谢 各 位 老 师 @ ！,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1
之 前 自 己 零 零 碎 碎 学 了 p y t h o n ， 但 很 多 地 方 会 遇 到 困 惑 ， 这 门 课 好 在 能 够 系 统 化 讲 解 p y t h o n 的 入 门 基 础 ， 也 是 我 最 需 要 的 ， 打 牢 基 础 才 是 关 键,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
非 常 好 的 一 个 地 方 ， 可 以 学 习 到 很 多 东 西,O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
通 俗 易 懂 ， 易 上 手 ， 谢 谢 老 师 们 的 好 课,O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 2
更 加 深 入 的 了 解 和 掌 握 P y t h o n 相 关 知 识,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1
很 好 ， 很 喜 欢 这 个 老 师 ， 但 是 和 课 本 内 容 不 太 一 致,O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1
这 门 课 程 老 师 讲 得 真 的 很 好 ， 制 作 非 常 用 心 ， 讲 课 符 合 认 真 规 律 ， 从 从 前 课 预 习 到 课 上 讲 解 ， 课 后 习 题 方 方 面 面 都 非 常 好 。 下 个 单 元 上 课 时 老 师 还 会 带 领 大 家 再 回 顾 一 下 前 面 学 过 的 内 容 。 整 门 课 的 学 习 感 受 是 很 舒 服 ， 老 师 把 重 点 ， 把 常 用 的 知 识 通 过 大 家 易 于 接 受 的 方 式 讲 解 给 大 家 ， 真 的 很 好 。 有 的 课 程 一 味 追 求 初 学 的 时 候 就 讲 很 多 很 多 甚 至 很 高 深 的 内 容 ， 我 觉 得 不 符 合 认 知 规 律 ， 特 别 是 对 于 我 这 种 菜 鸡 而 言 ， 我 非 常 赞 同 嵩 天 老 师 的 这 种 教 学 模 式 ， 让 我 受 益 匪 浅 。 最 后 感 谢 嵩 天 老 师 ， 感 谢 整 个 课 程 制 作 团 队 ！ ！ ！,O O B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP O O O O O O O O O O B-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,-1 -1 2 2 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1
有 时 候 有 些 细 节 讲 解 的 不 是 很 清 楚 ， 学 着 不 是 很 明 白 ， 还 有 就 是 希 望 有 重 难 点 的 区 分,O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
在 讲 解 时 可 以 考 虑 辅 助 讲 解 计 算 机 一 些 底 层 逻 辑 ， 比 如 p y t h o n 参 数 在 计 算 机 内 是 如 何 存 储 ， 传 递 给 函 数 又 是 以 什 么 样 形 式 ， 适 当 讲 解 底 层 ， 可 以 帮 助 学 生 更 好 理 解 代 码 含 义,O O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
非 常 的 好 老 师 教 的 很 详 细 课 后 作 业 也 涵 盖 教 的 知 识 在 讨 论 区 提 的 问 题 也 很 快 有 助 教 回 复 解 答,O O O B-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
太 好 了 。 线 上 讲 解 和 课 后 题 结 合,O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O,-1 -1 -1 -1 2 2 2 2 -1 2 2 2 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 非 常 好 。 刚 开 始 觉 得 特 别 难 ， 感 谢 讲 课 老 师 的 入 门 教 育 ， 激 发 兴 趣 ， 坚 持 到 底 了 。 对 初 学 者 来 说 ， 兴 趣 是 克 服 困 难 的 最 大 的 动 力 。,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 点 难 哒 哒 哒 哒 ， 代 码 可 以 在 讲 解 细 一 点,O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 0 0 -1 -1 -1
非 常 好 ， 有 回 到 了 上 学 的 感 觉 ， 老 师 讲 的 内 容 也 很 让 自 己 受 益 ！ ！,O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 通 俗 易 懂 ， 课 程 进 度 设 置 十 分 合 理,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
很 好 ！ 老 师 能 从 学 生 的 角 度 去 教 学 ， 让 学 生 很 收 益 。,O O O B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 2 2 -1
前 后 知 识 点 衔 接 有 矛 盾 。 视 频 中 有 错 误 ， 比 如 第 六 章 第 二 节 的 第 五 个 视 频 里 面 第 二 个 元 素 的 序 号 应 该 是 1 ， 建 议 老 师 先 运 行 一 遍 再 放 上 来 呢 : ),O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 0 0 0 -1 0 0 -1 0 0 -1 -1 0 0 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 多 细 节 老 师 都 讲 到 了 ， 需 要 多 练 习 ， 加 强 程 序 思 维,O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
非 常 好 ， 理 论 结 合 实 践 ， 充 分 展 现 了 p y t h o n 的 魅 力 ， 让 人 爱 上 p y t h o n 。,O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1
老 师 讲 的 真 是 太 好 了 ， 非 常 感 谢 老 师,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
嵩 老 师 讲 的 太 好 了 ， 言 简 意 赅 ， 案 例 生 动 ， 由 浅 入 深 ， 绝 对 是 同 学 们 学 习 P Y T H O N 的 超 好 资 源 ！,B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1
课 程 不 错 ， 但 是 练 习 有 很 多 用 到 了 课 上 没 有 提 及 到 的 知 识 ， 也 没 看 到 补 充 的 自 学 资 料 ， 比 如 e v a l ( ) 这 个 函 数 可 以 直 接 读 取 并 计 算 字 符 串 形 式 的 算 式 ， 再 比 如 第 一 单 元 里 没 有 讲 到 f o r 循 环 的 用 法 ， 但 是 很 多 练 习 中 都 出 现 了 ， 这 些 对 于 初 学 者 来 说 都 不 是 很 友 好 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 -1 0 0 0 0 -1 -1 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
大 概 可 以 学 到 p y t h o n 的 基 本 内 容,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1
这 前 在 大 学 课 程 中 只 是 简 单 的 学 习 过 V B ， 也 未 曾 接 触 过 其 他 深 层 次 的 编 程 语 音 ， 而 在 以 后 的 生 活 学 习 工 作 中 ， 都 需 要 一 定 的 数 据 处 理 能 力 ， 因 此 我 才 来 入 门 尝 试 一 下 p y t h o n 。 刚 开 始 听 的 p y t h o n 入 门 课 也 是 嵩 天 老 师 2 0 1 7 讲 的 零 基 础 学 p y t h o n ， 随 着 m o o c 中 2 0 2 0 新 课 程 的 开 启 决 定 跟 进 新 课 程 进 度 。 以 前 我 觉 得 p y t h o n 应 该 是 很 难 理 解 的 ， 编 写 很 复 杂 ， 代 码 不 容 易 理 解 。 但 是 ， 自 从 跟 了 嵩 天 老 师 的 p y t h o n 课 程 之 后 ， 我 觉 得 似 乎 p y t h o n 入 门 也 没 有 想 象 中 的 那 么 难 。 经 过 嵩 天 老 师 的 讲 解 ， 我 对 p y t h o n 的 理 解 更 加 深 入 。 在 教 学 视 频 里 ， 能 感 觉 到 嵩 天 老 师 是 一 个 温 柔 细 心 的 老 师 ， 他 除 了 讲 解 实 例 ， 还 举 一 反 三 ， 个 人 觉 得 很 有 意 义 。 在 后 续 的 课 程 里 ， 我 会 紧 跟 嵩 天 老 师 授 课 的 脚 步 ， 并 在 课 程 结 束 之 后 继 续 深 入 学 习 p y t h o n 语 音 。 因 此 ， 在 这 里 诚 挚 的 感 谢 m o o c 平 台 ， 感 谢 嵩 天 老 师 ！ 谢 谢 你 们 ！ ！,O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 以 实 例 结 合 知 识 点 的 讲 解 方 式 ， 更 有 代 入 感 。,B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O,1 1 -1 2 2 -1 -1 2 2 2 -1 2 2 2 2 -1 -1 -1 2 2 2 -1
极 为 优 秀 的 课 程 ， 老 师 讲 课 内 容 清 晰 明 确 ， 配 套 练 习 也 很 好,O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
老 师 讲 的 很 适 合 我 自 己 的 接 受 能 力 ， 逻 辑 清 晰 明 了 ， 知 识 点 穿 插 承 前 启 后 ， 并 且 每 周 都 会 做 总 结 与 回 顾 ， 对 于 学 习 这 门 语 言 来 说 恰 到 好 处 。,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
跟 着 老 师 入 门 了 ， 讲 的 很 清 楚 。,O O B-ASP I-ASP O O O O B-ASP O O O O O,-1 -1 2 2 -1 -1 -1 -1 2 -1 -1 -1 -1 -1
非 常 棒 ！ 老 师 讲 的 很 详 细 ， 作 业 也 很 好 地 起 到 了 复 习 巩 固 的 作 用,O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
感 觉 前 期 都 比 较 容 易 跟 上 ， 到 后 期 t i m e 库 ， 科 赫 雪 花 那 里 特 别 吃 力 ， 每 周 的 课 程 也 比 较 多 ， 因 为 工 作 原 因 ， 只 能 抽 一 天 的 时 间 学 习 ， 感 觉 精 力 跟 不 太 上 ， 学 习 这 个 课 程 三 次 了 ， 都 没 有 完 整 学 完 ， 课 程 设 计 很 好 ， 就 是 自 己 坚 持 不 下 来 挺 有 挫 败 感 。,O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1
送 花 ， 很 喜 欢 老 师 的 讲 课 ， 讲 得 通 俗 易 懂 ！ ！ ！,O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
逻 辑 很 清 楚 、 示 例 讲 解 详 细 ， 重 点 是 免 费 ， 用 来 自 学 相 当 合 适 。,B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1
学 习 的 内 容 很 棒 ， 需 要 自 己 练 习 的 地 方 很 多 ， 每 次 看 完 视 频 需 要 自 己 有 3 个 小 时 左 右 的 时 间 去 消 化 理 解,O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
可 能 是 基 础 课 程 讲 解 的 比 较 宽 泛 深 度 不 够 感 觉 都 学 了 有 感 觉 都 不 会,O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 说 话 听 着 很 舒 服 ， 课 程 内 容 设 置 非 常 好 ， 不 会 觉 得 枯 燥 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
我 觉 得 特 别 有 意 思 ， 凭 几 个 简 单 的 字 母 就 可 以 编 出 自 己 喜 欢 的 东 西 。,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
针 对 基 础 的 讲 解 配 合 相 应 简 单 应 用 ， 这 种 方 式 很 好 理 解 基 础 知 识 ， 关 于 程 序 化 设 计 以 及 设 计 思 维 的 理 念 的 讲 解 对 我 也 很 有 帮 助 ， 不 愧 国 家 精 品 ， 不 但 简 单 易 懂 ， 而 且 可 以 引 发 思 考,O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 1 1 1 1 1 -1 -1 1 1 1 1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
很 不 错 的 课 程 ， 重 点 明 确 ， 条 理 清 晰,O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O,-1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1
课 程 讲 的 很 细 致 ， 老 师 也 很 有 亲 和 力 ， 就 是 看 课 件 ， 似 乎 内 容 还 是 不 够 丰 富 ， 能 否 后 续 再 扩 展 一 些 内 容 ， 让 小 白 们 把 基 础 打 的 扎 扎 实 实 的 。 另 外 ， 希 望 课 件 一 直 能 放 在 网 上 不 要 删 掉 。 我 喜 欢 没 有 围 墙 的 大 学 ！ 谢 谢 老 师 及 幕 后 工 作 者 ！,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 0 0 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1
老 师 内 容 非 常 详 细 ， 适 合 基 础 小 白 ， 通 俗 易 懂 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
课 时 安 排 合 理 ， 内 容 难 度 规 划 适 宜,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
非 常 好 ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！,O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
每 天 学 习 一 点 ， 时 间 长 了 就 可 以 进 步 很 多,O O O O O O O O O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
本 课 程 结 合 实 际 ， 讲 解 的 非 常 不 错,O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O,-1 2 2 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1
思 路 清 晰 ， 讲 解 透 彻 ， 实 用 性 强 。,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1
这 个 课 程 生 动 易 懂 ， 老 师 的 讲 课 细 致,O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O,-1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1
老 师 讲 得 很 好 ， 只 是 对 于 小 白 来 说 ， 有 些 代 码 不 理 解 ， 还 希 望 助 教 多 多 答 疑 啊,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1
讲 的 比 较 走 马 观 花 。 进 入 正 题 前 的 内 容 较 多 。,B-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP O O O,0 -1 -1 -1 0 0 0 0 -1 -1 -1 1 1 -1 -1 0 0 -1 -1 -1
不 但 有 知 识 点 ， 更 有 思 路 分 析 ， 在 思 维 训 练 上 也 很 有 帮 助 。,O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 -1 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1
老 师 棒 棒 的 ！ 希 望 自 己 能 坚 持 下 来,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 得 很 细 ， 很 有 条 理 ， 案 例 也 用 的 很 经 典 ， 获 益 良 多,B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
能 给 刚 入 门 的 小 白 打 下 很 好 的 基 础,O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 们 讲 的 很 好 ， 由 浅 入 深 ， 条 理 清 晰,B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
老 师 说 话 很 温 柔 ， 教 学 清 楚 有 条 理,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2
老 师 讲 的 非 常 细 致 ， 简 单 易 懂 ， 作 为 小 白 的 我 受 益 匪 浅,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
学 习 到 了 p y t h o n 的 一 些 知 识,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP,-1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2
老 师 讲 的 好 好 哦 ， 小 白 开 始 入 门 了 。,B-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 给 力 ， 就 是 年 龄 大 了 ， 还 要 处 理 工 作 事 宜 ， 学 习 起 来 进 度 慢 ， 作 业 都 没 分 数 了 。,O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 0 0 -1 -1 0 0 -1 -1
挺 好 的 ， 我 是 小 白 中 的 小 白 ， 有 收 获,O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
这 老 师 不 错 。 形 象 不 错 ， 表 达 自 然 。,O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,-1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1
作 为 小 白 来 说 ， 听 完 一 半 的 课 程 以 后 ， 用 最 笨 的 方 法 已 经 能 将 编 出 的 程 序 应 用 到 实 际 工 作 中 了 ， 非 常 有 成 就 感 。,O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
没 听 懂 ， 公 式 没 看 懂 ， 对 齐 方 式 没 看 懂 ， 参 数 没 看 懂,O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 0 0 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 0 0 -1 -1 -1
课 程 挺 好 的 ， 希 望 可 以 继 续 加 油 ， 但 是 对 于 编 程 小 白 来 说 ， 需 要 理 解 的 东 西 还 有 很 多,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 得 太 好 了 ！ ！ 必 须 给 3 2 个 赞 ！,B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1
老 师 讲 解 深 入 浅 出 ， 例 子 很 有 启 发 性 。,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 -1
感 受 到 了 学 习 P Y T H O N 的 乐 趣 ， 冲 冲 冲,O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1
老 师 语 速 非 常 适 合 我 的 理 解 速 度 。 另 望 增 加 些 趣 味 性 才 更 完 美 。,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1
好 的 ， 内 容 与 例 子 结 合 ， 通 俗 易 懂,O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 由 浅 入 深 ， 老 师 讲 解 得 很 棒 ， 容 易 理 解 ， 真 的 是 我 发 现 的 宝 藏 课 程 ！,B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 设 置 很 考 究 ， 感 觉 是 不 断 总 结 打 磨 出 来 的 版 本 ； 每 周 的 课 程 内 容 也 安 排 得 很 合 理 ， 讲 解 的 P Y T H O N 实 列 即 能 练 习 和 掌 握 知 识 点 ， 又 有 一 定 实 践 性 和 扩 展 性 ； 另 外 ， 课 件 p p t 的 制 作 也 很 精 良 ， 下 载 下 来 复 习 可 以 作 为 一 个 很 好 的 笔 记 资 料 。 整 体 来 说 ， 美 中 不 足 的 是 ， 课 外 知 识 或 参 考 资 料 提 供 的 有 一 些 少 ， 尤 其 是 在 做 5 道 自 由 练 习 的 时 候 ， 虽 然 大 部 分 按 照 自 己 的 写 法 都 可 以 做 出 来 ， 但 却 不 一 定 是 最 优 化 的 代 码 。 标 准 答 案 里 面 只 给 出 了 代 码 ， 如 若 能 附 加 一 些 思 路 方 面 的 就 更 好 了 ； 其 次 ， 标 准 答 案 里 面 的 一 些 函 数 的 用 法 ， 是 课 程 里 面 没 有 讲 到 的 ， 这 也 可 以 理 解 ， 上 课 只 需 要 讲 重 点 和 核 心 知 识 ， 不 可 能 面 面 具 到 ， 其 他 的 知 识 可 以 查 询 网 络 自 学 ， 也 许 这 是 嵩 天 老 师 有 意 而 为 之 的 : D 。 其 实 以 前 我 在 C o u r s e r a 上 面 上 过 南 京 大 学 的 一 个 女 老 师 的 P y t h o n 课 程 ， 还 获 得 了 结 业 证 书 ， 不 过 好 久 没 用 P y t h o n ， 忘 得 都 差 不 多 了 ， 那 门 课 程 讲 得 有 些 快 不 够 细 致 ， 所 以 学 习 效 果 不 是 很 深 刻 。 总 的 来 说 ， 我 给 嵩 天 老 师 的 这 门 课 程 打 4 . 8 5 / 5 分 ， 这 已 经 是 我 学 过 的 最 好 的 编 程 类 网 课 了 ！,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1
虽 然 有 些 地 方 会 不 太 懂 ， 需 要 多 去 研 究 ， 其 他 老 师 讲 解 很 好,O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O,-1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1
这 门 课 让 我 更 容 易 地 去 理 解 代 码 如 何 实 现 的 功 能 很 细 心,O O B-ASP O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O,-1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1
嵩 老 师 教 的 太 好 了 ， 打 算 彻 底 学 会 这 门 课 后 再 去 学 老 师 的 其 他 课 程,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 1 1 -1 -1 -1 2 2
P y t h o n 入 门 非 常 好 的 一 门 课,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP,1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2
深 入 浅 出 ， 惠 及 大 众 ！ 一 本 书 好 不 好 看 ， 常 常 在 第 一 章 佳 能 感 觉 到 ， 赞 ~ ~ ~,O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 很 好 ， 娓 娓 道 来 ， 例 子 也 很 生 动,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
看 过 前 两 期 的 课 ， 到 1 9 年 这 期 ， 讲 得 条 理 更 清 晰 了 。 重 点 更 明 确 了 ， 感 谢 老 师,O O O O O O B-ASP O O O O O O B-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2
学 习 了 很 多 计 算 机 语 言 ， 学 会 了 编 程,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 1 1
很 不 错 ， 受 益 匪 浅 ， p y t h o n 希 望 出 的 教 程 更 全 面 点 ， 就 更 好 了 下 次 会 打 下 满 分,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 解 浅 显 易 懂 ， 结 合 实 例 ， 很 容 易 入 手 。,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 时 长 合 适 ， 实 践 与 理 论 相 得 益 彰 ，,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1
质 量 很 高 的 学 习 课 件 ， 对 学 习 和 掌 握 p y t h o n 语 言 帮 助 很 大,B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O,2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 -1 -1
这 个 课 程 很 棒 ， 之 前 自 学 过 发 现 远 不 如 参 与 这 样 一 个 课 程 ， 每 次 作 业 也 会 有 一 些 超 出 课 程 的 内 容 促 进 自 学 ， 很 赞,O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 2 2 -1 -1 -1
目 前 遇 到 的 最 强 计 算 机 语 言 公 开 课 ！,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1
老 师 讲 解 的 十 分 细 致 认 真 ， 认 真 学 习 下 来 掌 握 了 不 少 编 程 相 关 的 知 识 ， 可 谓 受 益 匪 浅,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 从 最 基 础 的 知 识 讲 起 ， 慢 慢 深 入 讲 解 ， 听 后 会 对 编 程 更 有 兴 趣 和 信 心 ， 但 是 也 一 定 在 多 练 习 才 能 学 的 快 。,B-ASP I-ASP O O B-ASP I-ASP O I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
课 程 内 容 组 织 特 别 好 ， 很 新 颖 ， 能 激 发 我 们 的 学 习 兴 趣 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
非 常 棒 ， 老 师 讲 课 很 好 ， 通 俗 易 懂 。,O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 理 论 容 易 理 解 ， 结 合 联 系 能 够 很 好 的 掌 握 知 识 点,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2
感 觉 一 般 吧 ， 如 果 作 业 与 讲 课 在 同 一 页 面 会 更 好 。,O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1
我 是 完 全 的 编 程 小 白 ， 课 程 一 开 始 比 较 难 ， 不 知 道 自 己 写 的 代 码 什 么 意 思 ， 后 来 系 统 的 学 习 了 后 面 的 章 节 后 对 前 面 章 节 的 代 码 有 了 一 定 的 理 解 ， 老 师 讲 解 很 细 致,O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1
对 自 学 者 来 说 非 常 有 用 的 课 程 ； 我 在 学 这 门 课 程 之 前 ， 也 找 了 一 些 网 友 推 荐 的 教 材 ， 像 国 外 那 本 《 p y t h o n 从 入 门 到 精 通 》 的 译 本 ， 但 是 感 觉 对 小 白 来 说 并 不 合 适 ， 几 经 辗 转 找 到 了 这 里 。 感 谢 老 师 ， 终 于 是 把 我 讲 通 了,O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O,-1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 一 些 部 分 讲 得 不 是 特 别 清 楚 ， 还 需 要 自 己 查 找 资 料 和 花 大 量 时 间 理 解 。,O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O,-1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 0 0 -1 -1 -1
真 的 是 太 棒 了 ~ ！ 老 师 的 讲 解 清 晰 明 了 ~ ！ 课 程 的 设 置 也 很 有 趣 ~ ！ 虽 然 第 五 课 开 始 难 了 起 来 ， 但 是 相 信 自 己 可 以 做 到 ~,O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 学 到 了 很 多 ， 老 师 很 好,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
如 果 我 的 大 学 老 师 也 是 这 样 讲 编 程 的 ， 我 现 在 可 能 是 一 名 程 序 员,O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2
课 程 案 例 经 典 ， 老 师 讲 解 通 俗 易 懂 ， 很 棒 的 p y t h o n 课,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP,2 2 2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2
课 程 很 详 细 ， 框 架 扎 实 ， 每 堂 课 前 有 铺 垫 ， 课 后 有 总 结 ， 练 习 例 子 选 的 很 好 ， 特 别 有 代 表 性 每 一 单 元 讲 完 之 后 有 引 申 ， 拓 展 了 思 路 不 仅 简 单 讲 了 语 法 ， 更 重 点 讲 了 编 程 思 想,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2
讲 解 通 俗 易 懂 ， 课 程 内 容 丰 富 ， 作 业 设 计 更 是 非 常 棒 ！ ！ ！,B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
课 程 非 常 不 错 ， 比 起 那 些 讲 实 操 的 课 程 ， 这 种 偏 重 理 论 分 析 的 还 是 更 适 合 我,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 结 构 知 识 背 景 和 知 识 相 结 合 ， 很 有 趣 。 逻 辑 很 清 晰 ， 有 的 刚 开 始 名 词 术 语 听 不 懂 ， 但 听 到 后 面 的 视 频 就 懂 了 ， 加 深 了 记 忆 ， 很 棒 ！,B-ASP I-ASP O B-ASP B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O,1 1 -1 2 2 2 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
听 嵩 天 老 师 讲 课 真 是 一 种 享 受 ， 知 识 点 讲 解 非 常 清 晰 透 彻 ， 让 人 容 易 理 解 ， 实 例 也 很 有 趣 ， 很 赞 ！,O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 2 2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
可 以 总 结 以 前 学 到 的 内 容 ， 还 学 习 新 的 内 容 ， 不 错 的 一 门 课 程,O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2
讲 的 非 常 的 细 ， 学 生 理 解 的 比 较 清 楚,O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
开 篇 的 第 一 小 节 中 程 序 的 基 本 编 写 方 法 咋 听 不 到 呢 ！ 直 接 往 后 跳 了 ， 怎 么 办 呀 ？,O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 0 0 -1 1 1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 很 风 趣 幽 默 ， 给 的 案 例 也 比 较 有 趣 ， 会 及 时 带 学 生 复 习 。 但 个 别 地 方 稍 微 有 点 粗 略 学 的 不 是 很 清 楚,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 到 了 p y t h o n 相 关 的 课 程 知 识 ， 感 谢 嵩 老 师,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP,-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2
老 师 上 的 很 好 ， 所 以 我 听 课 很 投 入 ， 希 望 可 以 补 充 一 点 额 外 的 练 习 来 加 以 辅 助 。,B-ASP I-ASP O O O O O O O O O B-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1
老 师 课 程 讲 得 非 常 细 致 到 位 ， 对 我 这 编 程 小 白 帮 助 很 大 ， 非 常 值 得 听 讲 ， 五 星 推 荐 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 的 讲 课 简 洁 明 了 ， 谢 谢 老 师 。,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O,1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
内 容 丰 富 ， 讲 解 清 楚 ， 增 加 了 很 多 案 例 知 识,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
每 一 小 节 分 得 很 细 ， 开 篇 和 小 结 做 的 也 很 好 ， 老 师 讲 课 很 有 意 思,O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1
课 程 全 部 学 完 了 ， 感 觉 就 像 是 听 了 一 场 关 于 P y t h o n 的 广 告 ， 毕 竟 课 件 里 老 师 也 给 自 己 打 了 很 多 广 告 ， 吹 了 很 多 天 下 第 一 出 来 。 P y t h o n 并 不 像 一 开 始 介 绍 说 的 那 么 简 单 ， 学 完 好 像 依 然 啥 也 不 会 ， 但 是 又 好 像 能 看 懂 一 些 程 序 了 。 要 想 熟 练 运 用 解 决 问 题 ， 那 还 是 要 花 很 多 时 间 深 入 学 习 的 ， 各 位 加 油 ！ 给 课 程 礼 节 性 评 4 星 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O,0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 0 0 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 内 容 很 丰 富 ， 总 体 感 觉 很 好,B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O,1 1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1
超 出 预 期 的 好 ， 讲 解 详 细 而 不 啰 嗦 ， 面 向 大 众 而 精 于 基 础 ， 很 棒 的 一 套 课 。,O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 -1
非 常 好 ， 不 知 不 觉 中 就 学 会 了 ， 而 且 培 养 了 思 维 ， 强 推 ！,O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
实 例 选 择 非 常 用 心 ， 编 程 只 讲 语 言 是 大 忌 。 理 工 的 老 师 们 辛 苦 了 ， 这 些 实 例 还 是 非 常 耗 费 心 血 的 。 另 外 开 头 非 常 有 创 意 ， 没 有 从 头 讲 语 法 ， 让 门 外 汉 很 快 接 触 到 有 反 馈 的 能 运 行 的 程 序 ， 赞,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
课 程 设 置 清 晰 严 谨 ， 教 学 团 队 优 秀 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
嵩 天 老 师 的 课 还 是 一 如 既 往 的 精 彩,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O,1 1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 教 的 真 的 很 好 ， 只 是 题 目 不 在 m o o c 里 面 很 可 惜 ， 而 且 希 望 例 题 可 以 更 多 一 点,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
理 论 和 实 际 操 作 相 结 合 还 是 蛮 不 错 的,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O,2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 解 到 位 ， 深 入 浅 出 ， 非 常 适 合 零 基 础 伙 伴 学 习 观 看,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。 讲 得 很 透 彻 ， 很 容 易 明 白 ， 结 合 P Y T H O N 1 2 3 能 够 及 时 巩 固 学 到 的 东 西 ， 为 老 师 点 赞,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1
老 师 讲 的 很 棒 ， 很 适 合 没 有 基 础 的 小 白 ， 结 合 语 法 和 实 例 讲 解 。 课 件 也 做 的 很 棒 ， 上 课 受 益 匪 浅,B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
配 合 p y t h o n 计 初 教 程 来 学 ， 事 半 功 倍 。,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
"内 容 简 单 明 了 很 好 懂 ， 跟 着 学 习 循 序 渐 进 ， 老 师 讲 得 也 很 仔 细 。 但 是 对 于 一 些 细 节 还 是 多 少 有 些 忽 略 了 ， 比 如 说 到 赋 值 语 句 的 时 候 只 说 了 右 边 的 值 赋 值 给 左 边 ， 类 似 C = 3 这 样 的 列 子 ， 但 是 对 于 等 号 两 边 有 多 个 值 的 情 况 并 没 有 说 明 , 但 是 在 课 程 中 就 直 接 用 了 （ 比 如 x , y , z = 3 , 4 , 5 或 者 x = y = z = 1 这 样 的 连 续 赋 值 ） , 学 习 过 程 中 ， 还 需 要 寻 找 其 他 的 基 础 课 程 进 行 互 补 式 学 习",B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
入 门 了 p y t h o n 学 习 了 基 本 操 作 ， 老 师 讲 的 比 较 有 意 思 ， 很 喜 欢 单 元 小 结 部 分,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2
很 有 收 获 ， 虽 然 我 是 先 学 的 C / C ， 但 学 了 嵩 天 老 师 的 P Y T H O N 后 ， 收 获 良 多 。,O O B-ASP I-ASP O O O O O O O O B-ASP O I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 2 2 -1 -1 2 2 -1 -1 -1
嵩 老 师 讲 课 通 俗 易 懂 ； 一 口 气 学 了 8 周 的 课 。 还 要 反 复 看 3 遍,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP O O O O O O O O,2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 的 课 程 太 棒 了 ， 由 浅 入 深 ， 举 一 反 三 ， 课 件 制 作 精 良 ， 内 容 丰 富 ， 5 分 好 评 ！ 感 谢 嵩 天 老 师 及 各 位 老 师 的 辛 苦 付 出 ！,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O,2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
刚 开 始 会 详 细 讲 代 码 ， 还 可 以 ， 后 面 的 代 码 就 知 道 整 个 的 意 思 ， 个 别 处 感 觉 可 以 再 细 致 一 些 。 还 可 以 给 出 举 一 反 三 的 参 考 代 码,O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
特 别 喜 欢 老 师 讲 理 论 方 面 的 内 容,O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1
课 程 设 计 不 错 ， 作 业 相 结 合 很 用 心,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
内 容 设 置 非 常 合 理 ， 老 师 讲 解 也 十 分 细 致 。 理 论 与 实 例 结 合 更 加 有 利 于 理 解 。 非 常 喜 欢 这 个 课 程 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
讲 课 非 常 简 洁 明 了 ， 逻 辑 清 晰 ， 一 听 就 懂 ， 老 师 真 的 很 厉 害 ！,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 的 教 学 方 法 很 实 用 ， 节 奏 也 很 好 。,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O,1 1 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1
很 好 的 课 程 ， 老 师 讲 解 的 深 入 浅 出 ， 如 果 配 套 的 练 习 题 目 有 进 一 步 的 讲 解 就 更 好 了 ！,O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 2 2 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
希 望 以 后 老 师 能 多 带 着 我 们 做 一 些 练 习 就 更 好 了,O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 解 深 入 浅 出 ， 知 识 与 代 码 相 结 合 ， 对 一 段 代 码 逐 步 分 析 ， 让 学 生 更 容 易 继 续 学 习 下 去,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 极 好 ， 由 浅 入 深 ， 易 于 理 解,B-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 组 织 很 好 ， 尤 其 是 课 后 联 系 ， 紧 贴 授 课 内 容,B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 1 1 1 1
有 一 定 的 效 率 提 升 ， 但 无 法 与 课 堂 完 全 相 同 。,O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
非 常 非 常 棒 的 P y t h o n 课 ！ ！ ！,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 -1 -1 -1
老 师 讲 解 的 非 常 好 ， 每 个 知 识 点 都 穿 插 到 实 例 中 ， 实 例 设 计 非 常 实 用 和 有 意 思,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
非 常 不 错 ， 适 合 零 基 础 的 人 来 学 ， 但 好 像 不 够 精 深,O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
语 言 表 达 清 晰 条 理 ， 知 识 面 广 深 自 如 ， 高 水 平 讲 授 ， 不 愧 为 精 品 课 程 ， 谢 谢 北 理 嵩 天 老 师 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 2 2 -1
深 入 浅 出 ， 入 门 中 ， 大 获 裨 益 ， 准 备 持 续 学 习,O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2
言 简 意 赅 ， 全 是 干 货 ， 谢 谢 老 师 ！,O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
救 命 课 程 ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ ！ 1,O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 真 的 很 棒 ， 我 都 能 听 懂 ！,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
入 门 来 说 讲 的 非 常 好 ， 适 合 初 学 者,B-ASP I-ASP O O B-ASP O O O O O O O B-ASP I-ASP I-ASP,2 2 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 2
这 个 课 程 用 心 ， 内 容 循 序 渐 进 。 很 喜 欢 老 师 用 案 例 开 头 的 方 式 做 课 程 引 导 ， 更 容 易 有 目 标 感 。 有 些 语 法 细 节 记 不 太 清 楚 ， 但 可 以 快 速 地 记 得 哪 里 可 以 寻 找 到 需 要 的 答 案 ， 觉 得 对 自 己 后 续 的 学 习 非 常 有 帮 助 。,O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
课 程 安 排 合 理 ， 老 师 讲 解 细 致 ， 适 合 无 基 础 人 群 学 习 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1
p p t 精 致 ， 课 上 详 细 ， 课 下 练 习 合 理 ， 老 师 令 人 敬 佩 ， 谢 谢,B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O,2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
很 好 的 课 程 ， 老 师 讲 的 很 好 。 对 小 白 来 说 很 不 错 ， 不 过 自 己 还 是 要 多 练 练 。,O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 -1 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 孩 子 很 喜 欢 ， 下 次 还 来,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 好 ， l a o ' s h i ' j i a n g ' d e ' h e n ' x i l a o s h i j i a n g d e h e n x i n g x i n g ' x i,O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
无 法 理 解 这 门 国 家 精 品 课 程 ， 是 我 打 开 方 式 不 对 吗 ？ 内 容 空 泛 不 讲 实 质 内 容 ， 去 p y t h o n 1 2 3 看 发 现 又 要 收 费 ？ ？ ？ 第 一 次 在 慕 课 遇 到 这 种 情 况 。,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ， 对 于 打 好 基 础 ， 非 常 实 用 ， 知 识 点 讲 的 很 透 切 ， 通 俗 易 懂 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
第 一 感 觉 该 课 程 匠 心 打 造 ， 期 待 下 面 精 彩 的 内 容 ！,O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
非 常 不 错 ， 只 是 没 有 明 确 第 三 方 库 安 装 不 了 的 问 题,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 0 0
老 师 讲 解 的 非 常 详 细 。 我 这 种 小 白 用 心 认 真 去 听 和 理 解 ， 也 能 看 懂 课 堂 内 容 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
课 程 内 容 很 不 错 ， 课 时 短 小 精 炼 ， 对 于 维 持 学 生 的 学 习 积 极 性 方 面 有 很 大 的 作 用 。 同 时 感 觉 课 程 深 度 可 以 提 升 一 下 。,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1
希 望 老 师 可 以 再 增 加 一 些 案 例 分 析 与 实 战,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2
很 棒 ！ 一 边 看 网 课 一 边 在 学 校 跟 老 师 走 ， 能 巩 固 很 多 知 识,O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2
p y t h o n 是 一 个 比 较 有 趣 的 语 言 ， 比 较 人 性 化 ， 容 易 理 解,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 每 一 句 感 觉 都 是 重 点 ， 很 多 注 意 点 都 讲 到 了 。 学 编 程 还 是 要 多 理 解 ， 多 实 战 操 作 ， 慢 慢 领 悟 其 中 的 奥 妙 。 老 师 的 讲 解 很 棒 ， 真 真 循 序 渐 进 ， 而 且 干 货 多 多,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
很 不 错 ， 灰 常 棒 ， 我 爱 p y t h o n ！,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1
学 之 前 自 己 是 有 基 础 的 ， 所 以 学 起 来 感 觉 没 啥 压 力 ， 对 于 作 为 0 基 础 ， 特 别 是 那 些 从 没 接 触 过 的 ， 友 好 度 应 该 更 高 ， 并 且 授 课 的 方 式 很 有 特 点 ， 是 以 事 例 来 认 识 p y t h o n 的 一 些 关 键 字 和 语 法 。,O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 1 1 1 -1 1 1 -1
老 师 讲 课 思 路 清 晰 ， 0 基 础 也 容 易 跟 上 ， 非 常 棒,B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O,1 1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 详 细 ， 用 d e m o 辅 助 理 解 ， 很 好,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 但 还 可 以 针 对 完 全 小 白 的 学 习 者 把 某 些 细 节 再 讲 深 一 点,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
我 是 一 名 研 究 生 ， 虽 然 学 习 过 c 语 言 和 r ， 但 是 以 前 的 课 程 都 是 有 点 枯 燥 的 ， 老 师 一 般 只 讲 技 术 。 这 次 老 师 对 于 p y t h o n 应 用 领 域 的 介 绍 让 我 对 p y t h o n 有 了 强 烈 的 兴 趣 ， 感 觉 后 面 还 会 有 很 多 惊 喜 ， 非 常 期 待 后 面 的 学 习 了 。,O O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 0 0 -1 -1 -1 1 1 -1 -1 2 2 2 2 2 2 1 1 1 1 -1 2 2 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
讲 的 非 常 好 ， 深 入 浅 出 ， 课 下 还 是 自 己 要 多 多 复 习 实 践,B-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1
老 师 讲 的 太 好 了 ， 是 非 常 适 合 小 白 们 的 课 程,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 2 2
老 师 教 的 很 用 心 ， 很 有 层 次 ， 希 望 能 跟 着 老 师 更 好 的 学 习 P y t h o n,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1
很 系 统 且 详 细 地 教 学 ， 使 我 获 益 良 多,O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,-1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1
课 程 是 可 以 直 接 感 受 到 的 能 让 生 活 中 的 一 些 工 作 马 上 变 得 简 单 高 效 便 捷 的 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 教 的 很 详 细 ， 很 容 易 系 统 地 掌 握,B-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
视 野 广 ， 立 意 高 ， 老 师 还 有 系 列 课 程 ， 非 常 棒,B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 2 2 -1 -1 1 1 -1 -1 2 2 2 2 -1 -1 -1 -1
听 了 半 小 时 ， 觉 得 很 有 趣 ， 刚 好 自 己 也 想 学 p y t h o n ， 巧 了,O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1
老 师 把 知 识 点 做 了 细 分 处 理 ， 虽 然 是 简 单 的 知 识 点 ， 但 是 却 总 结 的 很 全 面,B-ASP I-ASP O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O,2 2 -1 2 2 2 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1
授 课 方 式 ： 希 望 一 节 完 整 的 课 时 的 视 频 不 要 分 割 成 几 十 个 视 频 ， 严 重 影 响 投 入 度 和 学 习 效 率,B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 0 -1 0 0 0 0
嵩 老 师 授 课 的 同 时 会 加 入 一 些 思 想 层 面 的 教 育 ， 很 有 指 导 的 意 义,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O I-ASP I-ASP,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2
中 国 大 学 M O O C 上 最 好 的 P y t h o n 入 门 教 材 ， 不 足 为 过 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 2 2 2 2 2 2 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 特 别 详 细 ， 自 我 感 觉 特 别 适 合 看 视 频 自 学 的 同 学 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1
学 习 的 是 自 动 化 ， 发 先 自 己 不 适 合 学 习 硬 件 ， 还 是 软 件 编 程 适 合 自 己 ， 要 跨 专 业 找 工 作 ， 所 以 现 在 抓 紧 学 习 。,O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 太 棒 了 ， 视 频 分 段 有 序 ， 比 长 段 视 频 更 好,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 1 1 1 1 -1 -1
感 谢 ! ! ! 嵩 老 师 讲 课 很 细 致 ~ 制 作 团 队 很 良 心 ~ 很 系 统 的 一 门 课 ! 大 爱,O O O O O B-ASP I-ASP I-ASP O B-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP O O O,-1 -1 -1 -1 -1 2 2 2 -1 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1
不 喜 欢 计 算 机 ， 还 是 以 网 课 形 式 ， 你 学 的 进 去 ？,O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 0 0 0 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 是 很 好 的 ， 老 师 讲 解 也 很 清 楚 ， 但 是 我 自 己 太 懒 散 跟 不 上 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 谢 相 遇 ， 此 时 无 声 胜 有 声 ， 紫 薯 布 丁 。,B-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 认 为 该 门 课 程 需 要 加 大 知 识 容 量 ， 同 时 对 知 识 进 行 更 为 系 统 化 的 讲 解 。 从 学 生 的 角 度 来 讲 这 是 我 目 前 能 够 看 到 的 最 大 的 不 足 之 处,O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 的 课 非 常 细 致 ， 通 过 案 例 分 析 和 逐 行 解 释 以 及 默 写 ， 作 为 初 学 者 的 我 不 至 于 轻 易 放 弃 。 相 信 跟 紧 老 师 的 步 伐 ， 学 完 这 个 课 程 我 还 是 会 对 p y t h o n 入 门 更 有 信 心 ！ 谢 谢 老 师 ！,B-ASP I-ASP I-ASP O O B-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O,2 2 2 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
很 好 ， 选 取 的 例 子 很 有 代 表 性 ！ 讲 解 很 给 力 ！,O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 -1 2 2 -1 -1 -1 -1
大 致 了 解 了 p y t h o n 编 程 的 快 乐,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 2 2
喜 欢 嵩 老 师 的 讲 课 风 格 ， 言 简 意 赅 ， 案 例 选 取 恰 到 好 处 。,O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
嵩 老 师 的 课 程 设 计 得 很 好 ， 零 基 础 学 起 来 也 没 压 力 。,B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
课 程 内 容 很 适 合 新 手 ， 容 易 理 解 ， 练 习 也 是 由 浅 入 深 ， 学 习 效 果 很 棒 。 必 须 点 赞 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,2 2 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
p y t h o n 交 流 ， 6 6 2 1 5 0 7 4 1 ( 不 是 卖 课 ) ​,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,1 1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
感 觉 挺 实 用 的 ！ 坚 持 学 下 去 定 有 收 获 ！,O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
内 容 组 织 结 构 框 架 非 常 好 ， 能 够 让 我 在 非 常 多 的 知 识 点 中 ， 明 确 好 分 类 和 框 架 。 能 够 让 我 更 系 统 的 去 学 习 这 门 课 。 非 常 棒 ！ ！ ！,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP O O O O O O O,2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 的 很 细 致 配 套 的 练 习 也 很 有 针 对 性,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
p y t h o n 1 2 3 上 找 作 业 时 要 求 提 供 课 程 代 码 ， 是 什 么 ， 请 告 知,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O,1 1 1 1 1 1 1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 的 内 容 非 常 的 全 面 和 系 统,B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O O,1 1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
本 来 对 p y t h o n 就 有 点 兴 趣 ， 北 理 的 这 堂 基 础 课 更 是 加 深 了 我 的 兴 趣 ， 老 师 们 讲 解 的 很 精 准 ， 实 例 操 作 也 很 有 趣 ， 谢 谢 老 师 和 助 教 的 指 导 ， 热 心 的 同 学 也 很 多 ， 不 会 的 问 题 很 快 被 解 答 了 。 共 性 问 题 看 看 其 他 同 学 的 问 题 和 解 答 就 好 了 。 总 体 来 说 ， 很 开 心,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 详 细 ， 适 合 P y t h o n 新 手 ， 很 棒,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是,O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 觉 得 老 师 课 程 内 容 的 条 理 性 和 章 节 分 类 很 清 晰 也 是 比 较 全 面 的 学 习 起 来 比 较 清 晰 ， 希 望 老 师 增 加 p y t h o n 应 用 领 域 的 深 度 课 程 如 数 据 分 析 方 面,O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 1 1 2 2 2 2 -1 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 2 2 2 2 2 2 2 2 -1 2 2 2 2 -1 1 1 1 1 1 1
老 师 讲 得 很 细 ， 很 容 易 理 解 ， 或 者 说 适 合 我 个 人,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
初 步 学 习 了 一 种 新 语 言 很 开 心,O O O O O O O B-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
感 谢 嵩 天 老 师 的 精 彩 生 动 的 讲 解 让 我 了 解 了 P y t h o n 的 基 础 语 法 ， 让 我 感 受 到 了 P y t h o n 的 巨 大 魅 力 。,O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1
课 程 内 容 很 充 实 ， 基 本 上 包 含 了 参 考 书 上 所 有 的 知 识 点 。 老 师 讲 课 也 很 有 趣 ， 能 够 很 轻 松 的 跟 着 老 师 的 节 奏 听 下 去 ~,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 2 2 2 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1
挺 不 错 的 ， 0 基 础 的 人 听 着 挺 舒 服 ， 教 学 安 排 和 逻 辑 设 计 都 很 合 理,O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1
老 师 教 学 方 法 新 颖 ， 讲 述 细 致 ， 我 这 样 的 小 白 都 能 轻 松 学 习 ~ 点 赞 ！,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O,1 1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 组 织 得 丰 富 又 有 趣 ， 老 师 讲 解 得 超 级 清 楚 ， 配 套 的 练 习 也 很 有 帮 助 。 尤 其 是 每 讲 解 一 个 主 题 之 后 可 以 动 手 做 出 点 东 西 让 人 非 常 有 成 就 感 ， 很 容 易 让 人 有 继 续 学 下 去 的 欲 望 。 真 的 非 常 感 谢 老 师 们 在 M O O C 平 台 上 分 享 这 门 课 ！ ！ ！ ！ P S : 助 教 老 师 答 疑 神 速 ~,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1
希 望 可 以 再 加 个 c l a s s 的 讲 解 。,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 2 2 -1
f e i c h e n g a h f e i,O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 好 的 课 程 ， 对 于 快 速 掌 握 语 法 很 有 效 果,O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2
首 先 ， 在 课 程 内 容 上 ， 老 师 对 P y t h o n 编 程 思 路 和 语 法 讲 解 得 非 常 详 细 ； 其 次 ， 在 每 听 完 一 单 元 的 P y t h o n 课 程 之 后 ， 都 有 相 对 应 的 习 题 留 给 我 们 去 做 ， 我 认 为 这 是 一 个 将 知 识 吸 收 、 巩 固 、 内 化 的 好 办 法 。,O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 2 2 2 2 -1 -1 2 2 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
讲 课 严 谨 细 致 ， 无 有 不 到 位 ， 太 感 谢 老 师 了 ！,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
讲 的 很 清 楚 很 好 ， 入 门 p y t h o n 良 选,B-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O,2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1
6 . 3 课 程 计 算 中 位 数 时 ， s o r t e d ( n u m b e r s ) ， 这 一 行 代 码 不 会 改 变 n u m b e r s 列 表 元 素 的 顺 序 ， 需 要 定 义 一 个 重 新 排 序 后 的 新 列 表 ， 再 对 新 列 表 计 算 中 位 数 。 n e w n u m b e r s = s o r t e d ( n u m b e r s ),O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 1 1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1
安 排 的 课 程 题 目 其 实 不 适 合 初 学 者 ， 我 有 一 定 编 程 基 础 还 勉 强 跟 得 上 。,O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
讲 解 内 容 清 晰 ， 容 易 理 解 ， p y t h o n 入 门 神 作,B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 2
非 常 好 ， 配 合 p y t h o n 1 2 3 简 直 太 完 美 了 ， 就 是 练 习 有 点 难 ， 超 出 老 师 讲 解 的 内 容 了 。,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 1 1 0 0 -1 -1 -1 -1 -1
教 学 设 计 合 理 ， 讲 述 精 确 ， 结 构 清 晰 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1
课 不 错 ， 章 节 循 序 渐 进 ， 作 为 入 门 级 课 程 还 是 很 给 力 的 。 遗 憾 的 是 可 能 是 时 间 太 紧 ， 具 体 的 代 码 讲 解 还 是 让 人 犯 迷 糊 ， 可 能 也 是 因 为 我 是 编 程 小 白 。,B-ASP O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O,2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 的 很 细 致 ， 内 容 设 置 的 也 很 好 ， 理 论 与 实 践 相 结 合 。 争 取 坚 持 到 底 ！ ！,B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 设 计 巧 妙 ， 讲 授 大 部 分 都 能 听 懂,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 的 太 好 了 ， 远 远 超 出 预 期 ， 感 谢 老 师 ！,B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
教 授 的 方 式 非 常 利 于 学 生 理 解 ， 这 一 点 上 超 过 外 面 的 培 训 机 构 ； 如 果 一 定 要 说 点 不 足 的 话 ， 嵩 天 老 师 能 不 能 系 统 地 ， 全 栈 讲 解 P Y t h o n 应 用 ， 而 且 要 注 意 是 商 业 级 别 的 ， 比 方 说 我 就 是 要 开 发 一 个 类 Q Q 程 序 。,O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1
这 里 讲 解 非 常 透 彻 ， 虽 然 只 是 基 本 内 容 ， 学 习 完 了 这 个 课 程 之 后 ， 自 学 深 入 内 容 毫 无 压 力,O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
课 程 的 编 排 与 众 不 同 ， 虽 然 只 是 一 门 程 序 设 计 语 言 ， 却 在 其 中 学 到 了 计 算 机 中 更 广 泛 的 内 容 。 一 门 课 顶 三 门 。,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP O O O O,2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 -1 -1 -1 -1
我 觉 得 讲 得 挺 好 的 ， 非 常 基 础 全 面 ， 适 合 入 门 小 白,O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
我 是 中 学 信 息 技 术 教 师 ， 下 个 学 期 就 要 换 新 教 材 了 ， 需 要 讲 P y t h o n 了 ， 正 好 利 用 不 开 学 的 这 段 时 间 每 天 学 习 ， 不 再 是 一 头 雾 水 无 从 下 手 了 。 课 程 很 好 ， 老 师 讲 的 也 很 好 ， 谢 谢,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O,-1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
不 错 ， 要 是 讲 解 的 再 详 细 一 点 就 好 了 ， 基 础 比 较 差,O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
嵩 老 师 讲 的 很 详 细 ， 课 件 也 做 的 很 不 错 。,B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
讲 解 内 容 很 详 细 ， 通 俗 易 懂 很 适 合 新 手 入 门,O O B-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
能 够 有 条 理 性 地 学 习 p y t h o n ， 整 体 框 架 清 晰 能 够 有 条 理 性 地 学 习 p y t h o n ， 整 体 框 架 清 晰,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1
老 师 讲 的 太 好 了 ， 特 别 适 合 基 础 为 零 的 小 白 学 习 ！,B-ASP I-ASP B-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 得 得 很 好 ， 很 有 系 统 性 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
感 觉 老 师 讲 的 很 好 ， 而 且 理 论 与 实 践 并 行 ， 并 没 有 让 人 感 觉 到 枯 燥 乏 味 ， 很 有 意 思 ， 本 以 为 会 很 难 的 内 容 ， 学 科 ， 可 如 今 当 你 喜 欢 上 时 ， 你 会 发 现 ， 其 实 ， 兴 趣 可 以 决 定 程 度 ！ 态 度 ！ 力 度 ！,O O B-ASP I-ASP B-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 1 1 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1
我 觉 得 在 慕 课 上 我 可 以 学 到 很 多 .,O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 上 课 认 真 ， 内 容 适 合 初 学 者 入 门 ， 很 不 错,B-ASP I-ASP O B-ASP O O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 丰 富 条 理 ， 讲 解 清 晰 易 懂 ， 案 例 简 洁 ， 设 计 合 理 。,B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,2 2 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1
课 程 通 俗 易 懂 ， 感 觉 听 了 都 会 。 课 后 也 有 任 务 ， 可 以 巩 固 学 习 内 容 。 只 是 练 习 还 不 够 多 ， 特 别 是 扩 展 练 习 ， 对 于 学 会 了 这 些 内 容 ， 不 太 明 白 能 做 多 少 东 西 ， 或 者 是 掌 握 内 容 不 够 熟 练 吧 。,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1
很 好 ， 循 序 渐 进 ， 课 程 难 度 适 宜 ， 初 学 者 也 能 听 懂,O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 要 是 讲 解 的 更 加 详 细 一 点 就 好 了,B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O,0 0 0 0 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 讲 了 理 论 之 后 ， 会 给 相 关 的 例 子 解 释 。 非 常 棒 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 的 讲 解 深 入 浅 出 ， 非 常 具 有 系 统 性 。 上 完 这 门 课 感 觉 把 自 己 原 来 自 己 摸 索 的 一 些 知 识 点 串 联 了 起 来 ， 而 且 老 师 的 讲 解 突 出 重 点 ， 很 好 掌 握 ！,O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O,-1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1
很 喜 欢 老 师 的 讲 课 方 式 ， 能 听 懂 。,O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1
不 明 觉 厉 ， 希 望 自 己 能 赶 紧 学 会 呀,O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
认 真 负 责 ， 生 动 有 趣 ， 非 常 喜 欢 嵩 天 老 师 的 课,O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2
老 师 的 讲 解 简 单 易 懂 ， 讲 授 的 功 能 也 很 实 用 ， 很 棒 ！,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
课 程 系 统 性 强 ， 结 合 实 例 ， 应 用 讲 解 ， 深 入 浅 出,B-ASP I-ASP B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O,2 2 2 2 2 -1 -1 -1 -1 1 1 -1 1 1 2 2 -1 -1 -1 -1 -1
唯 一 一 个 坚 持 学 下 来 的 自 学 课 程 ！,O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
挺 不 错,O O O,-1 -1 -1
从 很 基 础 开 始 学 习 P y t h o n ， 很 棒 ， 老 师 讲 的 很 好 。,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
让 我 进 步 很 多 ， 学 到 了 好 多 知 识 。,O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
深 入 浅 出 的 讲 解 ， 非 常 适 合 初 学 者 。,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 -1
我 作 为 初 学 者 ， 通 过 学 习 基 本 上 了 解 了 P Y T H O N 编 程,O O O B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1
大 学 生 百 度 确 实 可 以 当 成 入 门 课,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP,1 1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 2
很 不 错 不 过 对 于 小 白 来 说 一 节 课 要 反 复 听 几 次 才 行 跟 不 上 节 奏 也 正 常 吧,O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1
内 容 严 谨 ， 逻 辑 性 强 ， 希 望 增 加 更 多 实 例,B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 得 浅 显 易 懂 ， 我 觉 得 我 又 行 了 。 听 好 老 师 上 课 就 是 一 种 享 受 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 课 条 理 很 清 晰 ， 容 易 听 懂 ， 不 错 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 很 棒 ， 细 致 入 微 ， 面 面 俱 到,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
慕 课 制 作 团 队 专 业 ， 课 程 内 容 思 路 清 晰 易 懂,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O,2 2 2 2 2 2 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
老 师 真 的 超 级 好 ！ 听 了 课 很 多 没 太 懂 的 地 方 一 下 子 茅 塞 顿 开 了 ！,B-ASP I-ASP O O O O O O O O B-ASP O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 觉 收 益 良 多 ， 会 使 用 p y t h o n 真 的 很 方 便,O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1
内 容 框 架 层 层 递 进 ， 逻 辑 紧 扣 ， 非 常 好 ， 通 俗 易 懂,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
真 是 一 个 小 白 ， 从 这 边 学 习 到 了 能 编 写 个 小 应 用 程 序 的 程 度 ， 感 觉 很 有 成 就 ， 后 面 继 续 学 习 文 件 处 理 ， 工 作 中 也 就 用 的 上 了 。,O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 清 晰 形 象 ， c a s e s t u d y 趣 味 性 强 ， 很 容 易 上 手 。 非 常 适 合 零 基 础 的 同 学 ！,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 教 导 有 方 ， 感 触 良 多 ， 讲 解 耐 心 细 致 ， 一 整 个 爱 住 了,B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 非 常 好 ， 嵩 老 师 讲 得 很 好 ， 很 喜 欢 的 。,B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 老 师 真 是 神 仙 啊 ， 是 我 见 过 讲 的 最 通 透 的 课 程 。,O O B-ASP I-ASP O O O O O O O O O O B-ASP O O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 2 2 -1
嵩 天 老 师 的 课 通 俗 易 懂 ， 对 新 手 学 员 较 为 友 好 ， 感 谢 嵩 天 老 师 团 队 为 此 付 出 的 努 力,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP,1 1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2
p y t h o n 这 门 课 老 师 讲 的 很 好 很 适 合 入 门 学 习,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP B-ASP I-ASP O O O O O O O O O O O,1 1 1 1 1 1 -1 -1 2 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 觉 得 很 多 题 目 没 有 细 讲 ， 小 白 真 的 很 难 懂,O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
挺 好 的 ， 基 本 很 满 意 。 但 是 希 望 有 些 点 希 望 可 以 讲 解 更 细 致 。 对 课 后 的 自 由 编 程 。 希 望 有 程 序 解 析 ， 也 就 是 为 什 么 要 这 样 编 写 。,O O O O O O O O O O O O O O O O B-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 同 学 需 要 考 研 加 分 、 保 研 加 分 、 出 国 加 分 、 论 文 指 导 、 专 利 指 导 的 ， 请 联 系 ： 1 7 3 8 6 1 6 8 7 1 1 ( 微 信 同 号 ),O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 -1 -1 -1
希 望 课 程 脱 离 时 间 限 制 ， 给 学 习 者 更 多 自 由 时 间 选 择 学 习 时 间,O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 2 2 2 2
老 师 讲 得 很 好 ， 第 一 章 目 前 我 都 能 听 懂 。,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 和 其 他 相 比 很 不 错 ， 通 过 一 个 个 有 意 思 的 例 子 进 行 讲 解 ， 很 感 兴 趣,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 听 着 有 兴 趣 ， 也 学 到 了 很 多 知 识 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
非 常 棒 的 课 程 ， 感 谢 老 师 ！ 感 谢 平 台 ！,O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
深 入 浅 出 的 一 门 课 ， 学 起 来 比 较 有 趣,O O O O O O O B-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1
不 适 合 零 基 础 的 学 生 ， 很 多 理 论 知 识 铺 垫 ， 但 是 课 程 衔 接 不 好,O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 0 0 0 0 -1 -1
"h a o / / p o w ( 2 , 3 )",O O O O O B-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1
我 感 觉 老 师 讲 的 蛮 好 的 ， 就 是 遇 到 问 题 不 知 道 找 谁 了 ， 一 般 情 况 的 问 题 ， 我 自 己 百 度 可 以 解 决 或 者 再 c s d n 上 面 都 能 找 到 答 案 ， 但 是 有 的 问 题 实 在 难 以 理 解 ， 希 望 老 师 给 个 解 决 办 法,O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 2
嵩 天 老 师 的 课 讲 的 非 常 清 晰 易 懂 ， 是 非 常 好 的 课 程,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O B-ASP I-ASP,2 2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
这 个 课 程 适 合 没 学 过 编 程 的 人 ， 有 很 多 地 方 讲 的 不 是 很 仔 细 ， 运 算 符 讲 的 不 仔 细 ， 没 有 讲 很 深 入 ， 也 没 讲 运 算 符 优 先 级 和 结 合 性 ， 总 之 一 句 话 这 个 课 适 合 小 白 ， 例 子 分 析 是 分 析 了 ， 讲 的 不 是 很 细,O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O B-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 2 2 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 谢 这 么 好 的 公 益 课 ， 让 我 认 真 的 学 习 p y t h o n ， 有 朝 一 日 我 出 师 了 ， 也 要 把 公 益 之 心 传 递 给 有 需 要 的 人 ！,O O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 不 错 ~ 只 是 一 些 细 节 如 果 可 以 讲 得 再 细 点 ， 如 一 些 库 的 安 装 环 节 ， 错 误 如 何 解 决 等,O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 1 1 -1 0 0 -1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ， 点 赞 。 请 问 有 高 阶 语 法 吗 ， 关 于 做 E R P 方 向 的,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1
很 不 错 的 p y t h o n 课 程 ， 老 师 们 花 了 很 大 心 思 设 计 ， 讲 练 结 合 的 方 式 课 后 作 业 ， 推 荐,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 2 2 2 2 2 2 -1 -1 -1
有 些 具 体 问 题 转 换 为 编 程 问 题 ， 感 觉 挺 有 难 度 ， 不 是 特 别 容 易 想 明 白 。,O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
真 的 很 棒 不 过 还 是 建 议 稍 微 有 点 基 础 的 再 来 听 不 然 一 开 始 估 计 很 难 听 懂,O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 觉 和 老 师 一 起 学 习 让 我 的 学 习 非 常 有 规 律 ， 感 谢 老 师 ！ ！,O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1
嵩 天 老 师 讲 解 的 细 致 但 不 拖 沓 ， 将 枯 燥 无 味 的 编 程 与 具 体 实 例 结 合 ， 使 学 生 更 好 理 解 ， 这 门 课 程 很 适 合 零 基 础 的 同 学 来 学,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1
超 级 喜 欢 老 师 这 种 娓 娓 道 来 的 讲 授 方 式 。,O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
非 常 厉 害 的 老 师 ， 讲 课 简 单 易 懂 ， 逻 辑 清 晰 ， 就 是 我 太 笨 。 。 递 归 真 的 没 法 搞 定 。 。 。,O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 不 错 。 但 我 的 学 习 进 度 显 示 为 1 0 / 1 4 ， 而 我 所 有 的 视 频 都 看 了 ， 作 业 和 测 验 也 做 了 ， 不 知 是 系 统 原 因 还 是 我 操 作 不 正 确 ？,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1
从 嵩 老 师 的 讲 授 中 能 感 知 他 是 编 程 高 手 ， 我 是 编 程 小 白 ， 跟 随 嵩 老 师 团 队 学 习 编 程 有 信 心 的 。,O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O,-1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 1 1 0 0 -1 -1 -1 2 2 2 2 2 -1 -1 1 1 -1 2 2 -1 -1
学 习 编 程 ， 最 重 要 的 就 是 可 以 学 习 编 程 思 维,O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1
课 程 内 容 讲 解 的 很 清 晰 ， 容 易 接 受 ， 课 上 和 课 后 的 一 些 实 例 和 练 习 也 能 使 自 己 所 学 及 时 得 到 巩 固 ， 很 棒 的 一 门 课 程 ， 值 得 认 真 学 习 。,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP O O B-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 2 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
课 程 讲 解 详 细 ， 从 知 识 到 案 例 ， 逻 辑 结 构 清 晰 ， 课 堂 使 用 的 方 式 也 比 较 新 颖 有 趣 ， 老 师 很 亲 切 幽 默 ， 很 好 的 课 程 。,B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 1 1 -1 1 1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
嵩 天 老 师 讲 的 非 常 细 致 ， 全 面 ， 知 识 点 覆 盖 非 常 全 面 ， 讲 解 层 层 递 进 ， 浅 显 通 俗 但 是 该 讲 的 都 讲 到 了 。 虽 然 以 前 我 有 一 定 基 础 ， 但 是 从 来 没 有 这 么 细 致 全 面 的 学 习 了 解 ， 这 个 课 程 真 的 收 获 良 多 ， 值 得 一 遍 一 遍 的 学 习,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 P y t h o n 有 了 一 个 更 深 的 了 解,O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP,-1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2
科 班 与 野 课 毕 竟 不 同 ， 科 班 是 体 系 化 和 系 统 化 的 教 学 ， 我 更 喜 欢 前 者,B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O,2 2 -1 0 0 -1 -1 -1 -1 -1 2 2 -1 1 1 1 -1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 风 趣 幽 默 ， 比 较 有 代 入 感 ， 而 且 也 很 清 晰 ， 比 我 想 象 的 网 课 效 率 高,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
讲 的 比 较 详 细 ， 用 例 子 来 进 行 教 学 ， 更 加 通 俗 易 懂 。,O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1
这 门 课 程 让 我 完 成 了 从 一 个 编 程 小 白 到 了 解 了 一 些 p y t h o n 基 本 语 句 的 转 变 ， 还 是 有 收 获 的 ， 从 第 五 周 开 始 就 感 觉 难 度 加 大 了 ， 一 些 概 念 不 是 很 好 理 解 ， 需 要 多 多 练 习 才 能 掌 握 。,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
登 录 p y t h o n 1 2 3 网 站 后 需 要 加 入 课 程 才 能 做 练 习 ， 请 问 这 个 课 程 的 代 码 是 什 么 啊,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1
这 个 小 白 教 程 ， 老 师 的 课 程 设 计 和 组 织 教 很 不 错 ， 学 习 使 我 受 益,O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 2 2 -1 1 1 -1 2 2 2 2 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1
整 套 课 程 条 理 清 晰 ， 老 师 讲 解 细 致 入 微 ， 非 常 周 到 。 是 一 堂 非 常 棒 的 课 程 。,O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 种 p y t h o n 学 习 方 法 很 有 趣,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,-1 -1 2 2 2 2 2 2 2 2 2 2 -1 -1 -1
非 常 棒 ， 理 论 实 践 相 结 合 ， 还 有 课 后 题,O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2
感 谢 老 师 的 分 享 ， 让 我 有 机 会 学 习 到 含 金 量 非 常 高 的 课 程 。,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1
可 否 再 一 次 开 放 一 下 1 7 期 证 书 申 请 呢 ， 错 过 了 5 月 2 5 日 这 一 批 证 书 申 请 · · · ·,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1
老 师 讲 的 深 入 浅 出 ， 易 懂 。 但 是 熟 练 应 用 还 需 好 好 练 习 ， 不 求 通 过 学 习 这 门 课 程 具 体 能 解 决 多 少 具 体 问 题 ， 学 习 一 种 思 维 方 式 也 不 错,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
讲 解 很 细 致 ， 重 要 知 识 点 详 细 列 出 ， 作 业 和 知 识 点 环 环 相 扣 ， 有 利 于 掌 握 。,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
超 级 棒 ， 讲 解 的 和 清 晰 透 彻 ， 入 门 简 单,O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
很 好 的 老 师 ， 很 好 的 课 程 ， 获 益 良 多,O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 2 2 -1 -1
嵩 天 老 师 讲 课 风 格 生 动 有 趣 ， 引 人 入 胜 ， 例 子 详 实 有 趣,B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
思 路 清 晰 ， 例 子 典 型 。 重 点 突 出 。,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1
我 认 为 对 于 有 一 点 儿 编 程 概 念 的 初 学 p y t h o n 人 员 来 说 还 是 很 好 的 课 程 。,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
好 ， 希 望 可 以 在 慕 课 直 接 布 置 选 择 题 作 业,O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 2
老 师 讲 解 得 很 透 彻 ， 示 列 很 典 型 ， 对 学 习 很 有 帮 助 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1
嵩 老 师 的 课 很 棒 ！ 谢 谢 ！ 再 次 学 习 p y t h o n 收 获 很 多 ， 很 喜 欢 老 师 讲 完 知 识 再 讲 如 何 应 用,B-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP,1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 2 2
老 师 的 讲 解 很 生 动 ， 适 合 初 学 者 学 习 。,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
强 烈 推 荐 想 学 习 p y t h o n 的 人 看 这 个 课 程 。 嵩 天 老 师 解 释 各 种 含 义 是 都 是 比 较 符 合 大 众 认 知 的 ， 完 全 可 以 很 轻 松 的 g e t 到 要 点 。 还 有 p y t h o n 1 2 3 这 个 平 台 ， 两 者 配 合 使 用 ， 1 ＋ 1 > 2 的 效 果 ！,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 2 2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
超 值 ， 收 获 很 大 ， 配 套 习 题 再 多 点 更 完 美,O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
"课 程 讲 解 不 错 , 对 于 小 白 来 说 很 友 好 , 边 看 边 学 感 觉 进 步 很 快 .",B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
对 于 没 有 一 点 编 程 基 础 的 童 鞋 来 说 太 难 了 ， 需 要 多 听 几 遍,O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
无 论 是 课 程 设 置 ， 时 间 掌 握 ， 知 识 讲 解 都 称 得 起 国 家 精 品 这 个 头 衔,O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP,-1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 2 2
非 常 适 合 入 门 选 手 ， 看 完 视 频 一 定 要 再 看 看 推 荐 书 目 。,O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
老 师 们 讲 的 超 级 赞 ， 编 程 小 白 顺 利 入 门 ， 已 经 在 升 级 ， 谢 谢 课 程 主 讲 老 师 们 ， 以 及 精 美 的 录 制 ， 很 赞,B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O,2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
感 觉 老 师 教 的 很 细 心 ， 课 程 安 排 的 也 很 好 ， 结 合 教 材 ， 先 预 习 ， 后 看 视 频 ， 最 后 做 练 习 ， 感 觉 自 己 提 升 很 快,O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 非 常 好 ， 课 程 结 构 设 计 清 晰 ， 内 容 讲 授 非 常 适 合 P Y T H O N 打 基 础 ， 讲 得 也 很 有 条 理 。 真 的 是 非 常 优 秀 的 一 门 课 ！,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP O,2 2 2 2 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1
讲 解 清 楚 ， 但 是 对 于 初 学 者 来 说 ， 还 是 有 点 难 度 。 可 能 是 对 于 标 准 库 的 含 义 不 太 了 解 ， 如 果 老 师 能 在 应 用 标 准 库 时 能 稍 微 解 释 一 下 ， 库 的 含 义 ， 就 更 好 了,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 课 深 入 浅 出 ， 讲 的 特 别 明 白 ， 是 我 听 过 的 最 好 的 p y t h o n 课,B-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP,2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2
老 师 讲 解 的 很 仔 细 ， 学 起 来 能 跟 上,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 就 是 能 不 能 再 开 一 个 p y t h o n 进 阶 课 程 ， 哈 哈 哈,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 2 -1 -1 -1 -1
全 面 且 细 致 ， 特 别 是 对 初 级 学 生 来 说 很 好 的 兼 顾 了 易 学 性 。 同 时 ， 切 入 了 大 多 数 课 程 学 习 者 的 需 求 ， 实 用 性 也 很 强 。,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 1 1 -1 2 2 2 -1 -1 -1 -1
打 字 速 度 太 快 了 ， 不 知 道 怎 么 想 的 ， 我 是 大 佬 我 还 来 看 这 个 吗 ， 就 不 能 打 的 慢 一 点 吗 ？ 还 有 中 英 文 输 入 法 切 换 太 快 了 吧 ， 这 些 符 号 我 要 搞 半 天 ， 其 他 的 都 很 好 ， 超 出 预 期 了,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
希 望 可 以 加 入 一 些 编 程 引 导 讲 解 ， 这 样 对 小 白 入 门 会 轻 松 容 易 点 。,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 系 统 精 练 ， 选 题 作 业 很 有 针 对 性 ， 觉 得 学 到 了 很 多 。 自 己 还 有 多 练 多 熟 悉 才 能 掌 握 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 觉 老 师 讲 得 特 别 好 ！ ！ 非 常 喜 欢 ！,O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
在 如 此 成 熟 和 有 质 量 的 课 程 上 ， 学 生 感 到 十 分 幸 运 ， 老 师 和 同 学 们 一 起 进 步 哦 ！,O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 -1 -1 2 2 -1 -1
老 师 讲 课 程 真 的 很 棒 ， 而 且 那 些 实 例 也 是 很 吸 引 人 很 有 趣 ， 让 我 总 是 想 快 点 加 油 学 习 后 面 的 课 程 ， 每 次 听 课 都 充 满 期 待 。,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1
希 望 可 以 在 作 业 提 交 之 后 会 出 现 一 个 对 作 业 讲 解 的 视 频,O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2
老 师 讲 课 浅 显 易 懂 ， 没 有 一 句 多 余 的 话 ， 满 满 干 货 ， 真 正 实 现 从 小 白 到 入 门 。,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
很 好 ， 希 望 跟 着 老 师 学 ， 可 以 通 多 计 算 机 二 级,O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 2
讲 的 很 细 致 ， 通 俗 易 懂 ， 可 惜 我 没 有 早 点 开 始 学 习 ， 不 能 赶 在 结 课 前 学 习 完 毕 了 。 。 。,O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 难 易 合 理 ， 通 俗 易 懂 。 希 望 再 添 加 一 些 编 程 题 巩 固 知 识 点,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 2
老 师 讲 课 风 格 简 单 干 练 ， 每 节 课 的 内 容 也 不 会 太 长 ， 能 够 在 短 时 间 集 中 掌 握 知 识 点 ~ 谢 谢 老 师 们 的 用 心 ~ 作 为 一 个 文 科 背 景 的 编 程 小 白 ， 希 望 可 以 获 得 更 加 多 的 细 节 详 解 ， 比 如 说 ， 什 么 情 况 要 用 大 括 号 、 中 括 号 ， 列 表 里 ， 字 母 还 是 数 字 要 加 引 号 呢 ？ 有 点 混 乱 ， 还 需 要 自 己 加 强 消 化 ~,B-ASP I-ASP O O O O O O O O O O O B-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 1 -1 1 1 -1 -1 1 1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
动 态 数 据 爬 取 验 证 码 这 些 能 继 续 有 课 就 好 了 老 师 是 偶 像,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O B-ASP O O O B-ASP I-ASP O B-ASP I-ASP,2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 2 2 -1 2 2
内 容 丰 富 ， 语 言 得 体 ， 学 有 所 获 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
超 棒 ， 不 过 关 键 是 自 己 去 多 理 解 和 练 习 ， 师 傅 领 进 门,O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 1 -1 -1 -1
真 得 好 ！ 谢 谢 老 师 。 希 望 推 出 面 向 对 象 的 程 序 设 计 p y t h o n,O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
老 师 讲 课 由 浅 入 深 ， 让 我 这 个 P Y T H O N 小 白 觉 得 很 赞,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1
嵩 天 老 师 及 团 队 是 不 可 多 得 的 好 老 师,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP,2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2
上 课 听 懂 了 ， 但 是 课 下 编 程 题 目 做 不 出 来,O B-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1
老 师 讲 得 很 棒 ， 作 为 转 行 的 人 很 好 入 门,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
可 以 学 习 很 多 知 识 ， 很 有 用 ， 谢 谢 老 师,O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2
讲 的 很 细 ， 也 很 好 理 解 。 案 例 选 的 挺 好 ， 对 理 解 基 础 语 法 很 有 帮 助 。 课 程 设 计 和 量 也 适 中,B-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 1 -1 -1 -1
自 顶 而 下 的 课 程 讲 解 设 计 ， 思 政 与 思 维 内 容 巧 妙 的 融 合 ， 讲 授 方 法 更 适 合 自 学 能 力 强 与 有 学 习 主 动 性 与 积 极 性 的 学 生 。 老 师 课 程 设 计 方 法 与 思 维 值 得 学 习 。,O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 -1 1 1 1 1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1
课 程 内 容 组 织 科 学 ， 讲 解 精 辟 到 位 ， 既 有 知 识 传 授 ， 又 有 理 念 传 达 ， 是 值 得 研 读 的 好 课 程,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
课 程 内 容 简 明 扼 要 ， 我 零 基 础 都 可 以 上 手 ， 很 棒 的 课 ！ 很 棒 的 老 师 ！,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 2 2 -1
深 有 感 触 ， 感 觉 自 己 的 p y t h o n 又 提 高 一 阶,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1
以 前 也 读 过 一 些 关 于 P y t h o n 入 门 的 书 ， 感 觉 设 计 的 很 混 乱 ， 这 门 课 不 同 ， 设 计 的 非 常 好 ， 趣 味 性 也 比 书 上 的 要 强 得 多 。,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP O O O B-ASP I-ASP O O O O O O O B-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O B-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 0 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 1 -1 -1 -1 -1 -1 -1 -1
寓 教 于 乐 ， 课 程 内 容 生 动 有 趣 ， 但 又 干 货 满 满 。,O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 很 系 统 ， 很 有 趣 ， 想 一 直 学 下 去,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP O O O O O O O,1 1 -1 -1 -1 2 2 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1
授 课 方 式 很 好 ， 从 实 例 出 发 更 能 使 学 生 记 忆 深 刻,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
老 师 讲 解 的 生 动 形 象 ， 也 有 结 合 P P T 和 采 用 吸 引 人 的 动 画 效 果 ， 并 加 以 大 量 练 习 ， 缺 点 就 是 有 的 题 目 有 点 超 越 知 识 点 ， 需 要 一 定 时 间 去 吸 收 ， 但 不 影 响 这 是 一 门 优 秀 的 p y t h o n 入 门 课 程 ， 希 望 后 续 还 能 学 习 到 嵩 天 老 师 讲 解 的 关 于 p y t h o n 的 进 阶 课 程,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2
老 师 讲 的 非 常 的 清 晰 ， 引 导 的 也 非 常 的 好 ， 很 不 错,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
还 不 错 哦 ， 先 举 个 实 例 ， 再 讲 基 本 概 念 ， 更 有 助 于 理 解,O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
为 什 么 不 能 给 半 星 ， 看 完 第 一 周 后 就 知 道 这 是 个 非 常 糟 糕 的 课 程 ， 浪 费 时 间 ， 一 点 也 不 基 础 ， 废 话 太 多 ， 教 条 式 的 网 课 ， 可 见 课 堂 上 的 教 学 方 式 影 响 深 远 ， 根 深 蒂 固 了 ， 非 常 佩 服 能 看 完 的 同 学 ！,O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 0 0 0 -1 0 0 -1 -1 -1 0 0 0 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 的 很 清 晰 。 每 个 实 战 也 非 常 有 意 思 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 课 ， 三 个 字 ： 太 棒 了 ！ 对 课 程 ： 爱 死 了 ！ 好 老 师 水 平 就 是 不 一 样 。 《 P y t h o n 语 言 程 序 设 计 》 不 仅 能 学 到 很 多 的 p y t h o n 基 础 知 识 ， 还 能 学 到 很 多 的 编 程 思 维 。 对 于 没 有 基 础 的 人 ， 也 非 常 适 合 。 给 一 个 大 大 的 赞 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 到 了 很 多 关 于 P y t h o n 的 基 础 知 识 ， 以 及 编 程 思 维,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 2 2 2 2
嵩 老 师 很 温 柔 ， 也 很 幽 默 ， 非 常 好 的 一 次 学 习 体 验,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
老 师 讲 的 很 棒 偏 入 门 希 望 有 更 多 的 高 级 一 点 教 程,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
非 常 棒 ， 能 分 享 到 这 么 好 的 教 育 资 源 乃 人 生 中 的 大 幸,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
这 个 老 师 的 一 句 话 让 我 产 生 强 烈 共 鸣 ： 工 具 决 定 思 维,O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2
内 容 由 浅 入 深 ， 非 常 容 易 理 解 ， 尤 其 是 画 图 形 那 部 分 的 设 计 ， 很 吸 引 人 ， 可 以 扩 展 出 多 种 图 形 的 画 法 ， 温 度 转 换 内 容 的 设 计 ， 通 过 简 单 的 案 例 ， 就 学 习 了 非 常 多 的 p y t h o n 知 识 ， 感 谢 老 师 们 的 精 心 设 计 !,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1
结 构 严 谨 ， 比 较 容 易 学 习 ， 课 程 很 棒,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
老 师 上 课 讲 的 非 常 棒 ， 循 环 渐 进 ， 逻 辑 清 晰 ！,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
初 次 涉 及 编 程 感 觉 相 当 不 错 讲 解 再 详 细 一 点 就 更 好 了,O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 十 分 有 味 道 ， 理 论 与 实 例 相 结 合 ， 同 时 实 践 度 非 常 高 ， 语 言 通 俗 易 懂 ， 很 棒 ！ 强 烈 推 荐 ！,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 觉 得 这 门 课 的 唯 一 缺 点 就 是 讲 得 太 过 详 细 了,O O O O O B-ASP O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 0 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 课 十 分 有 条 理 性 ， 听 完 受 益 匪 浅 ！,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 精 彩 。 感 谢 嵩 天 老 师 和 您 的 团 队 ！,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1
老 师 ， 证 书 ， 怎 么 不 能 申 请 了 。 我 也 不 知 道 今 天 结 束 证 书 申 请 。 不 是 说 下 次 开 课 ， 就 申 请 证 书 吗 ？ 我 还 以 为 要 等 很 久 ， 没 有 想 到 今 天 打 开 ， 证 书 申 请 就 结 束 了 。 能 否 再 开 通 一 下 证 书 的 申 请 。 谢 谢,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O,1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 0 -1 -1 -1
课 程 是 好 的 ， 但 为 什 么 暑 假 开 课 把 上 一 次 的 重 置 了 ， 重 开 的 到 第 四 周 ， 后 面 的 课 还 要 等 着 重 新 开 ， 本 来 准 备 复 习 的,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O O O O B-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 太 好 了 ， 循 序 渐 进 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
能 感 觉 到 老 师 和 助 教 在 用 心 准 备 课 程 ， 谢 谢 你 们 ， 我 在 天 天 向 上 ！,O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 件 简 洁 凝 练 ， 干 货 率 爆 满 ！ 老 师 讲 解 清 晰 ， 语 言 轻 松 ！ （ 表 白 嵩 老 师 ~ ~ ❤ ）,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1
基 础 知 识 讲 解 到 位 ， 难 度 逐 步 增 加 ， 练 习 题 太 少 ， 前 面 知 识 容 易 遗 忘 ~,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,1 1 1 1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
很 喜 欢 嵩 天 老 师 上 课 ， 上 课 内 容 特 别 有 条 理 ， 很 系 统 ， 还 有 练 习 题 ， 学 到 了 很 多 知 识 ！ ！ 非 常 棒 的 课 程,O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,-1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2
假 期 自 学 P y t h o n ， 可 以 说 崇 天 老 师 的 讲 解 让 我 非 常 容 易 地 了 解 了 p y t h o n ， 互 联 网 资 源 共 享 真 的 是 让 人 享 受 到 了 更 多 更 好 的 学 习 资 源 ， 最 后 只 想 引 用 一 下 慕 课 一 句 经 典 的 台 词 “ 好 的 大 学 没 有 围 墙 ”,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
边 学 边 练 ， 适 合 小 白 层 层 深 入 学 习 ， 谢 谢 老 师 们 精 心 准 备,O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1
课 程 安 排 非 常 合 理 ， 讲 解 通 俗 易 懂 ， 截 止 到 目 前 ， 这 是 我 第 一 个 坚 持 学 完 的 课 程 ！ 希 望 后 续 还 可 以 出 一 些 进 阶 课 程 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1
这 堂 课 的 设 置 为 基 础 语 法 体 系 ， 非 常 适 合 入 门 学 生 的 初 步 学 习,O O B-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 2 -1 2 2 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 详 细 ， 感 觉 到 了 编 程 的 乐 趣,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2
课 程 设 计 很 用 心 ， 老 师 讲 的 很 好,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1
真 的 是 很 适 合 小 白 ， 表 示 一 个 学 新 闻 的 硕 士 生 在 学 习 数 据 抓 取 ！,O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 -1 -1 -1 1 1 1 1 -1
非 常 不 错 ， 希 望 更 多 学 校 推 出 自 己 的 特 色 课 程,O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2
浅 显 易 懂 ， 知 识 层 面 广 博 ， 重 点 突 出 ， 循 序 渐 进 ， 生 动 有 趣 。,O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 好 ， 提 升 了 对 p y t h o n 的 兴 趣,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2
老 师 讲 的 非 常 好 ！ n i c e !,B-ASP I-ASP B-ASP O O O O O O O O O O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 详 细 ， 能 听 得 懂 ， 学 到 了 很 多 知 识,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
很 N i c e 的 课 程 ， 老 师 讲 的 也 通 透 易 懂 ， 努 力 学 习 i n g,O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
谢 谢 老 师 们 的 静 心 录 课 ， 学 习 到 了 很 多 ， 爱 心 ！ 希 望 老 师 能 出 更 好 通 俗 易 懂 的 课 程 ， 感 恩,O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O,-1 -1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
这 个 课 程 很 棒 ， i t i s t h e b e s t o n e i e v e r h a d,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 特 别 清 晰 ！ 而 且 实 例 也 很 有 趣 ~ ！ 喜 欢 ！ 希 望 老 师 能 出 后 续 课 程 ~ ~,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 2 2 -1 -1
课 程 非 常 不 错 老 师 讲 解 细 致 课 后 练 习 有 趣,B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1
老 师 讲 得 很 清 晰 ， 对 小 白 的 我 很 有 用 。,B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
1 . 课 程 内 容 设 置 合 理 ， 循 序 渐 进 ， 条 理 清 晰 ， 学 习 起 来 轻 松 易 理 解 ； 2 . 老 师 授 课 水 平 高 ， 语 言 逻 辑 清 晰 ， 课 程 结 合 例 子 ， 讲 解 生 动 形 象 ； 非 常 感 谢 课 程 团 队 ， 感 谢 嵩 天 老 师 ， 谢 谢 你 们 给 我 们 提 供 这 么 优 质 的 课 程 ！,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
讲 解 细 致 ， 条 理 清 楚 ， 不 过 感 觉 练 习 量 少 了 ， 如 果 能 强 调 一 下 重 点 的 程 序 语 法 就 更 好 了,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1
老 师 讲 的 太 棒 了 ， 内 容 清 晰 紧 凑 ， 知 识 点 的 提 示 对 初 学 者 非 常 友 好,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
个 人 是 自 学 ， 很 多 知 识 点 模 糊 ， 做 题 不 知 道 怎 么 下 手 。,O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 1 1 -1 -1 -1 0 0 0 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 棒 ， 特 别 是 举 一 反 三 的 模 块 ， 做 到 了 引 起 学 生 思 考 ， 这 种 模 式 不 仅 仅 适 用 于 p y t h o n 的 学 习 ， 也 适 用 于 日 常 其 它 内 容 的 学 习,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
内 容 丰 富 ， 讲 解 到 位 ！ 非 常 感 谢 老 师 们 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
挺 好 的 ， 很 实 用 ， 很 感 谢 老 师 的 辛 勤 付 出,O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 得 非 常 详 细 ， 课 程 编 排 十 分 系 统,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
老 师 讲 的 很 详 细 ， 重 难 点 处 也 会 着 重 的 介 绍 。 其 次 ， 老 师 的 鸡 汤 很 好 喝,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1
这 条 理 清 晰 ， 知 识 点 足 。 需 要 深 入 学 习,O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O,-1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 得 很 仔 细 ， 对 于 一 些 不 明 白 得 地 方 ， 可 以 多 次 回 看 加 深 印 象 ， 而 且 很 容 易 找 到 。 老 师 得 水 准 很 高 ， 很 高 兴 有 这 些 学 习 资 源 提 供,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1
老 师 讲 的 特 别 好 ， 对 于 一 个 编 程 小 白 来 说 感 觉 课 程 设 置 很 不 错 ， 要 是 再 多 一 些 实 例 讲 解 就 更 好 了 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1
感 觉 变 化 挺 大 的 ， 以 前 的 数 学 思 维 与 现 在 的 计 算 思 维 之 间 的 过 渡 其 实 还 是 一 个 坎 儿 ， 需 要 不 断 地 努 力 训 练 ， 才 能 更 好 的 掌 握 这 一 门 语 言 。,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 解 的 很 好 ， 就 像 在 课 堂 里 上 课 一 样 ！,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
课 程 内 容 非 常 喜 欢 ， 适 合 初 学 者 ， 老 师 讲 解 的 很 好 ， 内 容 设 计 例 子 都 很 容 易 理 解,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 2 2 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
感 觉 讲 的 很 不 错 ， 不 仅 能 让 我 学 到 编 程 基 础 知 识 ， 还 为 整 个 P y t h o n 做 出 比 较 全 面 的 讲 解 ， 如 果 能 坚 持 学 ， 反 复 学 ， 一 定 会 有 很 大 收 获,O O B-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
觉 得 很 棒 ， 每 一 个 视 频 都 值 得 去 好 好 听,O O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 棒 五 星 好 评 下 次 再 来,B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O,2 2 -1 -1 -1 -1 2 2 2 2 1 1 -1 -1
目 前 视 频 看 前 三 周 ， 这 个 老 师 讲 的 也 太 好 了 吧 ！ ！ ！ 我 非 常 喜 欢 这 个 老 师 ！ ！ ！ 第 一 次 看 视 频 看 到 不 想 睡 觉 ， 加 油 ！ ！ ！,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 精 练 ， 讲 解 精 辟 ， 学 习 捷 径 。,B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
弹 性 学 习 ， 课 程 安 排 轻 松 。 老 师 讲 解 详 细 。 加 油 ！,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O,2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1
听 完 第 一 节 ， 老 师 讲 的 内 容 层 层 深 入 ， 非 常 适 合 小 白 学 习 ， 是 慕 课 平 台 中 几 个 讲 解 p y t h o n 中 最 为 详 实 易 懂 ， 感 谢 老 师 ~,O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,-1 -1 1 1 1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
相 信 授 课 老 师 的 专 业 能 力 ， 希 望 自 己 能 够 持 续 完 成 本 期 完 整 课 程,O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
就 是 老 师 没 有 教 我 们 ‘ 怎 么 安 装 P y t h o n 和 e i d ’,O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O,-1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 -1
嵩 天 老 师 讲 的 很 好 ， 很 细 致 ， 很 有 条 理 ， 理 论 与 实 例 相 结 合 让 人 更 容 易 理 解 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
该 内 容 对 于 p y t h o n 有 基 础 了 解 的 同 学 更 易 接 受,O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 2 2 -1 -1 1 1 1 1 1 1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1
老 师 讲 的 很 明 白 ， 很 好 懂 ， 讨 论 区 的 问 题 能 够 得 到 很 快 回 答 ， 小 白 福 音 。 老 师 辛 苦 ， 感 谢 ！ ！ ！,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
从 目 前 的 课 程 来 看 的 确 是 非 常 好 的 。,O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
初 次 接 触 编 程 ， 老 师 讲 的 全 面 又 清 晰 ， 学 的 比 较 明 白 ， 从 第 三 章 开 始 感 觉 出 了 难 度 ， 需 要 自 己 慢 慢 研 究 了,O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 完 善 ， 视 频 制 作 精 美 ， 学 习 效 果 非 常 好 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
非 常 好 ， 讲 解 得 非 常 细 腻 。 中 途 开 始 参 课 ， 希 望 课 程 到 期 后 也 可 以 继 续 学 习 。,O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 不 错 的 课 程 ， 不 愧 是 北 京 理 工 大 学 的 团 队 ， 对 没 有 编 程 基 础 的 小 白 来 说 非 常 的 友 好 。,O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
真 的 很 不 错 ， 收 获 满 满 ， 超 出 了 我 的 预 期 。,O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
细 节 讲 的 很 到 位 ， 越 听 越 觉 得 老 师 很 有 水 平 。,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1
课 程 非 常 好 ， 通 俗 易 懂 ， 由 浅 入 深 。,B-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 好 ， ， ， 只 是 进 来 听 课 时 间 太 晚 ， 有 点 跟 不 上,O O O O O O O O O O O B-ASP B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 0 0 -1 -1 -1 -1 -1 -1 -1 -1
p y t h o n 的 基 础 知 识 讲 解 得 很 透 彻,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O,1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
讲 解 浅 显 易 懂 ， 更 棒 的 是 授 课 时 添 加 一 些 例 子 ， 既 巩 固 了 所 学 知 识 ， 又 能 激 发 我 们 对 编 程 的 兴 趣 和 并 获 得 成 就 感 。,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 2 2 2 -1
每 个 视 频 简 短 ， 更 有 利 于 我 们 的 接 受 ， 要 不 太 长 的 话 听 了 想 睡,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 谢 嵩 天 老 师 ， 每 节 课 都 能 有 不 一 样 的 地 方 ， 看 的 停 不 下 来,O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 2 2 2 2 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 详 细 ， 内 容 充 分 ， 但 有 一 些 小 瑕 疵,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 0 0 0
课 程 深 入 浅 出 、 非 常 适 合 入 门 学 习,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 老 师 讲 的 课 程 生 动 有 趣 ， 例 题 具 有 代 表 性 ， 引 人 深 思 老 师 讲 的 课 程 生 动 有 趣 ， 例 题 具 有 代 表 性 ， 引 人 深 思,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 的 讲 解 很 生 动 ， 边 实 践 边 学 习 我 觉 得 对 我 的 帮 助 很 大 ， 而 且 这 样 的 学 习 能 很 好 的 激 发 学 生 的 学 习 兴 趣 ， 老 师 讲 的 也 挺 好 的 。,B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 得 太 好 了 ， 思 路 清 晰 ， 语 言 逻 辑 性 很 强 。 超 赞,B-ASP I-ASP I-ASP B-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1
以 初 学 者 的 角 度 出 发 ， 深 入 浅 出 ， 有 理 论 有 实 践 ， 用 心 了 。,O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1
"m o o k [ "" 学 习 感 受 "" ] = "" 很 棒 """,B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,1 1 1 1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
懂 得 p y t h o n 是 怎 么 一 回 事 ， 可 以 绘 制 简 单 的 图 形 ， 用 计 算 机 解 决 一 些 重 复 性 问 题 。,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 1 -1 -1 -1 -1 0 0 0 0 0 -1
收 获 到 了 很 多 的 专 业 知 识 和 感 悟,O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2
课 程 深 入 浅 出 ， 非 常 生 动 ， 非 常 感 谢 嵩 天 老 师 ， 还 安 利 给 了 其 他 小 伙 伴 ， 还 会 继 续 学 习 的 ！ ！ ！,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
跟 着 老 师 思 路 走 ， 编 程 思 路 清 晰 很 多 ， 我 还 可 以 用 自 己 的 方 法 改 造 程 序,O O B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP,-1 -1 1 1 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1
这 逻 辑 特 别 清 晰 ， 学 的 很 清 楚 ！ 特 别 喜 欢 跟 着 写 和 听 写 的 部 分 ， 知 道 自 己 学 的 怎 样 了 。 就 是 练 习 部 分 有 点 少 ， 可 以 在 增 加 一 些 。 感 谢 老 师 ！,O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,-1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
我 本 人 是 个 小 白 ， 感 觉 这 篇 课 程 是 一 个 工 具 书 类 的 存 在 ， 现 在 我 学 习 后 边 的 课 程 时 ， 遇 到 不 会 的 ， 就 来 翻 本 课 。 这 篇 课 程 如 果 能 做 一 个 索 引 或 内 容 提 要 类 的 说 明 就 更 好 了 。,O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 对 新 手 很 友 好 ， 讲 解 的 内 容 都 很 详 细 ， 并 配 有 实 例 ， 学 习 起 来 生 动 有 乐 趣 ！ 感 谢 课 程 组 的 老 师 和 工 作 人 员 ！ 也 谢 谢 平 台 ~,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1
老 师 讲 得 很 好 ， 但 是 p y t h o n 1 2 3 上 的 练 习 题 跟 讲 课 内 容 对 比 起 来 太 难 了 ， 跳 度 太 大 了 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1

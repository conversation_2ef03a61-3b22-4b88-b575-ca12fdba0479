text,bio,pola
老 师 讲 得 很 好 ， 但 是 p y t h o n 1 2 3 上 的 练 习 题 跟 讲 课 内 容 对 比 起 来 太 难 了 ， 跳 度 太 大 了 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1
学 习 内 容 安 排 的 很 细 致 ， 让 我 从 浅 入 深 的 进 行 学 习 。 课 程 也 很 有 趣 ， 让 我 能 够 沉 入 进 去 学 习 ， 每 一 节 课 的 时 间 不 算 太 长 ， 适 合 碎 片 化 学 习 。 继 续 学 习 ， 继 续 加 油 吧 ！ ~,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 实 用 的 课 程 ， 可 以 很 好 的 把 这 些 串 起 来 。 老 师 棒 极 了,O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
讲 的 很 细 ， 内 容 丰 富 ， 同 时 很 锻 炼 动 手 能 力,O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
个 人 觉 得 老 师 讲 得 很 不 错 ， 特 别 是 p y t h o n 的 计 算 生 态 ， 让 我 从 一 个 新 的 角 度 认 识 了 p y t h o n 这 门 语 言,O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
0 基 础 学 习 真 的 很 赞 ， 加 油 追 梦 人,B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP,2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1
对 比 了 很 多 课 程 ， 还 是 觉 得 嵩 天 老 师 的 最 全 面 好 理 解 ， 又 生 动 有 趣 。 去 年 看 过 嵩 老 师 以 前 录 制 的 视 频 ， 现 在 这 个 貌 似 更 新 ， 人 也 变 瘦 了 ， 从 小 胖 子 变 知 性 男 青 年 …… 差 点 没 认 出 来 ， 赞 ！,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。 简 单 易 懂 ， 有 很 有 趣 ， 真 的 很 棒 。,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 的 生 动 ， 教 学 练 模 式 很 好 ， 课 程 时 间 不 长 ， 利 于 保 持 学 习 热 情 ， 结 合 1 2 3 平 台 ， 对 于 p y t h o n 的 学 习 理 解 很 棒 ！,B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O,2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 2 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1
老 师 讲 的 课 通 俗 易 懂 ， 自 己 基 础 少 ， 很 多 英 文 ， 引 用 等 等 都 不 了 解 其 含 义 ， 还 需 要 查 阅 很 多 资 料 才 能 懂 ， 没 关 系 ， 每 天 努 力 点 点 ， 每 天 进 步 一 点 点 ， 大 家 一 起 欧 力 给 ！,B-ASP I-ASP O O B-ASP O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,1 1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 基 础 ， 适 合 新 手 学 习 ， 可 以 结 合 教 科 书 进 行 观 看,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1
老 师 把 各 个 知 识 点 讲 解 的 非 常 清 晰 ， 有 编 程 基 础 的 同 学 接 受 很 快 ， 即 使 是 编 程 小 白 ， 稍 加 钻 研 也 能 搞 懂 ， 非 常 值 得 推 荐 的 一 门 课 程 。,B-ASP I-ASP O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 是 目 前 为 止 上 过 的 最 好 的 一 门 编 程 课 。,O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
"讲 解 思 路 清 晰 , 操 作 易 理 解 , 课 后 内 容 丰 富",B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1
开 头 感 觉 不 是 很 适 合 那 种 踏 实 的 学 习 方 法 ， 感 觉 具 有 系 统 的 教 学 ， 但 顺 序 有 点 不 好 ， 对 于 没 有 P Y T H O N 基 础 的 学 生 来 讲 ， 前 面 直 接 上 了 一 些 代 码 ， 表 示 看 不 懂 ， 又 渴 望 知 道 每 一 句 未 曾 见 过 的 语 句 的 含 义 ， 又 无 法 知 道 ， 只 能 安 慰 自 己 后 面 会 讲 的 感 受 很 不 爽 ， 课 程 适 合 对 P Y T H O N 进 行 了 解 或 者 培 养 兴 趣 ， 但 不 适 合 想 要 全 面 扎 实 基 础 ， 深 入 学 习 P Y T H O N 的 好 奇 心 强 的 人 。,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O,0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 太 好 了 呜 呜 呜 呜 大 后 天 就 考 二 级 了 希 望 六 天 速 成 老 师 保 佑 我 过 嘤 嘤 嘤,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 通 俗 易 懂 ， 老 师 讲 授 风 趣 幽 默 ， 学 p y t h o n 的 兴 趣 一 下 子 就 被 调 动 起 来 了,O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 对 于 我 这 小 白 来 说 ， 需 要 学 习 的 东 西 太 多 了,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"l i f e i s s h o r t , y o u n e e d p y t h o n .",O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1
老 师 讲 解 的 非 常 精 简 到 位 ， 听 一 遍 一 般 理 解 不 到 位 ， 需 要 多 听 几 遍 效 果 会 更 好 ， 稳 固 知 新 、 循 序 渐 进 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
北 京 理 工 大 学 嵩 天 老 师 主 的 《 P y t h o n 语 言 程 序 设 计 》 一 课 非 常 好 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP O O O O,2 2 2 2 2 2 2 2 2 2 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 2 -1 -1 -1 -1
老 师 讲 解 的 比 较 好 理 解 ， 整 体 课 程 是 由 难 到 易 的 感 觉 ， 前 面 还 没 完 全 学 完 基 础 语 法 的 时 候 就 开 始 编 程 ， 初 始 只 能 记 住 ， 后 面 学 到 这 部 分 的 时 候 有 点 我 前 面 好 像 学 过 的 感 觉 ！ ！,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1
课 程 讲 解 很 清 晰 ， 我 会 继 续 努 力 的,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 课 认 真 自 学 ， 听 课 是 一 种 享 受,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP,2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2
很 好 的 课 程 ， 我 是 先 看 书 ， 然 后 结 合 视 频 ， 目 前 学 习 很 顺 利 。,O O O B-ASP I-ASP O O O O O B-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1
我 1 7 年 3 月 份 开 始 自 学 p y t h o n ， 后 面 又 学 习 了 这 门 课 程 ， 难 忘 的 一 段 学 习 之 旅 ！ ！ ！,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
作 为 初 学 者 ， 超 级 满 意 ！ 适 合 各 位 亲 ！,O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1
老 师 的 讲 解 很 清 晰 ， 很 适 合 入 门 级 的 学 生 进 行 自 学,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
讲 的 不 够 深 入 ， 建 议 老 师 可 以 额 外 开 一 门 p y t h o n 面 向 对 象 ， p y t h o n w e b 开 发 的 课 程 ， 讲 下 d j a n g o 或 者 f l a s k,O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 1 1 1 1 1
不 愧 是 国 家 精 品 课 程 ， 与 其 他 P y t h o n 课 程 相 比 ， 讲 解 非 常 详 细 ， 备 课 能 感 觉 出 来 十 分 充 分 ， 非 常 感 谢 老 师 。,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 很 详 细 ， 内 容 也 很 吸 引 人 ， 老 师 讲 的 也 很 细 致,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
P y t h o n 基 础 ， 老 师 讲 解 浅 显 易 懂 ， 条 理 清 晰 ， 学 到 很 多 ， 正 在 强 化 训 练 ， 跟 着 老 师 一 步 一 步 来 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O,1 1 1 1 1 1 1 1 -1 1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
由 浅 入 深 ， 生 动 有 趣 ， 使 我 很 快 对 这 门 语 言 产 生 了 浓 厚 的 兴 趣 ， 进 步 也 很 快 ！ 谢 谢 ！,O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1
非 常 不 错 ， 激 发 了 我 学 编 程 的 兴 趣,O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2
之 前 也 听 了 好 多 节 基 础 课 ， 但 是 都 没 有 这 节 课 全 面 ， 讲 的 也 很 有 趣 。 非 常 灵 活 。 很 生 动 ， 容 易 听 懂,O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O B-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 2 2
非 常 好 的 课 程 ！ 内 容 安 排 很 合 理 ， 讲 解 深 入 浅 出 ， 课 后 也 有 对 应 的 练 习 题 ， 对 电 脑 小 白 非 常 友 好 。,O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
老 师 讲 课 内 容 细 致 可 学 性 强 很 适 合 技 术 小 白,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
课 程 内 容 简 洁 清 晰 明 了 ， 对 于 初 学 者 来 说 有 很 大 帮 助 ， 在 学 习 时 我 发 现 有 时 候 因 为 环 境 搭 建 与 p y t h o n 导 包 使 用 的 问 题 时 会 出 现 各 种 各 样 的 意 外 错 误 ， 学 生 会 在 网 上 找 各 种 方 法 解 决 ， 耽 误 的 时 间 会 有 点 多 ； 整 体 来 说 还 是 讲 的 非 常 浅 显 易 懂 的 ， 感 谢 老 师 和 老 师 团 队 的 辛 勤 付 出 ， 你 们 是 真 的 很 棒 ！ 希 望 后 面 有 更 多 基 于 项 目 的 实 战 开 发 的 讲 解 内 容,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1 -1 2 2 2 2
内 容 讲 解 由 浅 入 深 、 通 俗 易 懂 ， 非 常 棒 的 课 程 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是,O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 已 经 听 了 4 4 课 ， 说 明 该 课 程 真 值 得 我 继 续 学,O O O O O O O B-ASP O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 的 课 讲 得 非 常 好 ， 但 越 好 我 就 越 期 待 面 向 对 象 部 分 的 内 容 ！ 说 了 那 么 久 了 ， 怎 么 p y t h o n 面 向 对 象 课 程 还 没 出 啊 ？,B-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O,1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 0 0 -1 -1 -1 -1 -1
很 实 用 的 网 站 ， 希 望 一 直 秉 承 着 “ 好 的 大 学 是 没 有 围 墙 ” 的 理 念 ， 而 不 是 ， 用 金 钱 拉 开 了 一 条 难 以 逾 越 的 鸿 沟 ， 谢 谢 ！,O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1
从 实 例 出 发 深 入 㳀 出 逐 行 解 读 代 码 感 觉 比 较 容 易 接 受,O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
参 加 了 很 多 P y t h o n 的 网 络 课 程 ， 这 是 最 符 合 我 的 。 课 程 设 计 合 理 ， 由 浅 入 深 ， 知 识 结 构 条 例 清 晰 ， 练 习 实 践 针 对 性 强 且 有 梯 度 和 实 用 性 、 嵩 老 师 亲 切 和 蔼 ， 娓 娓 道 来 ， 专 业 又 不 呆 板 。 不 愧 为 国 家 精 品 ， 期 待 能 推 出 更 多 优 秀 课 程 ， 造 福 全 社 会 。,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
感 觉 不 错 老 师 讲 的 太 详 细 了 ， 我 慢 慢 理 解 了,O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 爱 嵩 天 老 师 ， 引 我 入 门 的 感 觉 。,O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 解 的 非 常 好 。 很 适 合 我 这 种 编 程 小 白 学 习,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1
讲 得 很 具 体 又 很 实 用 ， 条 理 清 晰 ， 难 度 适 当 ， 能 让 我 在 保 持 好 奇 和 热 情 的 状 态 下 愉 快 地 学 习 下 去 ， 很 棒 。,B-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 课 风 趣 ， 简 单 易 懂 ， 对 于 P Y T H O N 初 学 者 来 说 是 一 门 好 课,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O B-ASP,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2
感 觉 很 好 函 数 老 师 讲 的 也 很 细 致 实 例 对 于 开 阔 思 维 也 很 有 用,O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1
很 棒 ！ ！ 老 师 讲 的 很 清 楚 也 很 有 趣 我 很 愿 意 听 ！ ！,O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 详 细 ， 以 前 我 没 听 懂 的 内 容 在 这 里 听 懂 了 很 多 ， 感 谢 老 师 ！,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 得 通 俗 易 懂 ， 例 子 非 常 具 有 代 表 性,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
不 可 多 得 是 好 课 程 ！,O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 2 2 -1
学 到 了 很 多 实 用 的 东 西 ， 思 维 也 开 阔 了 很 多,O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 课 程 授 课 通 俗 易 懂 ， 思 路 清 晰 ！ ！ ！,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,1 1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
好 棒 的 老 师 ， 好 棒 的 课 程 ， 课 程 安 排 顺 序 很 适 合 小 白 逐 渐 接 受 与 理 解 ， 厉 害,O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 2 2 2 2 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 解 很 清 晰 ， 很 舒 服 ， 容 易 理 解 。,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
给 了 我 学 习 p y t h o n 的 很 多 帮 助 感 谢 嵩 天 老 师,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 2 2 2 2
非 常 喜 欢 ， 我 大 学 专 业 是 医 学 类 的 ， 可 是 听 懂 这 门 课 完 全 没 有 问 题 ！ 老 师 讲 得 很 好 ！,O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O B-ASP O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
在 讲 解 ， 课 程 编 排 ， 用 例 ， 教 材 上 的 编 写 是 非 常 非 常 精 彩 的 ， 真 是 精 品 ! ! !,O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O,-1 2 2 -1 2 2 2 2 -1 2 2 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 解 很 细 致 ， 对 程 序 小 白 很 友 好 ~,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
这 个 课 程 讲 解 透 彻 、 内 容 覆 盖 全 面 ， 案 例 精 选 的 得 当 。 感 谢 老 师 们 ！,O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
强 烈 推 荐 这 个 课 程 ， 五 星 好 评 ， 遇 到 这 样 的 课 程 是 幸 运 ！ ！ ！ ！,O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 上 课 的 质 量 没 得 说 ， 很 棒 ！ 基 本 的 语 法 结 构 都 囊 括 其 中 ， 且 还 有 很 多 实 用 的 例 子 ； 同 时 ， 赞 同 老 师 对 编 程 的 看 法 —— 对 大 家 的 鼓 励 ， 比 如 说 到 编 程 什 么 时 候 开 始 都 不 晚 ， 只 要 喜 欢 且 愿 意 坚 持 ， 就 有 学 好 编 程 的 很 大 可 能 性,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP,1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 2
一 串 神 秘 数 字 带 你 学 习 — — 7 9 0 3 1 3 0 0 0,O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1
嵩 老 师 讲 解 细 致 入 微 ， 结 合 p y t h o n 1 2 3 在 线 作 业 ， 简 直 是 学 习 神 器 ！ 绝 对 的 从 零 基 础 也 能 学 会 ！,B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 很 认 真 ， 课 件 也 很 不 错 ！,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
课 程 安 排 紧 凑 ， 关 键 内 容 都 齐 全 ， 非 常 适 合 新 手 入 门 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
很 有 趣 味 性 ， 就 是 难 度 有 点 大 ， 需 要 预 习 自 习 的 东 西 还 是 很 多 的,O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 2 2 2 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
老 师 讲 的 超 级 棒 ， 非 常 基 础 ， 很 适 合 零 基 础 的 学 生 ， 实 例 生 动 有 趣 ， 受 益 良 多,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1
很 喜 欢 嵩 天 的 课 ， 简 洁 易 懂 ， 一 直 能 跟 得 上 。 感 谢 平 台 ！,O O O B-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
很 棒 。 希 望 继 续 出 品 高 质 量 课 程 。,O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
结 构 合 理 ， 案 例 式 教 学 ， 非 常 好 ！,B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1
嵩 老 师 讲 的 特 别 好 ， 生 动 形 象 ， 丰 富 有 趣 ， 适 合 入 门,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
我 觉 得 老 师 讲 授 条 理 清 晰 ， 讲 练 结 合 ， 是 可 以 真 正 学 的 知 识 的 优 质 课 程 。,O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 1 1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
嵩 天 老 师 的 课 非 常 好 ， 在 案 例 中 学 习 p y t h o n ， 不 会 那 么 枯 燥 ， 而 且 每 节 课 后 都 能 解 决 新 的 问 题 。,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP O O O O O O O B-ASP I-ASP O,1 1 1 1 -1 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 的 非 常 好 ， 不 拘 泥 于 一 般 的 教 学 顺 序 ， 通 过 多 个 实 例 逐 步 带 入 知 识 点 。,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 -1
很 系 统 ， 很 实 用 ， 老 师 的 课 程 设 计 、 讲 解 都 非 常 棒 ， 这 个 课 性 价 比 太 高 了 ！,O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
嵩 老 师 讲 得 特 别 好 ， 通 俗 易 懂 ， 对 小 白 入 门 学 习 p y t h o n 特 别 友 好 。,B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1
老 师 讲 的 都 非 常 精 彩 ， 只 可 惜 我 没 能 一 直 坚 持 下 去 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
不 错 ， 条 理 清 晰 ， 安 排 得 当 。 是 一 门 不 错 的 入 门 教 程 。 如 果 加 上 类 就 是 门 完 善 的 入 门 课 程 。,O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
老 师 讲 的 很 细 致 ， 覆 盖 内 容 很 全 面,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 很 细 致 ， 课 程 所 涵 盖 的 知 识 比 较 全 面 ， 能 够 很 好 地 入 门 P y t h o n 。 这 门 课 程 听 起 来 挺 好 理 解 ， P P T 呈 现 的 内 容 简 洁 但 又 包 含 了 需 要 讲 的 知 识 。 让 我 映 像 最 深 的 课 程 彩 蛋 ， 让 我 感 觉 到 老 师 对 这 门 课 程 的 态 度 ， 准 备 的 非 常 充 分 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
好 ， 非 常 好 的 教 程 ， 配 合 书 本 更 赞 ！,O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1
非 常 好 的 教 学 方 式 ， 老 师 们 知 识 渊 博 ， 教 学 方 式 新 颖 ， 内 容 充 实 实 用 ！,O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1
内 容 组 织 由 易 到 难 ， 学 习 方 式 也 是 循 序 渐 进 ， 希 望 自 己 能 够 加 油 吧 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
n i c e ， w e l l d o n e,O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
挺 好 的 ， 就 是 建 议 每 个 小 节 可 以 直 接 一 个 视 频 ， 一 个 知 识 点 一 个 视 频 会 太 碎 ， 刚 好 上 头 又 要 切 视 频 。,O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
讲 的 很 细 致 ， 就 是 视 频 分 的 太 碎 啦 ， 然 后 希 望 讲 的 内 容 能 够 更 深 入 一 些 ， 老 师 人 很 不 错 哦,B-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O,2 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 但 是 不 知 道 为 什 么 课 后 的 代 码 部 分 我 总 是 做 不 对,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1
讲 的 很 好 ， 能 不 能 再 开 个 更 深 入 的 实 用 的 课 程,O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
内 容 合 理 、 进 度 比 较 紧 凑 ， 例 子 很 好 ，,B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1
作 为 一 名 编 程 初 学 者 ， 之 前 找 过 一 些 视 频 网 课 学 习 P Y T H O N ， 无 论 从 知 识 网 点 讲 授 、 知 识 体 系 还 是 课 堂 学 习 、 练 习 布 置 ， 比 起 这 门 课 都 相 去 甚 远 ， 不 愧 是 国 家 精 品 课 程 ， 感 谢 老 师 ， 感 谢 平 台 ！ 谢 谢 ！,O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 的 非 常 好 简 单 易 懂 让 p y t h o n 的 学 习 变 得 很 是 轻 松,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1
课 程 脉 络 清 晰 ， 老 师 授 课 简 单 易 学 ， 适 合 新 手 入 门,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
很 棒 的 课 程 ， 嵩 老 师 讲 的 非 常 好 ！,O O O B-ASP I-ASP O B-ASP I-ASP I-ASP B-ASP O O O O O,-1 -1 -1 2 2 -1 1 1 1 2 -1 -1 -1 -1 -1
非 常 基 础 ， 老 师 讲 的 也 通 俗 易 懂 ， 很 适 合 入 门 学 习,O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
实 在 是 太 棒 了 我 根 本 无 法 形 容 我 的 心 情 。 嵩 老 师 的 课 简 单 易 懂 ， 全 面 有 深 度 ， 着 重 培 养 编 程 思 维 ， 我 已 经 成 为 嵩 老 师 的 粉 丝 了 。,O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 -1 2 2 -1 -1
节 奏 合 适 ， 难 度 适 中 ， 适 合 入 门 学 习,B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2
看 过 这 么 多 慕 课 ， 再 也 找 不 到 一 门 这 么 简 洁 ， 清 晰 ， 有 字 幕 的 课 。 授 课 方 式 极 佳 ， 老 师 形 象 也 好 。 真 心 舒 服 ， 爱 了,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 好 的 课 程 ， 配 合 自 己 的 巩 固 和 练 习 可 以 学 到 不 少 东 西 ， 是 P Y E 小 白 的 优 选 课 程 。,O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 2 -1
很 好 ， 希 望 跟 着 老 师 学 ， 可 以 通 多 计 算 机 二 级,O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1
内 容 丰 富 有 趣 ， 老 师 授 课 深 入 浅 出 ， 易 于 理 解 ， 且 有 启 发 性,B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2
教 学 目 标 明 确 ， 讲 解 思 路 清 晰 ， 举 例 恰 当 ， 是 不 可 多 得 的 课 程 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
"有 些 错 误 ， 如 列 表 里 面 第 1 个 元 素 i n d e x 为 0 , 不 应 为 1 ， 表 述 有 些 问 题 ， 但 总 体 很 好 ， 特 别 是 编 程 思 维 的 论 述 ， 很 好 很 强 大 ， 轻 松 能 听 懂 。 学 语 法 容 易 ， 但 编 程 思 维 拓 展 很 少 有 老 师 教 。 总 之 ， 简 单 易 学 ， 学 有 所 获 ， 值 得 推 荐 入 门 。",O O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 0 0 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 0 0 0 0 0 0 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
课 程 内 容 组 织 有 条 理 ， 授 课 方 式 新 颖 ， 内 容 丰 富 ， 举 例 详 细,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O,2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1
非 常 好 ， 有 实 例 。 有 操 作 。 实 例 加 深 学 习 。,O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 赞 。 通 过 学 习 ， 我 掌 握 了 P Y T H O N 这 个 语 言 基 础 的 知 识 ， 对 后 续 的 学 习 有 很 大 帮 助,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
嵩 天 老 师 讲 课 娓 娓 道 来 ， 听 课 效 果 很 好 ~,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP B-ASP I-ASP O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 1 2 2 -1 -1 -1
蛮 不 错 的 ， 跟 着 一 点 一 点 的 学 起 来 ， 加 油 ！ ヾ ( ◍ ° ∇ ° ◍ ) ﾉ ﾞ,O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
i t i i s v e r y e x c e l l e n t ! v e r y g o o g !,O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
清 晰 明 朗 ！ 通 俗 易 懂 。 爱 了 爱 了 。 。,O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
只 有 好 老 师 ， 没 有 好 课 程 ！ 感 谢 嵩 老 师 团 队 带 来 的 这 门 国 家 精 品 课 程 ， 让 我 对 P y t h o n 有 了 更 大 的 认 识 。 人 生 苦 短 ， 我 用 P y t h o n ！,O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 2 2 -1 -1 -1 -1 0 0 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1
教 学 非 常 好 ， 分 析 到 位 ， 备 课 认 真 。 但 视 频 过 于 碎 片 化 。,B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
非 常 n i c e 的 课 程 ， 希 望 继 续 学 习,O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O,-1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 的 教 学 逻 辑 清 晰 ， 教 学 方 案 规 划 科 学 合 理 ， 一 定 要 跟 上 步 伐 好 好 学 习 ！,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O,1 1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 解 的 非 常 清 晰 ， 代 码 简 洁 而 且 每 一 步 都 会 解 释 其 作 用,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 1 1
课 程 已 经 结 束 了 才 开 始 学 ， 错 过 很 多 东 西 ， 但 是 还 是 坚 持 学 完 ， 嵩 老 师 讲 得 非 常 好 ， 非 常 感 谢 无 私 奉 献,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
p y t h o n 不 可 阻 挡 ， 未 来 可 期,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1
讲 课 浅 显 易 懂 ， 但 有 些 不 够 深 入 。,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
谢 谢 嵩 老 师 的 倾 情 讲 解 ， 体 会 到 了 编 程 语 言 的 趣 味 和 广 大 的 用 途 ！,O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 2 2 -1
课 程 内 容 设 计 难 度 不 大 ， 比 较 适 合 初 学 者 了 解 整 个 P Y T H O N 体 系,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 1 1
在 学 了 C 语 言 的 情 况 下 ， 再 学 p y t h o n 时 ， 会 感 觉 很 简 单 ， 且 受 益 匪 浅,O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
授 课 方 式 比 较 新 颖 ， 例 子 也 紧 跟 潮 流 ， 还 给 了 很 多 宏 观 上 的 概 念 和 方 向 的 辨 析 ， 总 体 来 说 质 量 超 出 预 期 ， 付 费 买 下 证 书 以 表 支 持 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
老 师 的 讲 课 无 论 是 内 容 充 实 性 还 是 组 织 合 理 性 上 都 无 可 挑 剔 只 是 发 际 线 可 以 放 低 一 点,B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O,1 1 -1 2 2 -1 -1 -1 2 2 2 2 2 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1
课 程 内 容 精 心 设 计 ， 循 序 渐 进 ， 能 够 感 受 到 嵩 天 老 师 团 队 的 用 心 ， 让 一 个 完 全 小 白 都 入 门 了,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
课 的 容 量 感 到 很 大 ， 虽 然 课 都 很 短 ， 消 化 起 来 不 容 易,B-ASP O B-ASP I-ASP O O O O O O O B-ASP O O O O O O O O O O O,1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
继 续 加 油 ， 别 辜 负 自 己 ， 超 越 比 你 优 秀 的 人 。,O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 很 专 业 ， 课 堂 十 分 生 动 形 象 ， 相 比 我 们 这 末 流 9 8 5 部 分 水 课 的 老 师 强 了 太 多,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 0 0 -1 1 1 -1 -1 -1 -1
特 别 好 ！ 非 常 棒 ！ 老 师 总 能 把 复 杂 的 概 念 讲 得 简 单 易 懂 ， 真 是 太 厉 害 了,O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 安 排 合 理 ， 老 师 讲 解 细 致 到 位,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1
讲 解 的 非 常 细 致 ， 可 以 很 好 的 理 解 所 讲 授 的 内 容 ， 很 全 面 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
它 让 我 快 速 的 感 受 到 编 程 带 给 人 的 那 种 成 就 感,O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 讲 的 可 以 ， 当 时 和 南 京 大 学 做 对 比 ， 还 是 这 个 讲 得 更 好 一 些,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ， 通 俗 易 懂 ， 还 有 非 常 丰 富 的 实 验,B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
讲 课 练 习 结 合 ， v e r y g o o d ！,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
震 惊 ， 花 了 3 w 买 的 全 套 爬 虫 资 料 现 在 免 非 赠 送 ！ ！ ！ ！ 觉 得 课 程 对 你 有 帮 助 的 话 ， 动 动 你 的 小 手 点 赞 一 下 哦 ， 需 要 p y t h o n 学 习 资 料 课 十 \ . / : x Q h 5 5 3 领 取 海 量 的 学 习 软 件 、 资 料 专 业 版 的 激 活 码 源 码,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 1 -1 1 1 1 1 1
非 常 好 的 一 门 P y t h o n 课 程 ， 适 合 零 基 础 的 我 ， 通 过 课 上 跟 着 老 师 学 ， 课 下 自 己 练 习 和 查 阅 相 关 知 识 ， 现 在 能 够 理 解 别 人 写 的 代 码 了 ， 自 己 写 还 有 些 困 难 ， 只 能 写 一 些 简 单 的 代 码 。 继 续 努 力 ！ 加 油 ！ ！ ！,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
希 望 能 在 这 个 课 程 基 础 上 有 进 阶 课 程,O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2
老 师 讲 得 太 好 啦 ， 耐 心 细 致 地 讲 解 ， 真 是 不 错 的 课 程 ！ 老 师 讲 得 太 好 啦 ， 耐 心 细 致 地 讲 解 ， 真 是 不 错 的 课 程 ！,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1
收 获 很 大 ， P y t h o n 语 言 便 捷 又 奇 妙 ！,B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
如 果 有 电 脑 版 软 件 就 好 了 主 要 是 方 便 快 捷,O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 ， 结 合 实 例 ， 将 P y t h o n 基 础 语 言 讲 解 得 非 常 易 懂,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 很 清 楚 ， 课 堂 也 生 动 有 趣 ， 简 洁 易 懂 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
符 合 学 习 的 心 理 学 ， 认 知 规 律 ， 照 顾 到 了 视 觉 效 果 ， 分 段 切 片 细 致 ， 到 位 ；,O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
非 常 有 用 的 课 程 ， 简 单 易 懂 ， 可 以 很 快 理 解 老 师 讲 解 的 内 容,O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 2 2
感 觉 老 师 讲 的 很 好 没 有 很 多 的 赘 述,O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0
学 到 了 p y t h o n 基 础 挺 不 错,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O,-1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1
讲 的 很 清 楚 ， 而 且 还 有 提 供 了 课 件 ， 真 的 很 棒 ！,B-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
本 课 程 授 课 方 式 很 有 趣 ， 对 掌 握 P y t h o n 的 基 础 知 识 很 有 帮 助 ! 感 谢 老 师 们 的 辛 苦 付 出 ～,O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O,-1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1
很 棒 非 常 适 合 新 手 学 习 。 我 之 前 学 的 C # ， 所 以 学 的 比 较 快 ， 但 p y t h o n 之 所 以 强 大 是 因 为 库 的 存 在 ， 将 来 需 要 学 习 的 地 方 还 很 多,O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 课 方 式 新 颖 ， 有 趣 ， 对 新 手 学 习 很 有 帮 助,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 课 理 论 应 用 案 例 ， 对 自 己 的 学 习 有 很 大 提 高 ， 谢 谢 老 师 ！,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,1 1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
N I U B I 嵩 天 老 师 讲 课 太 好 了,O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1
老 师 讲 课 思 路 清 晰 ， 非 常 易 懂 ， 厉 害 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 讲 解 透 彻 、 内 容 覆 盖 全 面 ， 案 例 精 选 的 得 当 。 感 谢 老 师 们 ！,O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
讲 解 是 清 晰 有 条 理 的 ， 从 一 个 小 白 入 门 困 难 了 一 些 。 有 些 习 题 难 度 过 高 了 ， 在 规 定 时 间 没 做 出 来 ， 或 者 大 体 对 了 ， 但 是 没 有 完 全 写 对 ， 完 全 不 得 分 就 很 挫 败,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 只 是 我 们 自 学 的 课 上 都 看 得 懂 ， 到 p y t h o n 1 2 3 上 做 题 就 不 会 了,B-ASP I-ASP O O O O O O O O O O O O B-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
言 简 意 赅 ， 流 程 图 式 的 教 学 ， 深 入 简 出 ， 学 习 效 率 非 常 高 ！,O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
小 白 的 最 好 选 择 ， 基 础 性 很 强 ， 逐 步 引 入 。,O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
对 p y t h o n 产 生 了 浓 厚 的 兴 趣,O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP,-1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2
不 错 的 ， 老 师 提 供 的 资 料 很 完 美 。 老 师 真 的 好 ， 期 待 老 师 的 更 多 课 程 ，,O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1
老 师 讲 得 非 常 棒 ， 深 入 浅 出 ， 通 俗 易 懂 ， 教 学 水 平 非 常 高 ， 也 希 望 自 己 能 够 学 好 ， 用 好 p y t h o n,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
非 常 简 单 易 懂 ， 很 好 的 学 习 体 验,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
老 师 讲 课 水 平 很 高 ， 有 编 程 基 础 的 话 基 本 上 很 快 能 够 上 手 p y t h o n,B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,1 1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
深 入 简 出 的 讲 解 ， 让 我 们 能 够 快 速 对 这 门 语 言 有 个 基 础 的 认 知 。 非 常 感 谢 ！,O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
感 觉 非 常 的 有 趣 ， 同 时 想 要 学 好 还 是 要 多 加 思 考,O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
慕 课 和 p y t h o n 1 2 3 太 好 了,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,2 2 -1 2 2 2 2 2 2 2 2 2 -1 -1 -1
老 师 讲 的 通 俗 易 懂 ， 十 分 好 理 解 ， 希 望 能 学 好 找 到 一 份 实 习 工 作,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1
这 个 课 程 简 单 有 趣 ， 配 套 的 练 习 也 很 完 备 ， 非 常 适 合 初 学 者 入 门,O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1
课 讲 的 倒 是 不 错 ， 但 是 夹 带 私 货 太 恶 心 了,B-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1
挺 基 础 的 ， 也 比 较 系 统 、 有 逻 辑 。,O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O,-1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1
呃 呃 呃 ， 还 行 ， 我 学 的 太 慢 ， 很 多 都 记 不 住 ， 实 际 没 感 到 有 多 大 提 升 。,O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
这 个 课 程 总 体 来 讲 不 错 ， 不 过 个 人 觉 得 可 以 进 度 再 快 一 点 。,O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
只 能 说 将 讲 得 不 要 太 好 ！ ！ ！ ！ ！ ！,O O O O B-ASP O O O O O O O O O O O,-1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 深 入 浅 出 ， 非 常 专 业 ， 这 是 一 门 很 适 合 编 程 小 白 看 的 课 ~,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 2 -1
很 好 。 学 到 东 西 了 。 已 经 推 荐 给 朋 友 。,O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
真 的 喜 欢 这 课 程 ， 老 师 讲 的 好 ， 完 全 听 得 懂 。 课 件 也 是 很 用 心 ， 深 入 浅 出 ， 循 序 渐 进 。 从 老 师 每 节 课 课 前 回 顾 上 节 课 知 识 ， 听 写 复 习 代 码 ， 然 后 不 断 有 规 律 的 重 复 所 学 ， 就 可 以 看 出 来 老 师 的 “ 心 机 ” 赞,O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1
"嵩 老 师 讲 的 太 好 了 , 只 不 过 对 于 完 全 不 懂 编 程 的 小 白 来 说 , 还 是 有 很 多 地 方 不 能 理 解 , 主 要 自 己 要 下 苦 功 夫 才 行",B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 设 计 非 常 合 理 ， 嵩 天 教 学 详 细 ， 例 程 具 体 ， 对 我 的 学 习 起 到 了 很 大 的 帮 助 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
讲 解 清 楚 ， 例 子 有 趣 ， 课 程 设 计 很 好 ， 收 获 很 大 。,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1
老 师 的 讲 解 很 细 致 ， 适 合 新 手 入 门 。,B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP B-ASP I-ASP O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 2 2 -1
课 程 很 棒 ， 通 过 知 识 点 例 子 课 后 练 习 的 方 式 ， 能 更 牢 固 的 掌 握,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
找 到 了 大 学 学 习 的 感 觉 ， 太 棒 了 ！ 不 断 充 实 自 己 ， 万 方 感 谢 M O O C ， 感 谢 老 师 ！ ！,O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1
跟 着 老 师 讲 ， 学 习 很 有 节 奏 ， n i c e,O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O O O O,-1 -1 1 1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1
还 行 ， 就 是 视 频 有 点 短 ， 许 多 术 语 太 官 方 ， 不 怎 么 听 得 懂 。,O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 有 收 获 ， 感 谢 老 师 和 为 这 门 课 程 付 出 的 人 ， 感 谢 自 己 坚 持 下 来 有 所 收 获,O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP O O O O O O O O O O O B-ASP I-ASP,-1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
前 半 部 分 挺 好 ， 但 后 半 部 分 的 课 程 稍 脱 离 应 用 ，,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 1 1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1
良 心 的 免 费 课 程 ， 终 于 不 是 从 入 门 到 放 弃 了,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 棒 ！ 授 课 方 式 让 人 耳 目 一 新 ！,O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 解 深 入 浅 出 ， 选 取 的 实 例 贴 合 实 际 ， 生 动 精 炼 ， 给 人 很 好 的 学 习 体 验 。 唯 一 遗 憾 的 是 半 途 加 入 ， 没 有 在 截 止 之 前 学 完 课 程 ， 期 待 下 一 期 课 程 ！,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O,1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 的 很 清 楚 ， 不 仅 仅 是 把 编 程 方 法 一 步 一 步 的 展 示 出 来 ， 还 有 更 重 要 的 编 程 思 维 。 1 2 3 平 台 也 不 错 ， 就 是 内 容 有 点 少,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
老 师 的 讲 解 和 教 学 速 度 都 挺 好 ， 就 是 作 业 有 点 难,B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O,1 1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
嵩 老 师 讲 的 深 入 浅 出 ， 有 趣 易 懂 ， 嵩 老 师 是 讲 P Y T H O N 课 难 得 的 好 老 师 。,B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 2 2 -1
老 师 讲 课 很 清 楚 能 听 明 白 是 我 太 菜 没 有 很 快 理 解 但 是 b a c k 几 次 就 能 明 白,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
第 一 次 学 到 这 么 系 统 的 p y t h o n 语 言 教 学 ！ 非 常 棒,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1
适 合 初 学 者 ， 谢 谢 嵩 天 老 师 ， 你 们 辛 苦 了,O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
从 课 程 内 容 来 讲 ， 我 觉 得 嵩 天 老 师 讲 的 真 的 很 好 ， 我 因 为 学 过 j a v a 所 以 有 一 些 基 础 ， 配 合 老 师 上 课 讲 到 的 代 码 实 例 ， 可 以 很 快 掌 握 ， 很 感 谢 这 门 课 程 ， 让 我 学 习 了 p y t h o n 基 础 语 法 体 系 。,O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1
课 程 难 度 梯 度 设 置 的 有 问 题 不 是 从 易 到 难 不 符 合 学 生 认 知 规 律 特 别 是 对 小 白 来 说 有 难 度,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP,0 0 0 0 0 0 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0
非 常 好 ， 教 授 全 面 ， 结 合 实 例 ， 深 入 浅 出,O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
课 程 由 浅 到 深 ， 讲 的 非 常 清 晰 ， 收 获 很 大 ， 谢 谢 老 师,B-ASP I-ASP O O O O O B-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2
老 师 讲 解 详 细 ， 很 透 彻 ， 好 理 解 。,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
实 用 ， 讲 解 清 晰 ， 超 赞 ， 已 经 推 荐 给 很 多 朋 友 学 习,O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 清 楚 ， 很 容 易 理 解 。 实 例 都 很 有 趣 。,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 解 透 彻 ， 加 上 学 过 两 门 编 程 语 言 ， 学 起 来 轻 松,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
讯 息 渐 进 ， 知 识 点 理 解 起 来 很 融 洽 ， 不 过 还 是 需 要 配 合 课 后 练 习 或 者 别 的 书 本 巩 固 加 深 。,B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
课 程 内 容 安 排 对 新 手 非 常 友 好 ！ 课 程 团 队 非 常 专 业 ， 授 课 教 师 教 授 引 人 入 胜 。,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 组 织 有 序 ， 上 课 层 层 递 进 ， 由 浅 入 深 解 析 ， 让 我 觉 得 p y t h o n 很 友 好 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
简 单 易 懂 ， 没 有 编 程 基 础 也 能 轻 松 学 p y t h o n,O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
这 课 程 深 入 浅 出 ， 只 要 用 心 听 ， 跟 着 老 师 动 手 写 。 感 觉 这 课 程 学 起 来 不 难 ！,O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O,-1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
感 觉 工 科 类 还 是 要 时 间 消 化 语 言 更 多 的 是 练 习 老 师 讲 的 很 好 但 是 作 为 一 个 文 科 生 理 解 起 来 很 困 难,O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O,-1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1
课 题 安 排 合 理 ， 老 师 幽 默 睿 智 ， 易 于 接 受,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 棒 ， 案 例 很 有 趣 ， 讲 解 很 清 晰 ， 进 度 也 很 适 中 ， 收 获 匪 浅 ， 谢 谢 所 有 的 课 题 团 队,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
播 放 怎 么 偶 尔 会 自 动 暂 停 啊 要 重 新 暂 停 播 放 才 行,O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 0 0 -1 -1 -1 -1
课 程 比 较 体 系 化 ， 容 易 理 解 ， 课 程 进 度 安 排 也 比 较 合 理,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1
讲 得 非 常 生 动 有 趣 ， 富 有 逻 辑 性 ， 是 我 比 较 喜 欢 的 授 课 模 式,O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
老 师 讲 的 非 常 好 ， 绝 对 是 我 听 过 最 好 的 P y t h o n 课 程 ， 学 P y t h o n 跟 老 师 就 对 了 ； 想 学 c 语 言 的 选 翁 凯 老 师 ， 这 两 位 老 师 一 位 讲 P y t h o n 一 位 讲 c 语 言 ， 都 讲 的 非 常 好 ！,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。 通 俗 易 懂 ， 希 望 我 能 坚 持 到 底 ， 为 自 己 加 油 ！ ！ ！,O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 是 真 的 好 ！ ！ ！ 强 烈 推 荐 ， 编 程 小 白 泪 目 ！ ！ ！,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1
老 师 讲 课 非 常 详 细 ， 易 懂 ， 从 这 门 课 中 我 学 到 了 很 多 知 识 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 是 我 听 的 嵩 老 师 的 第 三 门 p y t h o n 课 了 ， 查 漏 补 缺 ， 巩 固 基 础 ， 棒 棒 哒,O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
课 程 很 好 ， 很 不 错 ， 学 起 来 很 有 趣 。,B-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
认 真 学 习 了 这 么 久 ， 终 于 算 是 入 门 了,O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 的 很 详 细 ， 学 起 来 比 较 轻 松 ， 脉 络 清 晰,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
讲 解 清 晰 、 透 彻 ， 难 易 适 度 ， 收 获 很 多 。,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 很 简 洁 生 动 ， 又 突 出 强 调 了 重 点 难 点 ， 课 上 还 有 丰 富 的 实 践 操 作,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
老 师 的 课 信 息 量 很 大 ， 学 习 必 须 要 天 天 认 真 做 作 业 。,B-ASP I-ASP O B-ASP B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O,1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
课 程 组 织 逻 辑 清 晰 ， 教 师 知 识 丰 富 ， 枯 燥 的 程 序 设 计 课 被 讲 解 的 津 津 有 味 ， 推 荐 给 想 要 入 门 学 习 的 同 学 们,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 很 棒 ， 要 是 再 多 一 些 实 例 或 者 试 题 讲 解 就 更 好 了,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ， 易 学 ， 适 应 不 同 层 次 的 基 础 学 习 。,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1 -1
非 学 棒 的 课 ， 感 谢 老 师 ， 感 谢 M O O C,O O O O B-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2
课 程 通 过 实 例 激 发 学 习 者 的 兴 趣 ， 深 入 浅 出 引 出 各 种 概 念 、 语 法 、 关 键 字 、 函 数 ， 非 常 适 合 我 这 样 的 编 程 小 白,B-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 很 好 ， 例 子 中 贯 彻 语 法 ， 学 起 来 更 容 易 ， 有 实 例 佐 证 ， 效 果 更 好,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1
超 级 棒 的 老 师 教 授 超 级 计 算 机 语 言 ， 期 待 收 获 超 预 期 ！,B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O,2 2 -1 -1 2 2 -1 -1 2 2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
讲 的 很 细 腻 ， 每 节 视 频 很 短 不 会 有 听 不 下 去 的 感 觉,O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
编 程 对 我 来 说 不 再 神 秘 和 高 大 上 ， 自 己 也 可 以 学 习 一 点 编 程 。 随 着 学 习 的 深 入 ， 计 算 思 维 也 建 立 起 来 。 压 制 兽 性 ， 每 天 收 获 一 点 点 ~,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 的 课 很 有 趣 ， 我 终 于 没 有 那 个 畏 难 心 理 了,B-ASP I-ASP O B-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1
老 师 讲 得 通 俗 易 懂 ， 让 我 对 于 新 的 内 容 消 化 很 快,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
刚 入 门 可 以 接 受 此 难 度 ， 讲 的 很 好 ， 受 益 匪 浅 。,O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 比 较 清 楚 ， 上 课 比 较 轻 松 ， 不 会 有 太 大 的 压 力 。,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
确 实 很 有 用 ， 跟 着 这 门 课 程 ， 让 我 很 清 楚 地 看 到 自 己 的 进 步 。,O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 么 好 的 课 ， 有 点 相 识 恨 晚 。 我 已 经 好 久 没 有 编 程 了 ， 总 想 找 一 个 好 的 语 言 ， P Y T H O N 就 是 我 要 的 。 感 谢 老 师 们 ！ 感 谢 学 校 ！ 感 谢 M O O C ！ 感 谢 互 联 网 ！ 感 谢 自 己 还 有 这 么 坚 持 ！,O O O O B-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
对 p y t h o n 编 程 有 了 一 个 初 步 的 认 识,O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O,-1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 谢 嵩 天 老 师 ， 让 我 对 学 p y t h o n 充 满 信 心 ， 并 且 保 持 热 爱,O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2
讲 得 非 常 有 条 理 、 清 晰 ， 即 使 是 初 学 者 也 能 很 好 学 习,B-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1
学 了 之 后 会 了 一 些 P y t h o n 基 础,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1
"讲 的 非 常 细 致 , 基 本 上 所 有 的 小 细 节 都 详 细 的 讲 解 了 各 自 的 用 途 和 使 用 方 法",B-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1
课 程 制 作 精 良 ， 老 师 讲 解 得 循 序 渐 进 ， 搭 配 操 作 实 例 ， 作 为 入 门 课 程 真 的 很 棒 了,B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
课 程 质 量 极 高 ， 教 学 内 容 清 晰 很 利 于 学 习,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
这 种 教 学 形 式 挺 好 的 ， 上 课 的 时 候 就 有 老 师 带 着 练 习 。 课 程 还 是 国 家 级 的 大 学 老 师 授 课 ， 看 得 出 课 程 内 容 是 根 据 慕 课 的 特 点 专 门 开 发 的 。 请 继 续 把 慕 课 做 好 ！ 增 加 更 多 的 内 容 ！ 让 没 有 机 会 、 条 件 在 正 规 大 学 上 学 的 孩 子 们 也 能 得 到 良 好 的 教 育 资 源 ！,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 2 2 -1 -1 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
目 前 学 了 第 一 周 ， 内 容 很 基 础 ， 易 于 上 手 ， 非 常 n i c e ！,O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 -1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 组 织 的 非 常 好 ， 循 序 渐 进 ， 从 易 到 难 ， 比 较 容 易 理 解 ； 嵩 天 老 师 讲 的 非 常 好 ， 大 部 分 都 比 较 好 理 解 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 这 门 课 简 直 就 是 我 上 过 的 所 有 编 程 课 中 ， 最 优 秀 的 ， 甚 至 可 以 说 我 所 看 过 的 所 有 m o o c 中 最 优 秀 的 ， 没 有 之 一 ～,B-ASP I-ASP I-ASP O O B-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,1 1 1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ！ 作 为 入 门 课 程 强 烈 推 荐 ， 能 把 概 念 和 全 局 上 的 点 打 通 ！,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 -1 2 -1 -1 -1
嵩 老 师 讲 的 很 棒 ， 还 想 去 看 看 老 师 其 他 的 课 程 。,B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1
非 常 系 统 ， 能 从 初 学 者 的 角 度 帮 助 提 升,O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O,-1 -1 2 2 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 得 非 常 好 ， 课 程 设 计 得 合 理 ， 从 中 收 获 很 多 !,B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O,2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 生 动 有 趣 ， 激 发 了 我 学 习 P Y T H O N 的 兴 趣 ， 内 容 紧 凑 而 丰 富 ， 深 入 学 习 需 要 自 己 再 认 真 看 书 。,B-ASP I-ASP B-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1
开 始 2 周 了 ， 到 现 在 做 作 业 的 网 站 上 都 没 作 业 ， 评 论 区 很 多 人 反 馈 也 没 有 回 复 ， 一 是 没 准 备 好 ， 二 是 不 乐 意 沟 通 反 馈 或 者 是 连 评 论 区 看 都 不 都 看 ？ 可 笑 的 是 ， 还 被 评 选 为 国 家 精 品 ， 我 不 知 道 是 怎 么 被 评 选 上 的,O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,-1 -1 1 1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 0 0 -1 0 0 0 -1 -1 -1 0 0 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
挺 好 的 ， 这 个 老 师 讲 的 很 清 楚 。 感 谢 老 师 的 付 出 ， 谢 谢 ！,O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1
深 入 浅 出 ， 让 我 程 序 小 白 也 能 较 快 地 掌 握 p y t h o n 的 编 程 技 巧 。,O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1
突 然 从 国 家 智 慧 教 育 平 台 上 看 到 嵩 天 老 师 的 这 门 课 程 ， 试 着 听 了 一 个 星 期 ， 发 现 比 学 校 老 师 讲 得 好 太 多 了 。 前 天 刚 刚 找 到 p y t h o n 1 2 3 上 的 P y t h o n 语 言 程 序 设 计 ( 第 1 9 期 ) 入 口 ， 将 尽 快 进 行 练 习 测 试 ， 争 取 一 个 月 学 完 这 门 课 程 。,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
有 没 有 工 作 中 用 到 的 第 三 方 库 讲 解,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
这 很 好 的 一 门 课 ， 深 入 浅 出 。 特 别 是 对 案 例 的 逐 步 剖 析 能 够 让 我 一 步 步 地 学 习 到 如 何 使 用 P y t h o n 语 言 来 分 析 并 解 决 问 题 。 希 望 这 门 课 能 越 办 越 好 ！,O O O O O O B-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP O O O O O O,-1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1
课 程 循 序 渐 进 ， 对 我 这 样 一 个 真 真 正 正 零 基 础 的 傻 × 来 说 ， 也 能 看 得 懂 ， 并 且 一 开 始 就 能 自 己 琢 磨 着 写 出 十 几 行 的 代 码 ， 感 觉 很 有 趣 。 体 会 到 不 同 于 数 学 和 物 理 思 维 的 编 程 思 维 ， 虽 然 跟 自 己 专 业 一 点 也 不 相 关 ， 但 是 总 感 觉 学 无 止 境 ， 自 己 想 学 的 就 应 该 拿 起 来 学 ， 先 不 管 到 底 这 个 编 程 以 后 能 给 我 带 来 什 么 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 的 太 有 趣 ， 这 门 课 必 须 学 会,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1
不 错 ， 结 合 翁 恺 的 c 语 言 入 门 效 果 更 好,O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O,-1 -1 -1 -1 -1 1 1 -1 2 2 2 2 2 2 2 -1 -1
课 程 思 路 清 晰 ， 方 向 明 确 ， 是 一 个 值 得 上 的 好 课,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O B-ASP,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2
1 . 真 的 很 棒 ！ ！ 超 出 预 期 。 2 . g o o d,O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2
e m m m . . . . 为 了 考 二 级 来 自 学 的 ， 希 望 能 过 e w w w w w w ， 老 师 讲 得 很 好 ， 很 清 晰 ， 课 程 压 力 也 不 大 ， 点 赞 ！,O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
老 师 的 讲 解 内 容 很 适 合 我 们 初 学 者 ~ 真 的 是 很 有 进 步 ~ 跟 着 老 师 学 很 有 收 获 ！ ！ 赞 ！ ！,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O,1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 课 思 路 清 晰 ， 讲 课 内 容 由 易 到 难 ， 循 序 渐 进 ， 这 种 上 课 模 式 还 是 非 常 不 错 ！,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,1 1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
我 很 喜 欢 这 门 课 ， 是 能 够 真 正 让 人 学 到 东 西 的,O O O O O O B-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 会 了 很 多 东 西 ， 慢 慢 来 比 较 快 ！ ！ ！,O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
P y t h o n 程 序 设 计 这 门 课 特 别 好 ， 课 程 由 易 到 难 ， 由 浅 入 深 ， 逐 步 拓 宽 知 识 面 ， 增 长 了 我 编 程 的 兴 趣 和 能 力 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O B-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O,2 2 2 2 2 2 2 2 2 2 -1 -1 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1
非 常 好 的 课 ， 带 我 认 识 了 p y t h o n 这 样 一 种 编 程 语 言 ， 但 是 讲 道 理 这 课 节 数 分 的 太 多 了 ， 咱 能 不 能 一 单 元 就 分 一 两 个 视 频 讲,O O O O B-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
让 我 学 到 了 很 多 关 于 p y t h o 的 知 识,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 2 2
p y t h o n 基 础 知 识 体 系 讲 解 很 详 细 到 位 ， 配 和 上 各 种 编 程 实 例 ， 对 于 初 学 者 来 说 很 好 懂 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O,1 1 1 1 1 1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1
由 浅 入 深 ， 通 俗 易 懂 ， 多 一 些 课 下 互 动 就 更 好 了,O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
老 师 讲 讲 的 知 识 点 很 多 ， 但 思 路 清 晰 ， 作 为 一 名 小 白 收 获 颇 丰 。,B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O,1 1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 深 入 浅 出 ， 适 合 没 有 基 础 的 同 学 作 为 入 门 课 程 。,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 2 2 2 2 -1
还 行 ， 希 望 能 加 上 面 向 对 象 的 内 容,O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1
非 常 好 的 p y t h o n 课 ， 老 师 讲 解 很 仔 细,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 -1 2 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1
很 好 的 入 门 课 程 ， 条 理 清 晰 ， 目 标 明 确 ， 很 有 收 获 ~,O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
好 ， 学 到 了 很 多 ， 老 师 辛 苦 ， 我 也 辛 苦 了 。,O O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 有 个 手 机 煎 职 软 件 还 可 以 ， 今 天 玩 了 一 个 多 小 时 三 十 多 块 ， 还 可 以 吧 ( ｀ ・ ω ・ ´ ) 需 要 的 同 学 看 加 扣 扣 ： 2 7 3 8 再 加 2 1 再 加 3 3 0 7 直 接 发 给 你 ~,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1 -1 -1 1 1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
课 程 设 置 系 统 合 理 ， 讲 解 也 很 详 细,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O,2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
我 好 喜 欢 嵩 天 老 师 啊 ， 北 理 的 老 师 在 教 学 上 真 的 顶 。 高 琪 老 师 、 房 永 飞 老 师 我 都 好 喜 欢 。 我 对 编 程 的 恐 惧 大 大 降 低 了,O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 -1 2 2 2 2 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1
0 基 础 有 点 难 ， 我 有 点 编 程 基 础 都 有 点 跟 不 上 了,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
课 后 还 要 看 书 么 ？ 看 完 第 一 周 的 视 频 后 去 做 第 一 周 的 题 目 ， 感 觉 超 纲 了 啊 ~ ~ 好 多 内 容 课 上 都 没 提 到,B-ASP O O O O B-ASP O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP B-ASP O O O O O,0 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1
将 基 本 的 语 法 融 入 实 例 ， 通 过 实 例 让 学 生 更 容 易 接 受 ， 趣 味 性 强 ！,O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O,-1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1
嵩 老 师 的 讲 得 太 好 了 ， 我 这 零 基 础 的 小 白 也 完 全 能 听 懂 。 感 谢 老 师 。,B-ASP I-ASP I-ASP O B-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
讲 的 很 仔 细 也 特 别 有 趣 ， 学 到 了 很 多 东 西 。 但 是 嵩 天 老 师 的 进 阶 语 法 课 程 在 哪 里 呀 ， 怎 么 在 网 易 云 课 堂 上 找 不 到 。,O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1
老 师 讲 得 很 棒 ， 也 给 我 们 建 立 了 学 习 群 ， 只 是 很 少 又 能 恰 当 得 运 用 学 习 群 ， 若 是 非 本 专 业 没 有 环 境 ， 还 是 会 缺 少 学 习 氛 围 。,B-ASP I-ASP B-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1
本 人 从 使 用 C 语 言 、 R 语 言 转 到 使 用 P y t h o n 语 言 ， 感 谢 本 课 程 提 供 的 丰 富 多 彩 的 内 容 。,O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 1 1 1 -1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
学 了 这 些 课 程 后 使 我 对 P Y T H O N 产 生 了 浓 厚 的 兴 趣,O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP,-1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 的 真 好 ， 就 是 感 觉 p y t h o n 1 2 3 上 前 几 周 有 些 题 目 超 前 了,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
老 师 讲 的 特 好 ！ ！ ！ 真 不 愧 为 p y t h o n 大 师 ！ ！ ！,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1
非 常 好 ， 自 我 提 升 的 好 视 频 ， 热 爱 a i ， 热 爱 自 动 化,O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2
挺 好 的 ， 课 程 深 入 浅 出 ， 很 多 内 容 细 节 和 机 制 讲 的 很 清 楚 。 老 师 也 没 整 什 么 花 活 ， 就 是 正 常 讲 课 ， 但 是 案 例 设 计 的 很 好 ， 很 吸 引 人 ， 还 把 一 些 现 在 很 提 倡 的 ‘ 思 政 ’ 元 素 结 合 的 特 别 自 然 。 瑕 不 掩 瑜 的 地 方 是 ： 老 师 大 概 对 计 算 机 熟 悉 到 已 经 融 入 生 活 ， 有 极 少 部 分 内 容 并 没 有 考 虑 到 非 计 算 机 专 业 ， 对 计 算 机 仅 限 于 会 用 O f f i c e 和 视 频 软 件 ， 学 习 P y t h o n 就 是 为 了 考 级 的 学 生 。 不 过 助 教 的 及 时 回 答 应 该 可 以 解 决 这 类 问 题 。,O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 解 的 深 入 浅 出 ， 很 细 致 ， 课 程 很 好 ！ 推 荐 推 荐 ！ ！ ！,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O,2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 好 ， 编 程 半 个 小 白 也 能 听 懂 ， 老 师 超 级 棒 。,O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
很 喜 欢 老 师 的 课 ， 收 获 满 满 。,O O O B-ASP I-ASP O B-ASP O B-ASP I-ASP O O O,-1 -1 -1 1 1 -1 2 -1 2 2 -1 -1 -1
从 小 白 到 能 看 懂 代 码 编 写 的 意 思 ， 收 获 颇 多 ， 老 师 在 讲 解 内 容 时 很 仔 细 ， 希 望 能 在 每 节 课 的 结 尾 ， 可 以 有 拓 宽 和 延 伸 的 参 考 建 议,O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 2 2 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
简 单 易 懂 ， 深 入 简 出 ， 课 后 习 题 挑 战 较 大,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 0 0 -1 -1
老 师 授 课 非 常 细 致 ， 深 入 浅 出 ， 课 程 有 很 强 的 逻 辑 体 系 ， 很 适 合 小 白 入 门,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
讲 的 很 好 。 希 望 练 习 题 能 丰 富 一 些,O O O O O O O B-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1
介 绍 了 为 啥 学 P y t h o n ， 比 起 学 校 老 师 讲 得 更 有 意 思,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
"适 合 没 有 基 础 的 同 学 , 如 果 配 合 课 本 的 话 效 果 更 佳 , 不 过 一 定 要 记 得 去 p y t h o n 1 2 3 做 练 习 , 自 己 打 代 码 , 这 样 一 定 可 以 学 会 p y t h o n 的 !",O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1
非 常 的 n i c e 深 入 浅 出 ， 受 益 匪 浅,O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
还 是 要 配 套 练 习 使 用 ， 不 然 没 有 啥 效 果 ， 老 师 提 供 的 p y t h o n 1 2 3 平 台 是 个 不 错 的 选 择,O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP,-1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 1 1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2
课 程 视 频 不 需 要 分 得 这 么 细 ， 有 的 视 频 太 短 了 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O,0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1
j l k d h o a u h i o d a h j i o p d,O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 基 础 ， 初 学 p y t h o n 也 能 看 懂 。 总 体 课 程 设 计 很 好 ， 学 习 的 阶 段 性 获 得 感 很 强 ， 原 本 只 是 想 随 便 看 看 p y t h o n ， 没 想 到 看 了 几 周 发 现 有 点 喜 欢 了 。 不 足 的 地 方 大 概 是 没 有 介 绍 到 面 对 对 象 的 编 程 ， 总 体 只 是 一 个 p y t h o n 基 础 的 介 绍 ， 少 一 点 深 度 。 但 是 作 为 入 门 的 课 程 ， 真 是 相 当 不 错 了 。 另 外 ， 嵩 老 师 超 有 魅 力 2 3 3 3 3,O B-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O,-1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 课 很 亲 切 ， 实 例 选 得 非 常 好 ， 简 单 的 一 个 例 子 包 含 很 多 的 知 识 点 。,B-ASP I-ASP O B-ASP O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O,2 2 -1 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1
简 单 易 懂 ， 如 果 能 联 系 工 作 就 更 好 了,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1
内 容 密 度 很 大 ， 可 能 并 不 是 很 适 合 非 计 算 机 专 业 的 小 白 。 总 体 思 路 很 清 晰 ~,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 -1 0 0 -1 2 2 2 2 -1 -1 -1 -1
老 师 讲 课 很 专 业 ， 课 程 具 有 很 强 的 逻 辑 性 和 连 贯 性 ， 值 得 学 习,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 -1 -1 -1 -1 -1
老 师 讲 的 很 清 晰 ， 容 易 接 受 ， 适 合 于 初 学 者 入 门 学 习,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 到 了 很 多 ， 刚 开 始 听 的 时 候 感 觉 没 有 头 绪 ， 感 觉 讲 得 很 乱 ， 适 应 了 后 就 很 好 了 ， 大 家 太 强 了 ， P y t h o n 1 2 3 我 每 次 都 拿 不 到 2 0 分 ， 每 次 总 要 错 个 选 择 题 . . . . . . . . .,O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 棒 ， 棒 就 棒 在 我 没 有 毅 力 学 下 去,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
老 师 讲 得 挺 好 的 ， 就 是 很 多 知 识 点 只 过 一 遍 ， 还 是 需 要 自 己 课 后 多 练 习 的 ， 否 则 真 的 忘 得 很 快 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 好 ！ 吸 引 着 我 不 停 地 去 学 习 ！ ！ 赞 ！ ！ ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 很 用 心 ， 每 周 都 能 结 合 一 些 实 例 来 深 化 知 识 ， 理 解 起 来 非 常 容 易 ， 作 为 专 科 的 学 生 还 是 希 望 老 师 能 视 频 能 再 详 细 一 些 ， 辛 苦 老 师 。,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 是 讲 解 的 很 详 细 ， 听 起 来 也 怎 么 费 劲 。 谢 谢,O O B-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 循 序 渐 进 ， 易 于 理 解 。 特 别 是 实 例 配 合 的 非 常 好 ， 对 于 基 础 学 习 者 非 常 有 帮 助 。,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1
课 程 很 有 体 系 ， 并 且 通 过 理 论 与 实 例 的 结 合 极 大 的 提 高 了 学 习 的 兴 趣 和 知 识 的 接 受 程 度 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1
自 学 不 易 ， 自 身 还 不 懂 英 语 ， 目 标 不 明 。 但 很 想 学 好 。,O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
好 ， 如 果 再 把 面 向 对 象 加 入 就 完 美 了 。,O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 编 排 确 实 花 了 心 思 ， 适 合 新 手 入 门 。 不 足 之 处 ： 1 、 讨 论 区 老 师 答 疑 版 块 ， 回 复 敷 衍 了 事 ， 答 非 所 问 ； 2 、 申 请 电 子 证 书 也 要 收 费 （ 这 应 该 是 平 台 的 锅 ） ！ ！,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
讲 的 很 好 用 例 题 来 从 浅 入 深 让 我 们 有 更 深 的 印 象,B-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
授 课 内 容 很 好 ， 讲 授 也 清 晰 。 感 觉 需 要 再 学 一 遍,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
本 课 程 浅 显 易 懂 ， 讲 解 细 致 ， 是 P y t h o n 入 门 的 好 课 程 ！,O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,-1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2 -1
真 的 非 常 喜 欢 嵩 天 老 师 ， 对 P y t h o n 的 学 习 让 我 打 开 了 一 扇 新 的 窗 户 ， 也 会 继 续 深 入 学 习 下 去 ！,O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
超 爱 嵩 老 师 的 p y t h o n 课 程 国 家 精 品 是 真 的 名 副 其 实 ！ ！ ！ ！,O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,-1 -1 2 2 2 -1 1 1 1 1 1 1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
通 过 课 程 对 P y t h o n 语 言 有 了 一 定 的 了 解,O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 2 2 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
课 堂 非 常 棒 ， 老 师 讲 的 深 入 浅 出 ， 课 后 练 习 也 非 常 具 有 代 表 性 ， 让 我 收 获 满 满,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1
这 老 师 积 极 的 心 理 暗 示 在 课 程 中 帮 助 很 大 ， 当 然 还 有 老 师 的 逻 辑 思 维 能 力 可 以 兼 顾 小 白 ， 非 常 友 好,O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,-1 2 2 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 挺 好 ， 我 听 的 也 很 认 真 ， 感 谢 慕 课 平 台 ， 感 谢 各 位 老 师 免 费 的 、 无 私 的 精 品 课 程 的 分 享 。,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
上 了 6 周 的 课 了 ， 非 常 感 谢 老 师 的 讲 授 。 与 市 场 上 其 他 p y t h o n 课 不 同 ， 老 师 的 课 深 入 浅 出 且 成 体 系 的 讲 解 p y t h o n ， 实 在 让 我 受 益 匪 浅 。 我 个 人 有 一 些 编 程 的 基 础 ， 但 是 很 零 碎 ， 现 在 因 学 业 需 要 ， 要 把 p y t h o n 弄 懂 弄 会 ， 幸 甚 可 以 看 到 嵩 老 师 的 课 程 。,O O O O O B-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP O O O B-ASP I-ASP O B-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 -1 -1 -1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 2 2 -1
这 门 课 程 真 的 很 不 错 ， 学 习 内 容 循 序 渐 进 ， 安 排 得 当 ， 每 个 视 频 内 容 也 不 长 ， 非 常 精 简 ， 学 习 一 点 也 不 枯 燥 ， 嵩 老 师 讲 得 非 常 好 ！,O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 得 通 俗 易 懂 ， 通 过 讲 解 ， 把 枯 燥 编 程 学 习 变 得 轻 松 愉 悦 ， 给 老 师 大 大 的 点 赞 ！,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
言 简 意 赅 ， 深 入 浅 出 ， 有 实 用 ， 值 得 学 习,O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 是 中 南 大 学 研 究 生 ， 平 时 写 C / J a v a 的 语 言 ， 之 前 一 直 没 碰 过 p y t h o n 。 第 一 次 接 触 p y t h o n 就 是 上 的 嵩 天 老 师 的 网 络 爬 虫 课 程 （ 也 是 国 家 精 品 课 程 嘛 ） ， 觉 得 老 师 讲 的 非 常 好 。 又 回 过 头 补 充 看 《 P y t h o n 语 言 程 序 设 计 》 ， 真 的 是 印 象 深 刻 ， 受 益 匪 浅 ， 不 仅 仅 是 编 程 方 面 ， 还 有 天 天 向 上 的 力 量 、 爬 虫 、 绘 图 、 编 程 别 人 不 会 说 的 概 念 、 基 础 知 识 、 举 一 反 三 环 节 等 ， 课 程 体 系 很 完 备 。 希 望 嵩 天 老 师 以 后 多 多 出 课 程 ， 持 续 关 注 您 ！,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 2 2 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 -1 1 1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1 -1 1 1 1 1 1 1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
非 常 贴 心 给 出 了 教 学 P P T ， 感 谢 老 师 的 无 私 奉 献,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1
每 个 小 节 时 间 很 短 ， 利 用 碎 片 化 时 间 学 习 很 方 便 ， 老 师 讲 得 通 俗 易 懂 ， 学 起 来 很 舒 服,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 循 序 渐 进 ， 也 有 实 例 和 练 习 可 以 理 解 和 检 验 所 学 内 容 ， 小 白 学 起 来 不 费 力,B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
单 次 课 虽 然 短 了 一 些 却 很 充 实 ， 如 果 能 加 入 一 些 该 门 课 程 前 景 展 望 之 类 的 会 更 有 趣 吧 ， 个 人 理 解,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
以 例 子 讲 解 知 识 点 非 常 生 动 易 理 解 ， 帮 助 运 用 。,O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O,-1 2 2 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 开 始 的 时 候 有 点 难 ， 总 体 来 说 这 门 课 非 常 好,B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1
深 入 浅 出 ， 小 白 也 可 能 只 是 前 两 节 课 稍 微 南 一 点 ， 学 着 很 有 意 思,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
以 前 找 过 别 的 视 频 学 ， 学 了 一 阵 就 放 弃 了 。 嵩 老 师 的 课 安 排 的 很 好 ， 有 基 础 内 容 ， 有 趣 的 案 例 ， 还 传 递 编 程 的 计 算 思 维 ， 对 于 没 什 么 基 础 的 人 来 说 还 是 比 较 容 易 入 门 的 。 希 望 可 以 开 设 更 多 进 阶 课 程 。,O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
对 P Y T H O N 入 门 学 习 ， 老 师 讲 课 讲 的 非 常 好 ， 深 入 浅 出,O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O,-1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 好 ， 对 我 有 学 习 P Y T H O N 有 很 大 的 帮 助,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2
嵩 天 老 师 讲 的 非 常 好 ！ 喜 欢 ！ 课 程 以 小 视 频 形 式 展 现 ， 不 容 易 走 神 ， 很 棒 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 非 常 有 效 率 ， 讲 解 也 很 清 楚 ， 超 出 预 期,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2
确 实 很 不 错 ， 深 入 浅 出 ， 特 别 是 实 例 讲 解,O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
感 觉 嵩 天 老 师 讲 的 非 常 好 ！ 条 理 清 晰 ， 层 次 分 明 ， 通 俗 易 懂,O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
讲 不 出 所 以 然 ， 照 本 宣 科 的 读 概 念 ， 讲 得 太 浅 了 ， 没 任 何 实 用 性,O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0
老 师 教 的 很 好 ， 对 于 p y t h o n 入 门 很 有 帮 助,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 2 2
编 程 还 是 有 难 度 的 ， 感 觉 思 维 方 式 很 重 要 ， 难 怪 数 学 好 的 人 都 是 编 程 高 手 。,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
看 第 一 集 就 是 很 用 心 ， 感 谢 各 位 老 师 ， 学 习 无 墙 。,O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 可 惜 我 发 现 的 很 晚 ， 也 没 有 学 习 完 成 ， 我 希 望 在 下 学 期 早 点 发 现 课 程 ， 跟 着 老 师 的 步 子 走,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 1 1 -1
p y t h o n 1 2 3 平 台 需 要 老 师 的 课 程 代 码 ， 怎 么 要,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 -1 1 1 1 1 -1 -1 -1 -1
还 是 比 较 详 细 的 ， 虽 然 初 次 接 触 还 有 些 地 方 懵 逼 ， 但 是 多 用 几 次 就 会 好 很 多,O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 很 好 ， 自 己 基 础 不 好 ， 还 要 努 力 学 习 掌 握 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 设 计 的 非 常 好 ， 循 序 渐 进 ， 条 理 清 楚 ； 人 生 苦 短 ， 快 用 P y t h o n ， P y t h o n 实 在 太 强 大 啦,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
第 五 章 开 始 有 点 难 了 希 望 我 能 继 续 下 去,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O,0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 棒 棒 。 。 。 。 。 。 。 。 。 。 。 。 。 。 。 。 。 。,O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
从 完 成 课 后 作 业 和 测 验 的 过 程 上 ， 我 可 以 感 受 到 自 己 学 到 了 一 些 东 西 ； 在 思 想 方 法 上 ， 更 加 了 解 到 了 新 的 解 决 问 题 的 思 路 ， 比 如 模 块 化 、 工 程 化 的 解 决 问 题 思 路 ， 计 算 机 解 决 问 题 拓 宽 了 我 的 视 野 ， 让 我 产 生 了 浓 厚 的 兴 趣 和 深 入 学 习 的 欲 望,O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,-1 -1 -1 2 2 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 2 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2
条 理 清 晰 ， 由 浅 入 深 ， 适 合 p y h t o n 入 门 学 习 ！ ！ ！,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 -1 -1 -1
老 师 讲 的 很 浅 显 ， 不 错 ， 都 听 懂 了,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
好 难 ， 好 花 时 间 ， 没 必 要 分 那 么 多 小 节 ， 可 以 连 成 一 大 节,O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 课 ， 条 理 清 楚 ， 逻 辑 清 晰 ， 从 计 算 机 发 展 历 程 讲 起 ， 到 p y t h o n 语 言 的 入 门 ， 只 是 学 生 自 己 似 乎 对 编 程 的 兴 趣 不 是 很 大 ， 只 是 这 个 是 热 门 ， 就 想 看 个 究 竟 ， 选 了 老 师 这 门 课 ， 但 教 课 方 式 、 课 程 安 排 都 非 常 合 理 ， 老 师 对 这 门 课 程 的 喜 爱 也 很 好 的 传 达 给 我 了 。,B-ASP I-ASP O B-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 不 错 ， 不 过 老 师 说 话 有 时 候 会 平 舌 翘 舌 口 胡 呢 h h h,O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
天 天 向 上 的 案 例 选 得 太 励 志 了 ！ ！ ！ ！ 赞 赞 赞,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O,2 2 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
在 家 能 上 名 校 的 课 程 ， 是 我 们 的 幸 运 ， 是 我 们 这 个 时 代 的 幸 运 感 谢 上 课 的 老 师 和 学 校 ， 感 谢 我 们 亲 爱 的 祖 国 。,O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
不 愧 是 国 家 精 品 课 程 ， 老 师 讲 非 常 棒 ！,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1
老 师 内 容 安 排 的 让 我 感 觉 很 有 条 理 ， 讲 的 也 很 清 晰 。 感 谢 老 师 ！ ！ ！,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O,1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
非 常 喜 欢 嵩 天 老 师 的 讲 课 。 就 是 感 觉 每 个 视 频 有 点 短,O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
还 可 以 的 ， 对 于 门 外 汉 来 说 比 较 适 合 的,O O O O O O O B-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1
知 识 讲 解 的 很 全 面 ， 而 且 简 单 易 懂 ， 为 老 师 点 赞 ~,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
计 算 机 入 门 还 是 稍 微 有 点 难 度 的 。 前 几 周 的 课 觉 得 挺 简 单 ， 第 6 周 的 课 开 始 稍 微 有 点 吃 力 了 。,B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP O O O O O O O O O O B-ASP O O O O O O O O O O,1 1 1 1 1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
前 两 年 怎 么 学 也 学 不 进 去 ， 每 年 都 报 名 ， 今 年 突 然 就 学 进 去 了 ， 感 觉 好 像 这 门 课 程 也 做 了 很 多 调 整 ， 现 在 的 这 种 课 程 安 排 医 学 编 程 小 白 感 觉 很 O K ， 辛 苦 各 位 老 师 ！ 学 完 这 门 要 接 着 学 习 p y t h o n 数 据 分 析 哈 哈,O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 -1
内 容 组 织 特 别 好 ， 或 者 别 出 心 裁 ， 我 觉 得 语 言 类 课 程 如 果 不 是 长 期 系 统 的 学 ， 就 该 这 么 组 织 ， 问 题 导 向,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
j 简 单 易 懂 。 但 有 些 程 序 还 是 会 有 不 懂 的 地 方,O O O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
太 强 了 以 后 学 p y t h o n 就 来 m o o c 领 一 套 嵩 天 老 师 大 礼 包 就 好 了,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 1 1 1 1 -1 -1 -1 2 2 2 2 2 2 2 -1 -1 -1
我 学 会 了 p y t h o n 程 序 设 计 得 基 础 知 识,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1
课 程 循 序 渐 进 ， 安 排 合 理 ， 讲 解 透 彻 。 看 过 很 多 P y t h o n 的 t u t o r i a l ， 觉 得 这 个 是 最 适 合 入 门 的 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
讲 的 特 别 好 ， 自 己 去 实 践 更 重 要 。,B-ASP O O O O O O O O B-ASP I-ASP O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
虽 然 还 是 菜 菜 菜 菜 鸡 一 枚 ， 课 程 觉 得 是 挺 好 的,O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
这 老 师 认 真 准 备 ， 讲 解 清 楚 ， 平 台 资 源 利 用 率 高 ， 学 到 了 许 多 东 西 。 没 能 早 点 发 现 这 个 课 程 ， 真 是 遗 憾 ！,O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O,-1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
这 个 课 非 常 好 ， 实 用 ， 在 学 习 的 同 时 也 找 到 了 P Y T H O N 在 生 活 中 的 实 际 应 用,O O B-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 2 2
方 式 很 不 错 ， 就 是 其 进 阶 课 程 与 该 课 程 时 间 上 有 冲 突 ， 不 能 很 好 衔 接,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
假 期 充 电 ， 跟 着 学 基 础 应 用 ， 太 棒 了,O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1
嵩 老 师 的 讲 课 通 俗 易 懂 ， 很 受 用 ！,B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
"l i k e t h i s c o u r s e , t h a n k s f o r s h a r i n g",O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2
真 的 是 可 以 让 小 小 白 学 会 的 p y t h o n 课 程,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2
老 师 讲 的 非 常 好 ！ 感 谢 ！ 辛 苦 了 ！,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
能 听 懂 课 ， 看 得 懂 代 码 ， 但 是 自 己 却 写 不 出 程 序 代 码 。 请 问 老 师 怎 么 办 ？,O O O B-ASP O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 -1 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 1 1 -1 -1 -1 -1
老 师 讲 的 很 好 很 细 ， 很 喜 欢 这 种 授 课 方 式,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
我 太 爱 了 ， 简 直 救 我 命 啊 ， 我 会 努 力 学 完 学 好 的 ， 真 的 太 感 谢 了 ， 跪 了,O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"不 适 合 初 级 学 习 , 适 合 有 基 础 底 子 , 贯 穿 学 习 用 的 .",O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 0 0 0 0 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 -1 -1 -1
这 个 课 程 真 的 很 棒 ！ 老 师 把 复 杂 的 编 程 语 言 讲 得 课 程 易 学 易 懂 ！,O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1
非 常 有 用 ， 系 统 地 讲 解 了 p y t h o n 的 不 同 用 法 ， 强 度 适 中 ， 每 周 只 需 要 学 习 两 三 个 小 时,O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 1 1 1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
系 统 性 的 教 学 ， 让 人 很 容 易 理 解 ， 对 初 学 者 很 友 好,O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
前 面 还 挺 容 易 的 ， 后 面 就 满 满 看 不 懂 了 ， 只 看 视 频 估 计 还 有 点 不 足 。 但 老 师 的 授 课 还 是 能 感 觉 到 心 意 满 满 的,O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
得 到 了 p y t h o n 相 关 语 法 与 简 单 应 用 ， 在 使 用 中 也 学 会 了 使 用 一 些 第 三 方 库 ， 得 到 很 好 的 感 觉 。,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O,-1 -1 -1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1
"讲 的 非 常 细 致 , 很 适 合 初 学 者 使 用",B-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1
嵩 老 师 讲 得 很 好 ， 循 序 渐 进 ， 让 初 学 者 了 解 p y t h o n 的 魅 力 ， 以 及 简 单 快 乐 的 学 习 。,B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
h a o h a h a o h a h o h a h a o,O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 很 满 意 ， 老 师 讲 的 很 仔 细 ， 我 很 明 白,O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
真 的 很 好 ， 让 我 入 门 了 P y t h o n,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2
嵩 天 老 师 讲 的 属 实 不 错 ， 别 人 家 的 老 师,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
课 程 组 织 很 合 理 ， 老 师 讲 解 的 很 生 动 很 形 象 ， 确 实 能 够 学 到 很 多 知 识 ， 感 谢 老 师 开 设 这 门 课,B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP,2 2 2 2 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 2
比 那 个 网 上 的 图 灵 学 院 好 的 不 要 太 多 。 没 有 功 利 性 ， 专 心 教 基 础 知 识 ， 对 于 跨 行 跨 专 业 的 学 习 是 非 常 重 要 的 。 跨 行 跨 专 业 的 学 习 跨 行 跨 专 业 的 学 习,O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 2 2 2 2 2 2 2 2 2 2
这 个 课 程 十 分 棒 ， 循 序 渐 进 ， 虽 然 之 前 自 己 学 过 一 些 编 程 语 言 ， 但 学 了 p y t h o n 后 才 发 现 ， 原 来 编 程 可 以 那 么 简 单 那 么 愉 快,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
固 特 异 o u g y j c u i h u g k h g u i,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
P y t h o n 语 言 W i n d o w s 系 统 开 发 环 境 没 办 法 看,O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 0 0 0 0 -1 -1 -1 -1
课 程 设 计 合 理 ， 老 师 讲 课 方 式 风 趣 ， 受 益 匪 浅 ！,B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
课 程 有 趣 且 有 收 获 ， 适 合 入 门 编 程 课 程 有 趣 且 有 收 获 ， 适 合 入 门 编 程,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1
感 觉 讲 的 挺 简 单 易 懂 的 ， 每 节 讲 的 内 容 都 是 在 章 节 末 一 文 档 形 式 显 示 出 来 ， 可 供 大 家 谁 是 翻 阅 学 习 ， 也 有 很 多 课 后 习 题 可 以 帮 助 我 们 加 深 对 某 些 知 识 点 的 理 解 ， 巩 固 自 己 的 学 习 成 果,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
老 师 授 课 质 量 高 ， 答 疑 区 也 会 有 回 复 ， 很 好,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O,1 1 2 2 2 2 -1 -1 1 1 1 -1 -1 -1 2 2 -1 -1 -1
从 p y t h o n 基 础 开 始 ， 由 简 入 难 ， 很 好 理 解 ， 课 后 练 习 和 测 试 的 题 量 也 很 好 ， 需 要 花 时 间 但 又 不 会 很 有 负 担 。,O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
深 刻 感 受 到 嵩 天 老 师 及 其 课 程 团 队 的 认 真 负 责 ， 用 心 良 苦 。 课 程 细 节 用 心 ， 练 习 题 丰 富 。 老 师 讲 课 内 容 也 是 由 浅 入 深 ， 由 易 到 难 。,O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
现 在 主 要 在 听 嵩 天 老 师 的 p y t h o n 语 言 程 序 设 计 ， 做 的 网 络 课 程 很 用 心 ， 讲 的 也 很 细 致 入 微 ， 每 个 知 识 点 讲 的 都 挺 透 彻 的 ， 而 且 最 让 我 欣 慰 的 是 不 像 一 般 的 课 程 ， 听 起 来 比 较 有 趣 味 性 。 谢 谢 中 国 大 学 m o o c 提 供 这 么 一 个 平 台 ， 让 我 这 种 已 经 离 开 大 学 的 大 学 生 还 能 及 时 的 冲 一 冲 电 。,O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 组 织 逻 辑 清 晰 ， 案 例 丰 富 ， 老 师 讲 解 很 棒 ！,B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O,2 2 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 1 1 2 2 -1 -1 -1
希 望 老 师 最 后 再 来 一 次 系 统 的 复 习 ， 可 以 占 一 周 课 的 那 种,O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 1 1 2 -1 -1 -1
老 师 上 课 深 入 浅 出 ， 声 音 富 有 吸 引 力 ， 我 在 学 习 中 受 益 匪 浅 ， 非 常 感 谢 大 家 。,B-ASP I-ASP O B-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O,2 2 -1 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 详 细 ， 有 条 理 ， 让 我 有 了 很 大 的 兴 趣,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
好 ， 对 我 的 p y t h o n 学 习 有 了 不 小 帮 助,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 2 2
有 基 础 知 识 ， 有 实 例 操 作 ， 还 有 测 评 ， 老 师 讲 解 语 速 度 也 合 适 ， 非 常 好 ， 期 待 更 高 阶 的 讲 解 。,O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,-1 1 1 1 1 -1 -1 1 1 1 1 -1 -1 -1 1 1 -1 1 1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
感 觉 课 还 是 很 不 错 的 。 从 设 计 到 组 织 。 有 所 收 获 。,O O B-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 2 2 -1
还 想 学 P y t h o n 面 向 对 象 的 东 西,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
这 门 课 程 很 不 错 ， 可 以 作 为 基 础 知 识 的 复 习 ， 不 过 对 新 手 来 说 可 能 还 缺 少 一 些 实 例 ， 所 以 找 题 目 多 多 练 习 吧 ~,O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
特 别 适 合 我 这 种 没 什 么 基 础 的 小 白 学 习 ， 谢 谢 老 师,O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
准 备 上 大 学 ， 感 觉 这 门 课 ， 感 觉 老 师 讲 的 还 挺 不 错 ， 给 我 帮 助 挺 大 的,O O O O O O O O O O B-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
第 一 次 在 中 国 大 学 m o o c 上 完 整 的 学 完 一 个 课 程 ， 老 师 讲 的 很 好,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 -1 -1 -1 -1
优 秀 ， 特 别 是 配 套 的 练 习 完 整 ， 那 是 相 当 的 优 秀 ！,O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 不 错 老 师 的 讲 解 由 浅 入 深 循 序 渐 进 不 会 感 到 困 难 在 快 乐 中 就 掌 握 了 这 门 语 言 十 分 幸 福,O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
非 常 适 合 编 程 小 白 的 p y t h o n 课 ！ 嵩 老 师 从 简 入 繁 、 步 步 深 入 ， 逐 渐 帮 学 生 构 建 出 p y t h o n 的 知 识 体 系 。 课 程 中 ， 不 管 是 案 例 的 设 置 ， 还 是 老 师 手 把 手 敲 代 码 ， 都 是 理 论 与 实 践 相 结 合 的 有 益 实 践 。 这 是 一 门 非 常 值 得 学 习 和 推 荐 的 课 程 ！ ！ ！ 感 谢 教 学 团 队 各 位 老 师 辛 勤 付 出 ！ ！,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 非 常 之 好 ， 深 入 浅 出 ， 清 晰 明 了,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 由 浅 入 深 ， 层 次 条 理 清 楚 ， 并 且 老 师 深 入 浅 出 ， 帮 助 我 们 认 识 了 P H T H O N ， 也 开 阔 了 我 们 的 视 野 ， 非 常 好,O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 的 很 清 晰 ， 明 白 ， 概 念 准 确 ， 实 例 很 好,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1
科 学 系 统 ， 非 常 好 ， 真 的 是 入 门 级 必 学 课 程 ！ 给 课 程 和 老 师 点 赞,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 2 2 -1 -1
对 于 毫 无 基 础 的 我 来 说 ， 真 是 太 有 帮 助 了 。 十 分 优 秀 ！ ！ ！,O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1
虽 然 是 在 断 断 续 续 的 学 习 ， 但 是 对 于 基 础 薄 弱 的 我 而 言 ， 深 入 浅 出 循 序 渐 进 的 教 学 方 式 是 很 有 帮 助 的 ， 因 此 对 我 的 帮 助 很 大 。,O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 的 讲 解 非 常 详 细 ， 我 们 的 练 习 非 常 合 理 ， 很 喜 欢 这 个 课 程 ~ ~ ~,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 课 方 式 非 常 到 位 ， 很 适 合 基 础 人 ， 练 习 张 弛 有 度,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O,1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 的 挺 好 的 ， 只 要 脚 踏 实 地 的 认 真 学 ， 就 会 学 好,O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 各 种 学 习 考 证 考 公 资 料 资 源 各 大 机 构 资 源 ， 包 更 新 p p t E x c e l 教 学 技 能 培 训 要 资 源 撕,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 -1
很 不 错 ， 对 新 手 比 较 友 好 。 不 过 越 到 后 面 难 度 越 大 。,O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
本 门 课 程 重 点 突 出 ， 课 件 的 每 一 分 钟 ， 练 习 及 测 试 的 每 一 个 案 例 ， 都 经 过 精 挑 细 选 ， 学 习 过 程 十 分 流 畅 。 课 程 注 重 实 践 ， 与 现 实 世 界 联 系 紧 密 。 经 过 4 0 小 时 左 右 的 学 习 和 练 习 ， 基 本 获 得 对 P y t h o n 这 种 超 级 语 言 的 入 门 ， 学 习 收 获 感 十 足 ， 感 谢 嵩 老 师 及 课 程 团 队 。 本 课 程 非 常 值 得 推 荐 ， 感 兴 趣 的 小 伙 伴 赶 紧 加 入 吧 。,O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 解 详 细 ， 语 音 清 晰 ， 课 程 间 隙 有 度 ， 不 容 易 感 觉 疲 劳 ， 由 浅 入 深 ， 不 像 有 些 课 程 上 来 直 接 代 码 开 始 ， 稀 里 糊 涂 开 始 必 然 稀 里 糊 涂 结 束 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
组 织 有 序 ， 简 单 易 懂 ， 学 练 结 合 。 推 荐 ！,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
老 师 声 音 好 听 讲 课 清 晰 层 次 感 强,B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 2 2 -1 -1 2 2 2 -1
非 常 好 ， 作 业 和 课 程 节 奏 设 置 的 很 好,O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1
讲 解 的 调 理 清 晰 ， 思 路 清 楚 ， 有 点 后 悔 没 有 早 点 发 现 。,B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 深 入 浅 出 ， 结 给 一 些 小 程 序 ， 很 有 趣 味 性 。 讲 得 很 好 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 2 -1 2 -1 -1 -1 -1
超 出 预 期 ， 讲 的 很 好 ， 细 节 讲 的 很 细 ， 易 懂 ， 总 的 来 说 很 不 错 的 一 门 课,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP,2 2 2 2 -1 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2
【 这 是 2 0 1 8 年 ， 第 6 次 开 课 的 评 价 】 嵩 老 师 的 讲 解 ， 清 晰 明 了 ， 张 弛 有 度 ； 结 合 案 例 ， 面 向 “ 解 决 现 实 问 题 ” 的 讲 解 ； 对 每 个 案 例 ， 层 层 深 入 ， 符 合 认 知 习 惯 。 【 这 是 本 次 开 课 的 评 价 ， 2 0 2 1 年 第 1 6 次 开 课 】 三 年 前 ， 在 第 6 次 开 课 上 ， 跟 着 嵩 老 师 开 始 学 习 p y t h o n 。 三 年 来 ， 在 基 于 数 据 和 文 本 的 信 息 处 理 上 ， 面 向 问 题 的 解 决 ， p y t h o n 是 一 个 很 好 的 助 手 ； 但 自 己 基 础 不 牢 固 ， 知 识 不 体 系 的 问 题 ， 愈 发 明 显 。 本 次 秋 季 班 ， 一 口 气 报 名 了 四 门 课 （ p y t h o n 语 言 、 数 据 分 析 、 网 络 爬 虫 、 科 学 计 算 ） ， 目 前 课 程 内 容 都 已 学 完 ， 受 益 匪 浅 ！ 难 度 较 大 的 科 学 计 算 三 维 可 视 化 ， 也 顺 利 啃 完 了 全 部 内 容 ， 期 间 大 量 阅 读 英 文 手 册 。 集 齐 四 枚 卡 片 （ 四 门 课 程 ） ， 召 唤 神 龙 （ 面 向 问 题 的 解 决 ） 吧 ！,O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 2 2 2 2 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 于 0 基 础 的 同 学 非 常 友 好 ， 搭 建 p y t h o n 的 基 础 知 识 框 架 ， 为 进 一 步 学 习 打 好 基 础 。 非 常 赞,O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
结 合 具 体 案 例 讲 解 关 于 P y t h o n 的 相 关 知 识 ， 非 常 有 趣 ， 讲 解 的 也 很 全 面 ， 是 真 的 有 学 到 知 识 了,O O O O B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 1 1 2 2 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
跟 着 老 师 思 路 能 够 系 统 地 进 行 p y t h o n 学 习,O O B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2
课 程 深 入 浅 出 ， 系 统 性 强 ， 一 门 好 课,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP,2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2
课 程 讲 解 逻 辑 清 晰 、 层 次 分 明 ， 案 列 有 趣 。,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 解 的 很 详 细 ， 案 例 分 析 也 很 到 位,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
真 的 感 谢 老 师 ， 5 5 5 5 讲 的 真 的 很 精 细 了 ， 吹 爆,O O O O B-ASP I-ASP O O O O O B-ASP O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 不 错 ， 能 提 高 自 己 的 编 程 能 力,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
跟 着 嵩 老 师 真 的 学 了 很 多 ！ 这 门 课 太 精 品 了 ！ 老 师 们 辛 苦 了 ！ ！,O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP O O O O O B-ASP I-ASP I-ASP O O O O O,-1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1
非 常 受 启 发 ， 希 望 有 更 多 的 实 例 ， 或 者 专 门 开 一 门 实 战 教 学 的 课 就 更 好 了 ， 从 小 白 学 起 ， 首 先 需 要 一 步 一 步 模 仿 。,O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
认 识 到 了 很 多 自 己 不 知 道 的 事 情 ；,O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
思 路 清 晰 ， 干 货 慢 慢 ， 听 着 很 有 意 思 ， 很 感 谢 m o o c 平 台 和 老 师 为 我 带 来 的 学 习 机 会,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2
挺 好 的 ， 就 是 部 分 练 习 题 的 有 点 小 问 题,O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 0 0
老 师 讲 得 很 好 ， 由 简 到 繁 ， 同 时 带 有 案 例 讲 解 。,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
老 师 讲 的 很 清 爽 ， 新 手 听 课 需 要 有 层 次 性 ， 再 加 上 课 后 作 业 ， 学 起 来 效 率 较 高,O O O O O O O O B-ASP I-ASP O B-ASP O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 -1 -1
课 程 内 容 充 实 ， 案 例 与 实 践 相 结 合,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1
课 程 内 容 丰 富 ， 讲 课 细 致 到 位 ， 课 后 练 习 举 一 反 三 ， 易 懂 易 学 ！,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
虽 然 才 进 行 到 第 二 周 ， 但 是 真 的 非 常 不 错 ， 对 于 小 白 及 其 友 好 ， 完 全 能 听 得 懂 ， 跟 着 老 师 的 步 伐 来 ， 课 后 自 己 再 花 点 心 思 ， 完 全 可 以 搞 定 这 门 课 程,O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
不 知 道 我 们 老 年 人 学 这 个 还 有 用 没 得 。,O O O O O B-ASP I-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
首 次 使 用 慕 课 学 习 ， 嵩 天 老 师 讲 的 P y t h o n 真 不 错 。,O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 2 2 2 2 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
课 程 很 有 趣 ， 知 识 循 序 渐 进 ， 对 稍 有 C 编 程 基 础 的 我 上 手 很 快 ！,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 的 很 好 ， 简 单 易 懂 ， 有 p y t h o n 语 言 的 的 特 点 ， 希 望 能 出 一 些 p y t h o n 在 工 作 和 生 活 的 具 体 应 用 类 课 程 。,B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1
对 于 编 程 小 白 有 些 难 度 ， 但 还 在 努 力 中,O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O,-1 -1 1 1 1 1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1
老 师 讲 地 很 系 统 、 很 详 细 ， 超 级 棒 ！,B-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 内 容 很 多 ， 也 通 俗 易 懂 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
干 货 满 满 ， 不 愧 是 教 授 ， 充 分 满 足 了 我 的 好 奇 感 ！ ！ ！ ！,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1
非 常 好 ， 除 了 对 零 基 础 的 同 学 一 开 始 有 点 难 之 外 ， 作 业 和 考 试 设 计 部 分 也 不 错 。 知 道 自 己 学 到 多 少 东 西 。,O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
希 望 老 师 能 够 在 P y t h o n 1 2 3 提 供 软 件 安 装 、 第 三 方 库 安 装 等 常 见 的 问 题 和 解 决 办 法 图 解 并 不 断 更 新 非 专 业 零 基 础 的 学 员 在 处 理 此 类 问 题 上 需 要 花 费 大 量 的 时 间 和 精 力,O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 1 1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 1 1 -1 1 1 1 1 1 1 -1 -1 -1 -1 0 0 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0
通 俗 易 懂 ， 循 序 渐 进 ， 张 弛 有 度 ， 用 例 合 理 ！,O O O O O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 很 仔 细 很 认 真 很 到 位 ， 给 老 师 点 个 赞,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2
蛮 喜 欢 老 师 讲 课 的 风 格 ， 我 也 能 够 清 楚 明 白,O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 解 深 入 浅 出 ， 理 论 结 合 实 例 ， 相 得 益 彰 ， 能 让 我 更 好 的 了 解 。 当 然 要 充 分 的 掌 握 ， 还 需 要 加 强 实 践 应 用 。 可 谓 是 “ 师 傅 领 进 门 ， 修 行 靠 个 人 ”,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 ， 授 课 方 式 都 让 人 十 分 满 意 ， 很 喜 欢 这 门 课 ， 谢 谢,B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP O O O,2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1
"在 学 习 了 其 他 语 言 的 基 础 上 学 习 p y t h o n , 更 加 容 易 理 解 ， 感 受 到 了 这 门 语 言 的 魅 力 。",O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1
纯 小 白 入 门 有 点 难 度 ， 但 对 有 点 其 他 编 程 经 验 的 ， 是 非 常 好 的 入 门 及 提 升 课 程 ！ 不 但 有 系 统 性 ， 并 且 对 编 程 思 维 的 讲 解 非 常 到 位 ！ 是 门 优 秀 课 程 。,O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 分 段 可 以 再 改 进 一 下 ， 每 个 视 频 七 八 分 钟 左 右 比 较 好 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O,0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 安 排 还 是 很 不 错 的 。 第 一 个 周 开 始 ， 用 一 个 例 子 几 乎 把 P Y T H O N 所 有 的 基 础 知 识 和 语 法 讲 了 一 遍 ， 但 有 些 地 方 还 是 不 够 。 比 如 p r i n t 格 式 化 输 出 这 里 ， 老 师 就 是 叫 记 住 就 行 ， 如 果 不 详 细 讲 讲 ， 加 上 学 生 没 有 基 础 ， 估 计 到 五 六 周 ， 仍 然 是 懵 逼 的 。 第 二 呢 ， 有 些 实 例 不 是 很 适 合 初 学 者 ， 尤 其 是 需 要 安 装 第 三 方 库 的 实 例 。 比 如 w o r d c l o u d 那 个 实 例 ， 这 个 库 依 存 其 他 第 三 方 库 ， 包 括 n u m p y 和 m a t p l o t l i b 。 而 n u m p y 的 1 . 1 9 . 4 版 本 ， 在 W i n d o w s 平 台 是 运 行 不 了 的 ， 需 要 指 定 安 装 1 . 1 9 . 3 才 行 。 而 m a t p l o t l i b 也 依 存 很 多 其 他 的 第 三 方 库 ， 我 装 这 个 库 的 时 候 ， 折 腾 了 一 个 多 小 时 。 这 些 细 节 部 分 ， 对 于 初 学 者 来 说 ， 有 点 难 了 ， 尤 其 是 不 太 会 活 用 搜 索 引 擎 的 人 。 第 三 ， 我 个 人 觉 得 ， 如 果 今 后 还 要 开 班 ， 可 以 修 改 一 下 课 件 ， 增 加 一 些 基 础 的 内 容 。 比 如 p r i n t 函 数 的 格 式 化 输 出 ( 我 知 道 有 三 种 写 法 ) 、 推 导 式 、 l a m b d a 表 达 式 等 等 最 后 ， 总 的 来 说 ， 这 个 课 程 很 棒 ， 设 计 很 严 谨 。,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 0 0 0 0 0 0 0 0 0 0 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
该 课 程 适 合 初 学 者 学 习 。 由 浅 入 深 。,O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O O O,-1 2 2 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 真 的 很 棒 ， 课 程 节 奏 让 我 学 得 很 轻 松,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 不 过 马 上 要 结 束 了 ， 因 加 入 的 晚 ， 有 点 担 心 学 不 完 啊 ， 毕 竟 学 编 程 要 花 大 量 的 时 间 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1
"很 棒 讲 得 很 浅 , 注 重 实 例 , 很 适 合 新 手 入 门 .",O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1
平 时 接 触 j a v a 比 较 多 ， 这 课 程 学 起 来 也 挺 快 的,O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
学 会 了 p y t h o n 的 基 础 操 作 ， 更 深 层 次 的 了 解,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
国 家 精 品 课 程 确 实 不 一 般 ， 非 填 鸭 式 的 教 学 ， 五 星 五 星,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2
老 师 讲 课 讲 的 很 好 ， 课 程 设 置 合 理 ， 笔 记 记 了 很 多 ， 希 望 能 学 好 p y t h o n ！ ！ ！ ！,B-ASP I-ASP O B-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1
这 个 是 需 要 你 自 己 去 查 找 下 书 籍 ， 或 者 自 己 反 复 看 几 遍 做 做 练 习 才 能 理 解 的 。 并 非 小 白 文,O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
课 程 安 排 合 理 ， 老 师 讲 课 详 细 ， 还 有 配 套 练 习 ， 很 好 的 课 程 。,B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
对 小 白 来 说 ， 上 手 有 点 蒙 ， 尤 其 是 第 一 周 的 2 道 作 业 题 ， 感 觉 超 范 围 了 。,O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1
是 一 套 非 常 优 秀 的 课 程 ， 让 我 学 明 白 了 p y t h o n,O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
课 程 全 面 ， 条 理 清 晰 ， 让 人 又 不 断 往 下 学 的 欲 望,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
感 觉 都 是 直 接 讲 答 案 ， 并 没 有 在 入 门 花 多 少 心 思 ， 小 白 都 还 不 知 道 什 么 叫 做 算 法 ， 那 边 下 一 节 课 就 直 接 开 始 设 计 了 不 推 荐 菜 鸟 入 门,O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0
结 合 应 用 场 景 和 实 例 讲 解 语 法 ， 非 常 好 的 安 排,O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP,-1 -1 1 1 1 1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2
课 程 非 常 好 ， 从 简 单 到 进 阶 ， 容 易 上 手,B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
收 获 了 许 多 ， 认 知 深 度 进 一 步 加 深,B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
本 人 是 上 海 交 大 的 大 一 学 生 ： 这 门 课 简 直 太 太 太 好 了 !,O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP O O O O O O O O,-1 -1 -1 2 2 2 2 -1 1 1 1 1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1
很 喜 欢 举 一 反 三 这 个 环 节 的 内 容 ， 平 时 只 知 道 学 习 程 序 语 法 ， 不 会 思 考 除 了 老 师 的 教 导 的 内 容 ， 这 个 环 节 可 以 锻 炼 人 发 散 性 思 维 ， 真 的 太 棒 了 ！,O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 详 略 得 当 ， 而 且 非 常 生 动 易 于 理 解 ， 整 个 课 程 都 能 体 现 老 师 们 的 精 心 准 备 。 真 的 是 超 级 精 品 ！ ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1
大 一 上 学 期 的 时 候 学 的 ， 刚 开 始 看 觉 得 老 师 讲 得 不 错 ， 可 是 学 到 后 面 却 觉 得 ， 老 师 讲 的 太 简 单 了 ， 有 些 问 题 都 没 有 讲 解 ， 到 最 后 只 能 翻 开 课 本 自 己 研 究 。 主 要 我 这 还 是 为 了 应 付 一 个 学 期 的 期 末 考 来 着 ， 结 果 还 得 自 己 去 另 找 解 析,B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0
老 师 课 程 设 计 的 逻 辑 很 好 ， 讲 解 很 清 晰 ， 受 益 匪 浅,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O,1 1 2 2 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 基 础 ， 需 要 多 多 练 习 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
内 容 丰 富 ， 难 易 适 中 ， 强 调 计 算 生 态 ， 突 出 p y t h o n 特 色 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 2 2 2 2 -1
课 程 很 棒 ， 嵩 天 老 师 教 学 质 量 很 好,B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 1 1 1 1 2 2 2 2 -1 -1
讲 师 口 齿 清 晰 ， 慢 条 斯 理 ， 听 课 很 舒 服,B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 确 实 很 不 错 ， 内 容 知 识 点 真 的 分 析 的 特 别 详 细 了 ， 真 正 面 向 编 程 0 基 础 小 白 哈 哈 ， 个 人 非 常 喜 欢,B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
好 ！ 这 是 学 习 p y t h o n 最 好 的 课 程 。,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1
老 师 讲 得 很 n i c e ， 就 是 作 业 有 点 儿 超 纲,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
老 师 讲 的 太 有 水 平 了 这 是 我 听 到 的 最 好 p y t h o n 课,O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2
老 师 讲 的 太 好 了 ， 每 天 学 到 停 不 下 来,B-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 美 国 人 研 制 的 新 型 鸦 片 ！ 美 国 人 往 你 的 手 机 里 安 装 大 炮 ， 当 你 复 制 粘 贴 时 大 炮 就 会 被 引 燃 ， 真 是 细 思 极 恐 ！ 美 国 人 研 发 的 a i 编 码 机 器 人 会 自 动 生 成 复 制 粘 贴 ， 不 费 任 何 人 力 就 能 让 你 的 孩 子 上 瘾 。 现 在 的 孩 子 竟 然 复 制 粘 贴 可 见 美 国 人 已 经 毒 害 了 中 国 青 少 年 的 心 灵 ， 你 的 孩 子 已 经 失 去 了 原 创 能 力 ！ 复 制 粘 贴 多 有 暴 力 元 素 ， 引 导 人 走 向 暴 力 ， 残 害 家 人 和 朋 友 ！ 让 你 的 孩 子 有 自 残 倾 向 ！ 其 实 这 些 都 是 美 国 人 的 诡 计 ！ 如 果 现 在 的 青 少 年 打 的 都 是 这 种 东 西 ， 以 后 我 们 的 国 家 怎 么 会 有 栋 梁 之 材 ？ 坚 决 抵 制 复 制 ， 坚 决 抵 制 粘 贴 ！ 如 果 你 认 同 我 的 看 法 ， 请 转 发 出 去 ， 转 告 你 的 亲 友 ， 不 要 再 复 制 粘 贴 了 ， 抵 制 复 制 粘 贴 ！ ！ ！ ！,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 0 0 0 0 -1 0 0 0 -1 -1 -1 0 0 -1 -1 -1 0 0 -1 -1 -1 0 0 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 0 0 0 0 0 0 0 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 0 0 0 0 0 0 -1 -1 -1 -1
好 棒 像 我 这 种 有 一 点 基 础 的 自 学 很 容 易,O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1
教 会 了 我 很 多 p y t h o n 知 识,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2
学 到 了 好 多 很 有 趣 的 知 识 ， 想 要 一 直 学 下 去,O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
慕 名 来 观 看 这 门 课 ， 确 实 对 于 新 手 学 P y t h o n 很 有 帮 助 ， 老 师 讲 的 很 详 细 ！,O O O O O O O B-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
非 常 喜 欢 就 是 需 要 自 己 多 去 p y t h o n 1 2 3 . i o 上 面 做 题 不 然 知 识 点 很 容 易 遗 忘,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 1 1 -1 -1 0 0 0 -1 -1 -1 -1 -1
老 师 讲 得 很 好 ， 学 习 需 要 连 贯 ， 新 学 的 东 西 很 长 时 间 不 复 习 或 者 不 接 触 就 容 易 忘 。,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 条 理 框 架 清 晰 ， 内 容 丰 富 详 实 。 从 最 基 础 的 开 始 层 层 递 进 ， 对 新 手 菜 鸟 十 分 友 好 。 课 后 还 有 助 教 解 答 问 题 ， 能 帮 助 我 们 非 常 有 效 地 学 习 。 满 足 了 所 有 期 待 ， 感 谢 老 师 们 。,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O,2 2 2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1
老 师 的 课 程 真 的 很 详 细 ， 老 师 也 是 讲 的 非 常 好,B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
挺 不 错 的 ， 也 有 一 定 难 度 ， 非 常 感 谢 嵩 天 老 师 的 课 程 ！,O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1
非 常 详 细 、 实 用 ， 非 常 棒 ！ 大 爱 嵩 老 师 团 队 ！,O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1
教 会 了 我 如 何 去 学 习 P y t h o n,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
老 师 讲 的 很 棒 ， 一 点 小 建 议 就 是 ， 演 示 编 程 的 时 候 建 议 配 上 声 音 ， 就 怕 空 气 突 然 安 静,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"l e a r n t h i s f o r m a t h e m a t i c a l m o d e l i n g , d a s h !",O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 -1
老 师 的 授 课 方 式 很 新 颖 ， 讲 的 非 常 好 ！ 感 谢 嵩 老 师 ！,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O,1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
学 过 其 它 编 程 语 言 ， 听 这 个 课 程 很 顺 利 。,O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
课 程 时 间 短 ， 内 容 精 简 ， 老 师 讲 的 也 是 通 俗 易 懂 十 分 喜 欢 ！ ！,B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O,0 0 0 0 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 授 课 内 容 丰 富 ， 所 用 案 例 贴 切 生 活 实 际 简 单 明 了 ， 对 课 程 掌 握 帮 助 很 大 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
对 小 白 来 说 ， 有 些 题 讲 得 还 不 是 很 透 ， 希 望 可 以 讲 得 更 细 致 些,O O O O O O O O B-ASP O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
从 来 没 有 想 过 ， 我 会 在 这 里 学 到 了 知 识 。 老 师 讲 解 详 细 到 位 ， 此 外 还 提 供 了 交 流 群 一 起 交 流 ， 答 疑 释 惑 。 为 平 台 点 赞 ， 为 老 师 点 赞 ， 为 课 程 点 赞 ！,O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1
讲 解 很 详 细 ， 适 合 初 次 接 触 p y t h o n 的 同 学,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1
老 师 讲 课 很 详 细 也 讲 得 很 清 楚 ， 收 获 很 多,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
好 ， 特 别 好 ， 让 我 领 略 到 了 编 程 的 魅 力,O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2
有 些 函 数 不 了 解 ， 如 能 每 个 语 句 都 解 释 一 遍 更 好 ， 不 能 看 不 懂 某 句 意 思 ， 理 解 不 了 里 面 逻 辑,O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP O O O O O I-ASP I-ASP,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 0 -1 -1 -1 -1 -1 0 0
非 常 好 的 学 习 p y t h o n 的 课 程,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2
课 件 很 棒 ， 循 序 渐 进 ， 老 师 讲 得 也 很 好 ， 深 入 浅 出 ， 不 拖 泥 带 水,B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 授 方 式 很 新 颖 ， 从 例 子 切 入 厘 清 知 识 ， 对 小 白 很 友 好,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1
对 于 入 门 级 学 生 特 别 有 帮 助 ， 提 出 一 个 建 议 里 面 课 程 中 有 些 代 码 运 行 会 出 现 问 题 （ 如 词 云 f r o m i m a g e i o 改 为 f r o m i m a g e i o . v 3 ) ， 希 望 能 更 新 ， 总 体 是 很 优 秀 的 课 程 。,O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 0 0 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 课 很 好 ， 十 分 喜 欢 老 师 讲 课 风 格 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 2 2 -1
讲 的 很 好 ， 希 望 更 多 的 人 能 学 到 这 个 课 程 。,O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
嵩 老 师 的 课 非 常 条 理 ， 特 别 是 每 周 的 复 习 和 内 容 介 绍 ， 在 对 前 期 知 识 进 行 了 总 结 的 基 础 上 ， 引 入 新 的 内 容,B-ASP I-ASP I-ASP O B-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP,1 1 1 -1 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
嵩 老 师 讲 课 条 理 清 楚 、 言 简 意 赅 、 深 入 浅 出 、 循 序 渐 进 ， 课 程 内 容 也 组 织 得 非 常 紧 凑 ， 有 大 量 实 例 ， 实 在 是 P y t h o n 学 习 的 好 课 程 。,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2 -1
老 师 讲 得 特 别 好 ， 实 例 很 有 代 表 性,B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2
内 容 通 俗 易 懂 ， 建 议 多 一 些 更 常 用 实 用 的 案 例,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
很 有 帮 助 ， 要 是 有 份 P D F 讲 义 就 更 好 了 ， 不 用 听 课 还 兼 顾 做 笔 记,O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1
作 为 小 白 入 门 课 实 属 精 品 ， 学 习 该 课 程 能 够 对 p y t h o n 的 基 础 语 法 有 了 基 本 的 掌 握 。 希 望 老 师 添 加 关 于 c l a s s “ 类 ” 的 这 一 部 分 的 内 容 ， 发 现 课 程 缺 少 这 部 分 。,O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 1 1 1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
看 过 不 少 p y t h o n 教 程 ， 这 个 是 最 好 的 ， 老 师 对 p y t h o n 的 理 解 很 深 ， 能 够 把 细 碎 的 知 识 讲 得 系 统 ， 学 起 来 也 容 易 很 多 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
真 的 很 有 用 ， 从 入 门 学 起 ， 感 谢 老 师 和 m o o c,O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2
老 师 授 课 例 子 生 动 且 易 懂 ， 可 实 现 性 较 好 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
很 不 错 ， 跟 着 老 师 上 课 比 自 己 看 书 强 得 多 。,O O O O O O B-ASP I-ASP O B-ASP O O O O B-ASP O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 2 -1 -1 -1 -1 1 -1 -1 -1 -1
大 牛 老 师 ， 收 获 很 大 。 吾 国 之 幸 ！ ！,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 的 有 框 架 ， 不 一 定 特 别 全 ， 容 易 上 手,O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
打 破 了 传 统 课 程 按 部 就 班 填 鸭 式 教 学 ， 不 会 一 上 来 就 被 一 堆 需 要 记 忆 的 规 定 吓 退 。,O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 0 0 0 0 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
课 程 设 计 脉 络 清 晰 ， 老 师 授 课 内 容 有 逻 辑 有 关 联 ， 案 例 直 观 新 颖 ， 有 举 一 反 三 思 维 拓 展 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 1 1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
好 ， 就 是 自 己 能 看 懂 代 码 ， 但 是 敲 不 出 来 ， 害,O O O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 充 实 ， 老 师 讲 解 浅 显 易 懂 ！,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1
作 业 题 目 太 多 事 网 上 有 的 了 ， 建 议 多 弄 一 些 有 创 意 的 题 目 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,0 0 0 0 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
"很 好 , 对 爬 虫 有 了 初 步 的 了 解",O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 构 建 很 清 晰 ， 零 基 础 能 够 听 懂 ， 建 议 后 期 的 延 申 学 习 ， 以 及 运 用 领 域 等 可 以 多 介 绍 下 。,O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
我 很 喜 欢 老 师 的 教 授 风 格 ， 觉 得 老 师 是 以 一 种 更 加 接 近 我 们 的 水 平 的 方 式 来 讲 授 课 程,O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP,-1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1
很 受 启 发 也 学 到 很 多 干 货 ， 嵩 老 师 真 牛 ！,O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 2 -1 -1 -1
课 程 内 容 还 比 较 满 意 ， 不 过 对 于 操 作 符 等 编 码 符 号 多 一 些 具 体 的 解 释 就 更 好 了 ， 目 前 因 为 解 释 不 全 面 ， 有 的 会 不 太 理 解,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 专 业 、 系 统 真 正 的 单 纯 传 授 知 识 ， 没 有 网 课 那 些 反 复 的 广 告 谢 谢 老 师,O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 2 2
课 程 很 棒 ， 实 例 也 很 完 整 ， 具 有 很 强 的 教 学 性 与 应 用 性 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 -1
老 师 超 可 爱 ， 课 程 讲 的 很 详 细 ， 条 例 清 晰 。 好 评 ！ 好 评 ！ 好 评 ！,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1
该 课 程 形 式 将 各 个 语 法 部 分 系 统 化 的 进 行 讲 解 ， 作 为 听 众 的 我 ， 认 为 该 课 程 内 容 详 细 ， 例 题 清 晰 易 懂 ， 真 的 很 不 错,O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O,-1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
在 这 一 星 期 的 学 习 中 ， 感 觉 很 棒 ， 老 师 讲 解 的 很 好 ， 通 过 一 些 小 案 例 的 的 引 入 ， 让 P Y T H O N 课 程 简 单 易 懂 。 理 论 的 学 习 加 上 实 际 动 手 操 作 ， 完 成 练 习 ， 让 编 程 很 有 意 思 。,O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
希 望 可 以 讲 解 一 下 测 试 的 程 序 题 。 课 程 中 教 授 的 知 识 点 和 测 试 题 之 间 跨 度 有 些 大 ， 测 试 的 程 序 题 大 都 通 过 百 度 相 关 知 识 点 完 成 ， 也 有 不 太 懂 的 地 方 ， 学 到 之 后 的 下 一 个 单 元 也 没 有 讲 解 过 。,O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 0 0 -1 -1 -1 -1 0 0 0 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 的 课 真 的 有 让 编 程 小 白 体 会 到 编 程 的 快 乐 ！ ！ ！ ！ 而 且 我 很 喜 欢 边 讲 旁 边 放 着 简 洁 明 了 的 笔 记 ， 方 便 记 录 。 赞 ！ ！ ！ ！,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
挺 好 的 ， 通 俗 易 懂 ， 也 有 练 习 提 供 ， 学 起 来 就 忘 不 掉 了,O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 细 致 ， 课 件 准 备 充 足 ， 收 获 很 大 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1
真 的 很 好 ( • ̀ ω • ́ ) y,O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
作 为 一 名 零 基 础 学 员 ， 课 程 设 置 难 度 以 及 配 套 练 习 都 很 合 适 ， 期 望 后 面 的 收 获 更 大 。,O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 课 的 内 容 很 棒 很 清 晰 值 得 学 习,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O,1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 人 感 觉 是 还 特 别 好 的 ， 能 看 懂 代 码 和 理 解 ， 能 够 了 解 每 一 行 和 各 函 数 变 量 等 ， 是 特 别 不 错 的 一 门 课 。,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1
嵩 天 老 师 讲 课 节 奏 很 棒 ， 很 适 合 基 础 入 门,B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1
嵩 天 老 师 讲 课 实 属 厉 害 ， 可 惜 太 短 了,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 说 的 很 棒 ， 条 理 清 晰 ， 而 且 给 的 例 子 都 很 有 代 表 性 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O,1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
慕 名 而 来 ， 实 属 荣 幸 能 听 到 这 么 好 的 课 程 ， 一 定 好 好 学 P y t h o n 。,O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1
第 三 方 库 的 安 装 过 程 上 ， 安 装 过 程 老 师 讲 的 很 简 单 ， 但 实 际 安 装 过 程 中 我 遇 到 了 很 多 问 题 ， 一 开 始 安 装 不 上 ， 后 来 安 装 上 之 后 有 运 行 不 了 ， 总 之 如 果 可 以 的 话 ， 这 方 面 的 内 容 希 望 老 师 重 点 讲 一 下,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O,1 1 1 1 -1 1 1 1 1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1
根 本 就 不 是 从 零 开 始 的 课 ， 练 习 题 有 大 量 内 容 根 本 没 讲 过 ， 要 么 是 课 程 结 构 有 问 题 ， 要 么 是 我 不 配 学,O O O O O O O O O O B-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"课 程 内 容 非 常 详 细 , 老 师 讲 得 浅 显 易 懂",B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
谢 谢 你 们 的 工 作 ， 这 个 平 台 的 意 义 太 大 了,O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1
这 个 课 程 ， 特 别 用 心 的 老 师 ， 不 仅 课 程 内 容 特 别 棒 ， 用 心 也 体 现 在 讨 论 以 及 课 程 介 绍 文 案 方 面 ！ 赞 ！,O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 2 2 1 1 1 1 -1 -1 -1 -1 -1
课 程 深 入 浅 出 ， 讲 解 透 彻 。 如 果 能 按 时 完 成 课 后 练 习 ， 无 疑 对 P y t h o n 学 习 大 有 帮 助 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1
很 感 谢 这 门 课 程 ， 我 个 人 觉 得 不 错 。 课 程 教 授 只 是 引 入 门 ， 真 正 深 入 学 习 还 得 靠 自 己 多 实 践,O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 觉 上 课 讲 授 的 内 容 十 分 的 清 晰 易 懂,O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
课 程 不 错 ， 思 路 清 晰 ， 由 浅 入 深 ， 逻 辑 连 贯 ， 值 得 一 学 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
内 容 结 构 合 理 ， 授 课 思 路 清 晰 、 语 速 适 中,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O,2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1
课 程 内 容 非 常 合 适 小 白 入 门 ， 课 程 也 很 科 学 合 理 ， 不 错 不 错,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
棒 捏 ， 就 是 课 后 的 习 题 难 度 有 点 大 · · · ·,O O O O O B-ASP O O B-ASP I-ASP B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 1 -1 -1 1 1 0 0 -1 -1 -1 -1 -1 -1 -1
p y t h o n 是 一 门 很 精 彩 的 语 言 ， 吸 引 我 认 真 的 学 习 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
视 频 讲 解 简 单 明 了 ， 有 理 有 据 ， 但 视 频 被 分 割 成 了 很 多 小 视 频 ， 不 便 集 中 学 习 ， 另 外 ， 想 回 顾 某 以 短 视 频 内 容 时 ， 索 引 不 是 很 方 便,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
授 课 由 浅 入 深 ， 理 论 学 习 结 合 案 例 练 习 ， 让 我 对 P y t h o n 有 初 步 的 理 解,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1
讲 的 非 常 好 ， 零 基 础 开 始 学 P Y T H O N ， 刚 开 始 自 己 看 书 看 的 头 大 ， 后 来 朋 友 推 荐 嵩 老 师 的 讲 课 ， 果 然 厉 害 ， 老 师 能 把 知 识 点 逐 一 分 解 并 娓 娓 道 来 ， 就 像 是 讲 故 事 一 般 ， 引 人 入 胜 ， 感 谢 嵩 老 师 ！,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
很 好 。 课 程 可 以 基 础 ， 芯 片 方 面 内 容 还 要 更 新 一 点 ， 紧 跟 科 技 进 步 ， 满 足 我 国 人 才 发 展 需 求 。,O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1
希 望 每 次 都 更 新 题 目 ， 网 上 都 p o 出 答 案 了,O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 0 0 -1
理 论 与 实 践 结 合 很 紧 密 ， 学 有 所 得,B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP O O I-ASP,2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 -1 -1 2
课 程 非 常 好 ， 老 师 讲 的 很 生 动 ， 知 识 点 密 集,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1
北 理 工 的 几 位 老 师 的 教 学 质 量 真 的 没 话 说 ， 内 容 太 赞 了 ！ P y t h o n 语 言 和 实 例 结 合 的 教 学 方 式 通 俗 易 懂 ， 受 益 良 多 ！,B-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O,2 2 2 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1
嵩 老 师 的 p y t h o n 课 真 的 讲 的 特 别 好 通 俗 易 懂 并 且 超 乎 意 料 的 好,B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O,1 1 1 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 每 个 视 频 内 容 不 长 ， 学 起 来 不 容 易 疲 倦 ， 且 内 容 易 懂 ， 老 师 讲 得 很 好,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 的 都 是 干 货 。 非 常 好 ， 但 是 希 望 内 容 可 以 再 丰 富 一 些 。,O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 对 搭 建 自 己 的 知 识 框 架 很 有 帮 助 !,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
简 洁 明 了 ， 省 时 省 力 ， 真 正 能 学 到 知 识 ， 非 常 棒 ！,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
课 程 理 论 联 系 实 际 ， 能 激 发 学 习 兴 趣 。 嵩 天 老 师 的 讲 授 清 晰 明 了 ， 出 浅 入 深 ， 好 理 解 。,B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O,2 2 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 去 一 家 公 司 应 聘 ， 面 试 官 说 让 我 学 一 门 以 P 开 头 的 语 言 再 来 ， 于 是 我 学 了 P y t h o n ， 谁 知 再 去 ， 他 居 然 和 我 说 是 让 我 学 P u T o n g H u a ( ๑ • ̀ ㅂ • ́ ) و ✧,O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 入 门 ， 这 个 课 特 别 好 。 P P T 简 洁 系 统 又 直 观 ， 老 师 讲 课 不 是 念 P P T ， 很 好 听 进 去 并 且 引 发 思 考 。 小 实 例 多 样 性 ， 很 有 意 思 。,O O O O O O B-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
基 础 性 的 知 识 很 充 分 ， 值 得 花 时 间 学 习,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 2 2
老 师 讲 的 很 通 俗 易 懂 ， 很 实 用 的 一 门 课 程,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
很 好 的 课 程 ， 我 的 p y t h o n 入 门 课 。,O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O,-1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 2 2 2 -1
本 来 对 编 程 是 有 很 大 的 恐 惧 的 ， 但 在 嵩 天 老 师 的 带 领 下 ， 不 知 不 觉 就 克 服 了 内 心 的 恐 惧 。 这 个 课 程 真 的 是 p y t h o n 入 门 首 选 ， 深 入 浅 出 ， 让 你 不 经 意 间 就 领 悟 到 了 编 程 的 思 维 和 方 法 。,O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1
感 觉 不 错 ！ 希 望 能 表 达 自 己 的 思 想 。,O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
讲 的 很 好 ， 爱 了 爱 了 ( ⑉ ° з ° ) - ♡,O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 适 合 入 门 的 我 ， 老 师 讲 的 很 细 很 好 ！ ！ ！,O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 好 的 一 门 课 程 ， 是 和 我 这 种 初 学 者 入 门 。,O O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
做 练 习 很 重 要 ！ 做 练 习 很 重 要 ！ 做 练 习 很 重 要 ！ 重 要 的 事 说 三 遍 ！,O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 好 ， 但 是 有 些 题 我 不 会 就 不 能 及 时 得 到 解 决,O O O O O O O O B-ASP O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
虽 然 有 时 候 难 到 想 放 弃 ， 但 真 的 学 到 很 多 东 西 。,O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
非 常 有 收 获 ， 实 例 和 课 程 结 合 ， 方 便 理 解,O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O,-1 -1 -1 2 2 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
深 入 浅 出 ， 通 俗 易 懂 ， 给 嵩 老 师 点 赞 ！ ！ ！,O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1
真 的 牛 逼 ， 不 过 对 文 科 生 还 是 有 点 难 度 ， 尤 其 是 数 学 不 好 的 ， 不 过 相 当 于 一 个 b a s e s t o n e 吧,O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 -1
很 棒 的 课 程 ， 补 缺 了 以 前 自 己 学 习 的 一 些 不 足,O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0
课 程 知 识 结 构 清 晰 ， 老 师 讲 的 非 常 明 白 ， 好 评 。 只 是 自 己 时 间 安 排 的 不 合 理 ， 导 致 课 程 没 有 学 习 完 ， 遗 憾,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 0 0
感 谢 北 京 理 工 大 学 的 老 师 们 和 m o o c 平 台 让 我 有 一 个 学 习 的 机 会 ， 对 我 个 人 来 说 这 是 非 常 有 用 的 .,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 2 2 2 2 2 2 -1 2 2 2 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
没 有 发 现 慕 课 没 有 限 时 观 看 等 等 使 用 慕 课 平 台 的 说 明 ， 课 程 怎 么 更 新 等 等 细 节 。 对 于 初 次 接 触 平 台 的 学 生 来 说 比 较 麻 烦 ， 希 望 跟 进 。,O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 0 0 -1 1 1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 有 条 理 ， 结 构 框 架 很 详 细 。,B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1
初 次 接 触 一 开 始 学 习 起 来 有 点 困 难 ， 多 亏 了 群 里 的 给 予 了 我 帮 助 ， 很 是 感 谢,O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
很 棒 ， 知 识 点 很 全 面 ， 讲 解 很 透 彻 ！,O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O,-1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 的 十 分 详 细 ， 小 白 可 一 搞 懂,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
老 师 讲 解 的 清 晰 透 彻 ， 对 于 初 学 者 来 说 上 手 快 、 易 掌 握 ， 是 接 下 来 深 入 学 习 的 好 基 础 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
嵩 天 老 师 讲 的 课 非 常 棒 ， 举 的 例 子 也 比 较 用 心 ， 容 易 理 解 ， 非 常 照 顾 小 白 。,B-ASP I-ASP I-ASP I-ASP O O B-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O,1 1 1 1 -1 -1 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
对 于 编 程 小 白 来 说 ， 学 习 起 来 既 容 易 上 手 又 富 有 挑 战 ， 很 好 的 课 程,O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
嵩 老 师 讲 的 非 常 棒 ， 我 在 努 力 的 跟 上 进 度 ， 争 取 在 嵩 老 师 和 其 团 队 的 教 导 和 帮 助 下 ， 学 会 并 会 用 P y t h o n 编 程 。,B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1
思 路 很 清 晰 ， 很 好 理 解 。 唯 一 缺 点 就 i 是 有 些 时 候 的 个 别 行 代 码 不 好 理 解 ， 希 望 能 有 所 改 善,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
相 当 好 了 ！ ！ ！,O O O O O O O,-1 -1 -1 -1 -1 -1 -1
很 详 细 ， 讲 的 非 常 好 ， 思 路 清 晰 容 易 学 习,O O O O B-ASP O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
十 分 适 合 自 学 ， 入 门 p y t h o n . 但 是 关 于 第 三 方 库 的 安 装 介 绍 的 不 太 详 细 ， 有 时 候 用 p i p 安 装 不 成 功 我 就 会 去 别 的 地 方 找 原 因 ， 而 课 程 中 对 于 安 装 第 三 方 库 错 误 的 解 决 方 法 讲 的 不 是 很 详 细 ， 其 他 我 觉 得 还 好 ， 讲 的 很 透 彻 也 易 懂 ， 很 棒 。,O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 0 0 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 P y t h o n 有 了 初 步 的 了 解 ， 以 后 也 会 在 爬 虫 方 面 继 续 学 习,O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
能 获 得 很 多 基 础 的 知 识 ， 让 我 学 习 P Y T H O N 很 有 用 处,O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP,-1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 2 2
P y t h o n 真 是 一 门 有 着 无 限 可 能 的 学 科 ， 嵩 天 老 师 的 教 学 方 式 很 有 趣 ， 很 容 易 带 动 兴 趣 发 展 ， 主 要 靠 自 觉 性 还 有 爱 摸 索 的 性 格 来 学 习 ， 我 相 信 越 往 后 收 获 会 越 多,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
很 喜 欢 ， 同 时 也 很 痛 苦 ， 有 时 候 想 不 出 来 就 有 点 沮 丧 ， 但 是 想 出 来 了 就 很 开 心 ， 很 油 收 获,O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP,-1 2 2 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2
老 师 讲 课 很 全 面 细 致 ， 能 跟 紧 进 度 学 习 的 话 能 学 到 很 多,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 好 ， 起 码 我 这 个 本 科 毕 业 且 非 专 业 的 学 生 也 能 听 懂 。,O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1
这 个 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。,O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
G o o d ! T h a n k y o u v e r y m u c h ~,O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 适 合 入 门 ， 对 非 计 算 机 专 业 学 生 很 友 好 ！,O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
第 四 章 之 后 的 实 例 代 码 就 没 看 懂 过 ， 老 师 能 不 能 一 行 一 行 讲,O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
很 适 合 入 门 学 习 者 ， 通 俗 易 懂 ， 理 论 与 实 操 互 相 结 合 ， 对 学 习 者 有 莫 大 帮 助 。,O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1
老 师 讲 解 特 别 棒 ， 小 白 也 能 听 得 懂,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
让 我 学 会 了 很 多 很 棒 的 学 习 方 法 ， 慢 慢 的 养 成 了 一 些 良 好 的 学 习 习 惯 ， 收 获 了 一 些 感 兴 趣 的 知 识 。,O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
非 常 好 ， 从 p y t h o n 小 白 的 进 阶 之 路 开 启 了,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1
对 于 没 有 数 学 基 础 的 社 会 人 员 来 说 ， 凭 借 对 程 序 和 逻 辑 有 无 限 的 热 情 ， 跟 着 嵩 天 老 师 一 步 步 从 简 到 难 的 渐 入 佳 境 的 感 觉 很 n i c e ， 前 四 周 内 容 也 都 能 学 明 白 ， 但 第 五 周 的 函 数 学 习 ， 难 度 陡 然 增 加 ， 涉 及 一 些 理 解 困 难 的 数 学 知 识 和 相 对 复 杂 的 逻 辑 思 维 ， 着 实 有 很 强 挫 败 感 ， 不 禁 看 着 我 的 初 中 毕 业 证 陷 入 沉 思,O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 0 0 0 0 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 0 0 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1
老 师 讲 得 非 常 清 晰 ， 自 己 用 思 维 导 图 将 所 有 课 程 内 容 理 了 一 遍 之 后 ， 就 把 基 础 语 法 搞 明 白 了 。 多 写 代 码 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1
老 师 讲 的 很 好 ， 知 识 点 讲 的 很 清 晰 ， 全 程 满 满 干 货 ， 谢 谢 ！,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O,1 1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
嵩 老 师 的 课 很 精 彩 ， 值 得 推 荐 学 习 ！,B-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O,1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 讲 解 浅 显 易 懂 ， 使 用 的 实 例 也 很 有 应 用 价 值,B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2
老 师 的 知 识 点 很 细 致 ， 每 一 个 知 识 点 都 很 有 条 理 地 列 出 来 。 每 一 章 的 视 频 设 置 也 很 好 ， 每 一 个 视 频 都 是 针 对 一 个 知 识 小 点 所 讲 解 ， 当 那 个 知 识 点 还 有 不 会 的 时 候 ， 只 需 要 去 重 复 看 该 视 频 就 好 了 。 老 师 还 在 每 一 章 的 后 面 提 供 了 P P T 以 及 设 计 的 代 码 。 对 于 零 基 础 者 ， 这 门 课 程 真 的 超 级 友 好 ！ 老 师 的,B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O,1 1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1
老 师 教 学 很 生 动 ， 理 论 结 合 实 际 编 程 例 子 ， 让 学 生 更 容 易 理 解,B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 里 的 课 程 内 容 非 常 丰 富 ， 老 师 讲 解 得 很 清 晰 。 我 觉 得 超 级 好 ， 老 师 真 的 太 温 柔 了 ， 反 正 我 喜 欢 。,O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
第 一 次 接 触 p y t h o n ， 有 幸 接 触 了 该 课 程 ， 感 觉 很 不 错,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 解 非 常 耐 心 细 致 ， 可 以 看 出 准 备 的 非 常 用 心 ， 老 师 辛 苦 啦 ， 收 获 很 多 ， 感 谢 感 谢,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 的 授 课 思 路 清 晰 ， 案 例 典 型 ， 很 棒 ！ 老 师 的 授 课 思 路 清 晰 ， 案 例 典 型 ， 很 棒 ！,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O,1 1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
讲 的 非 常 好 ， 希 望 这 种 课 程 可 以 多 一 点 。,B-ASP O O O O O O O O O B-ASP I-ASP O O O O O O,2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
"文 化 水 平 有 限 , 一 句 牛 批 走 天 下",O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 有 所 获 ， 感 谢 老 师 ， 和 m o o c 平 台 ， 让 我 们 找 到 一 个 更 好 的 学 习 之 地 。,O O O B-ASP O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 2 -1 -1 -1 2 2 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
该 课 程 可 以 很 好 的 解 决 我 的 需 求 ， 老 师 也 很 耐 心 负 责 的 回 答 我 们 的 问 题,O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,-1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
课 程 内 容 全 面 ， 深 入 浅 出 ， 有 预 习 ， 有 分 析 ， 有 小 结 ， 步 骤 完 善 ， 已 初 步 浏 览 一 遍 ， 学 习 一 遍 ， 准 备 再 看 一 遍 把 作 业 做 了 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1
嵩 老 师 的 课 很 有 意 思 ， 就 是 不 知 道 为 啥 p y t h o n 1 2 3 后 期 的 编 程 题 都 没 有 了,B-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O,1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 0 0 0 -1 -1 -1 -1
老 师 讲 的 清 晰 易 懂 ， 课 程 安 排 科 学 有 序 ， 最 主 要 的 的 是 老 师 的 讲 述 太 好 了 ， 让 人 一 听 就 懂 ， 一 学 就 会 ， 非 常 感 谢 老 师 们 的 辛 勤 付 出 ！,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1
t e a c h e r i s g o o d,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
内 容 很 详 细 很 系 统 ， 讲 了 其 他 网 上 教 程 没 有 的 很 多 细 节 ， 受 益 匪 浅 ， 更 加 巩 固 基 础 了 ， 但 是 教 学 难 度 算 中 等 ， 如 果 我 不 是 学 过 一 边 基 础 ， 有 些 东 西 还 真 听 不 懂 ，,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"h e n h a o , h e n x i t o n g",O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 的 挺 好 的 但 后 面 有 点 跟 不 上 了,O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
真 的 不 愧 是 国 家 精 品 课 程 ， 课 程 设 计 、 内 容 等 等 都 很 不 错 ， 嵩 天 老 师 的 讲 解 真 好,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 -1 2 2 2 2 2 2 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1
这 门 课 程 制 作 精 良 ， 从 中 收 获 了 太 多 ， 感 谢 嵩 老 师 及 其 团 队 的 辛 勤 付 出 。,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1
暂 时 学 的 知 识 还 可 以 消 化 ， 运 用 方 面 还 有 欠 缺,O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1
课 程 时 间 分 割 太 细 ， 每 节 课 太 短 了,B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O,0 0 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1
很 好 ， 从 中 学 到 了 很 多 p y t h o n 知 识,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2
非 常 实 用 ， 内 容 跟 新 颖 ， 学 习 效 果 很 好 。,O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
作 为 一 个 完 全 没 有 计 算 机 基 础 的 人 ， 这 门 课 的 学 习 很 浅 显 易 懂 ， 老 师 对 于 一 些 基 本 概 念 和 思 维 方 法 的 讲 解 也 深 入 浅 出 ， 很 适 合 完 全 没 有 编 程 基 础 的 人 进 行 学 习 。,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 1 1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 很 细 致 ， 通 俗 易 懂 ， 让 对 P y t h o n 畏 难 的 我 有 了 信 心,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2
不 长 的 视 频 ， 但 能 学 到 很 多 。 课 堂 有 趣 生 动 ， 还 有 助 教 老 师 细 心 的 答 疑 ， 同 学 之 间 互 相 帮 助 ， 就 像 一 个 大 家 庭 一 样 。,O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 习 的 有 理 有 序 ， 然 后 老 师 教 的 清 楚 ， 不 明 白 可 以 重 播 一 遍 ， 然 后 老 师 教 的 比 较 全 ， 我 很 喜 欢,B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 详 细 ， 对 我 非 常 有 用,B-ASP I-ASP O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 的 通 俗 易 懂 ， 分 析 到 位 ， 只 要 认 真 听 都 能 学 会 ， 对 于 初 学 的 小 白 ， 一 遍 听 不 懂 的 多 听 几 遍 ， 根 据 讲 的 内 容 多 去 思 考 ， 多 去 分 析 ， 根 据 代 码 的 用 法 ， 分 析 案 例 ， 定 会 事 半 功 倍 ， 这 是 我 见 过 最 高 效 的 学 习 教 程 ， 学 习 时 间 短 ， 学 习 效 率 快,O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 0 0 0 0 -1 -1 2 2 2 2 -1
教 学 顺 序 和 内 容 对 入 门 者 都 非 常 友 好 ， 建 议 可 以 在 开 头 添 加 好 用 的 P y t h o n 调 试 环 境 介 绍 ， 这 样 在 后 面 课 程 的 编 程 过 程 中 ， 大 家 可 以 更 好 地 对 程 序 进 行 调 试 ~,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O,2 2 2 2 -1 2 2 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1
嵩 天 老 师 讲 解 的 知 识 体 系 很 详 细 ， 非 常 喜 欢 ！,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O,2 2 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 精 简 明 了 ， 老 师 讲 的 通 俗 易 懂 ， 非 常 好 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
前 面 程 序 细 节 不 再 讲 小 白 听 着 真 的 难 受 至 少 大 概 说 一 下 陌 生 代 码 的 用 途 吧,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 0 0 -1
通 过 这 次 课 程 我 学 习 到 了 p y t h o n 的 基 本 知 识 ， 老 师 讲 的 很 棒 ， 很 喜 欢 ！,O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP B-ASP O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 1 1 2 -1 -1 -1 -1 -1 -1 -1 -1
讲 解 的 通 俗 易 懂 ， 作 为 一 个 初 学 者 第 一 遍 能 学 个 入 门,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 2 2
老 师 课 讲 的 很 好 ， 作 为 一 个 初 学 者 ， 学 习 这 门 课 很 合 适 。 课 程 设 置 很 有 条 理 ， 循 序 渐 进 ， 能 够 把 每 个 知 识 点 都 讲 清 楚 ， 适 合 P Y T H O N 基 础 入 门 。 感 谢 老 师 带 来 这 么 好 的 课 程 ！,B-ASP I-ASP B-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 内 容 很 适 合 非 计 算 机 专 业 的 初 学 者 ， 内 容 设 计 巧 妙 ， 收 效 大 。,B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O,2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1
棒 ， 老 师 很 棒 ， 但 就 是 小 白 学 起 来 有 点 费 劲,O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
课 程 精 美 、 老 师 讲 解 清 晰 、 虽 然 我 还 没 看 完,B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 非 常 棒 ， 嵩 老 师 讲 课 讲 得 非 常 好,B-ASP I-ASP O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
老 师 讲 的 好 ， 现 在 还 在 学 不 懂 怎 么 应 用,B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
第 二 次 学 习 ， 对 于 我 这 个 非 计 算 机 专 业 的 学 生 来 说 ， 收 获 匪 浅,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
老 师 讲 的 非 常 好 ！ 我 听 的 很 舒 服 。 谢 谢 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 清 晰 易 懂 ， 真 是 一 门 好 课 啊 。,B-ASP I-ASP O O O O O O O O O O O O B-ASP O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1
层 次 感 清 晰 ， 从 入 门 到 难 度 升 级 梯 度 设 置 合 理 ！,B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O,2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
用 心 制 作 精 品 中 的 精 品 谢 谢 老 师 们 ！,O O B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O,-1 -1 2 2 2 2 -1 -1 2 2 -1 -1 2 2 2 -1
理 论 实 践 结 合 ， 精 品 课 程 ， 希 望 该 系 列 课 程 越 来 越 好 。,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1
老 师 讲 授 的 非 常 细 致 ， 这 段 时 间 的 学 习 我 获 益 良 多 ， 接 下 来 就 要 涉 及 生 物 信 息 学 了 ， 加 油 ！,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1
这 个 课 程 设 置 很 系 统 ， 案 例 都 很 有 趣 ， 老 师 讲 课 也 生 动 丰 富 ， 真 的 很 棒 。,O O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
听 了 嵩 老 师 的 课 ， 收 益 匪 浅 ， 虽 然 自 己 感 到 跟 不 上 节 奏 ， 但 是 自 己 有 信 心 加 倍 努 力 ， 迎 头 赶 上 ， 谢 谢 各 位 老 师 。,O O B-ASP I-ASP I-ASP O B-ASP O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,-1 -1 1 1 1 -1 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
学 了 这 么 久 ， 断 断 续 续 终 于 课 程 过 半 。 见 到 入 门 的 曙 光 了,O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1
老 师 讲 的 细 致 入 微 ， 教 学 方 式 容 易 让 人 沉 浸 其 中 ， 很 好 的 老 师 ， 很 好 的 课 程 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
超 级 好 啊 ， 现 在 在 准 备 计 算 机 二 级 考 试 啊,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1
老 师 的 讲 解 通 俗 易 懂 非 常 喜 欢,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
很 震 撼 呀 ， 谢 谢 老 师 ！ 我 辈 好 好 学 习 ， 天 天 向 上 。,O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
短 小 精 悍 的 课 程 ， 非 常 适 合 初 学 者,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1
简 单 易 懂 ， 而 且 一 下 子 就 可 以 抓 住 重 点 ， 真 的 是 宝 藏 课 程 呢,O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 -1
很 喜 欢 老 师 的 教 学 风 格 、 对 我 很 有 启 发 。 非 常 感 谢 老 师 的 辛 勤 教 学 。 老 师 从 实 例 出 发 到 逐 一 的 基 础 语 法 解 析 ， 很 有 逻 辑 性 。,O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O,-1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 2 2 2 -1
老 师 讲 的 很 细 致 ， 很 有 思 想 ， 就 是 自 己 学 习 的 时 候 感 觉 还 有 有 些 知 识 点 总 有 种 似 懂 非 懂 ， 简 单 的 代 码 看 得 懂 ， 但 是 自 己 就 是 编 写 不 出 来 ， 感 觉 是 还 没 有 编 程 思 维 ， 还 需 要 勤 加 练 习 ， 多 多 实 践 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"T h e t e a c h e r i s v e r y g o o d a t t e a c h i n g , w h i c h m a k e s m e v e r y b e l i e v e i n h i m . I n a w o r d , i l i k e t h e t e a c h e r .",O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 -1
很 容 易 理 解 ， 有 实 例 搭 配 ， 更 能 深 入 学 习,O O O B-ASP I-ASP O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 2 2 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2
简 单 直 接 ， 非 常 容 易 理 解 ， 适 合 有 一 定 自 学 能 力 的 小 白 。,O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 -1
内 容 挺 多 的 ， 是 P y t h o n 入 门 的 好 选 择,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 2 2
该 课 程 对 我 的 帮 助 很 大 ， 尤 其 是 线 上 线 下 的 结 合,O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP,-1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2
希 望 老 师 暂 时 别 关 闭 课 程 还 没 学 完,O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O,-1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
讲 得 很 好 ， 内 容 重 点 突 出 ， 条 理 清 楚 ， 谢 谢 老 师 ！,B-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
课 程 视 频 录 得 很 精 致 ， 老 师 讲 课 很 精 彩 。,B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1
言 简 意 赅 ， 通 俗 易 懂 ， 老 师 的 教 学 没 有 想 象 中 的 这 么 沉 闷 。,O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
我 已 经 学 会 一 门 编 程 语 言 了 ， 最 大 的 感 受 是 眼 睛 快 废 了 ， 建 议 开 课 时 先 教 会 小 白 同 学 把 代 码 字 体 的 大 小 调 整 到 较 大 。 还 有 P P T 的 背 景 色 都 改 成 黑 色 ， 绿 色 ， 灰 色 等 ， 光 感 弱 一 些 。,O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 1 -1 -1 0 0 -1 -1 -1 -1
很 棒 ， 但 因 为 工 作 的 原 因 第 一 次 没 能 持 续 学 下 去 ， 现 在 学 第 二 遍,O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
名 校 老 师 感 觉 就 是 不 一 样 ， 全 是 干 货 没 有 废 话 ， 讲 的 也 很 系 统,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 0 0 -1 -1 -1 -1 -1 2 2
老 师 讲 得 浅 显 易 懂 ， 比 别 的 课 程 容 易 坚 持,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 课 细 致 认 真 特 别 n i c e 推 荐,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
绝 对 让 我 这 个 小 白 有 兴 趣 和 信 心 ， 感 谢 。 只 是 我 作 为 一 个 地 理 教 师 ， 两 个 小 孩 子 的 妈 妈 ， 确 实 需 要 挤 更 多 时 间 ， 跟 上 学 习 ！,O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 1 1 -1
还 不 错 ， 课 程 很 全 ， 老 师 讲 的 很 不 错,O O O O B-ASP I-ASP O O O B-ASP I-ASP B-ASP O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 1 1 2 -1 -1 -1 -1
老 师 讲 解 的 十 分 不 错 ， 由 浅 入 深 ， 适 合 初 学 者 学 习 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
很 不 错 的 ， 非 常 好 的 学 习 资 源 和 平 台,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2
学 了 第 一 周 的 课 程 ， 感 觉 作 为 编 程 小 白 的 我 听 的 还 是 很 清 晰 的 ，,O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 仔 细 ， 通 过 具 体 方 法 的 解 释 ， 自 己 很 好 理 解 。,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 感 受 很 深 ， 对 今 后 的 工 作 和 学 习 有 较 大 帮 助,O O B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 2 2
我 在 逐 渐 的 领 悟 敲 击 键 盘 后 的 哒 哒 哒 声 中 ， 屏 幕 跳 跃 出 来 的 字 符 的 美 妙 。 既 有 听 觉 上 的 享 受 ， 也 有 视 觉 的 冲 击 。 更 有 程 序 运 行 后 正 确 结 果 的 喜 悦 ！ ！ ！ 我 喜 欢 哒 哒 的 敲 击 键 盘 的 声 音 ！ ！ ！ ！,O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1
内 容 由 浅 入 深 ， 丰 富 而 不 枯 燥 ， 效 果 很 好,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
课 程 内 容 组 织 得 非 常 好 ， 老 师 集 颜 值 和 才 华 于 一 身 ， 上 课 很 有 逻 辑 ， 娓 娓 道 来 ， 难 得 的 好 课,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP O O B-ASP I-ASP O O O O O O O O O O B-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2
老 师 思 路 很 清 晰 ， 跟 着 老 师 学 起 来 很 轻 松 入 门 P Y T H O N,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,1 1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1
老 师 讲 解 得 很 耐 心 很 清 晰 ， 知 识 点 很 详 细 ， 例 子 也 很 适 合 ， 绝 对 的 国 家 精 品 课 程 ！,B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1
老 是 讲 得 很 细 致 ， 很 通 俗 让 我 们 小 白 学 起 来 很 有 信 心 。 感 谢 平 台 ， 感 谢 老 是 的 辛 勤 付 出,O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2
很 好 的 一 门 课 ， 简 单 易 懂 ， 丰 富 多 样,O O O O O B-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 专 业 而 又 不 失 实 战 ， 很 喜 欢 ！,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
非 常 好 ， 给 非 计 算 机 专 业 的 同 学 以 学 习 机 会 ， 提 高 工 作 和 科 研 效 率,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 1 1 -1 2 2 2 2 -1 -1 -1 2 2 -1 2 2 2 2
M O O C 平 台 很 优 良 ， 老 师 的 课 也 很 精 良,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP O O O O,2 2 2 2 2 2 -1 -1 -1 -1 1 1 -1 2 -1 -1 -1 -1
课 程 内 容 丰 富 ， 老 师 素 质 很 高 ， 教 学 内 容 有 趣 ， 作 业 完 整 高 效,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 2 2 2 2 -1 2 2 2 2 2 2 -1 2 2 2 2 2 2 -1 2 2 2 2 2 2
老 师 讲 的 很 详 细 ， ， 非 常 不 错 的 课 程,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
以 实 例 来 讲 解 ， 更 让 人 容 易 接 受 ， 记 住 ！,O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O,-1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 讲 述 得 很 详 细 ， 配 套 练 习 和 作 业 也 很 有 针 对 性 ！,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 2 2 2 -1
结 合 实 践 的 学 习 过 程 肯 定 是 最 实 用 的 。 紧 跟 队 伍 ， 坚 持 到 底 。,O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O,-1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 但 跟 上 练 习 和 考 试 还 不 了 解 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1
课 程 从 易 到 难 ， 示 例 很 周 到 ， 练 习 丰 富 ， 有 测 验 和 考 虑 更 能 督 促 学 习 。 学 程 序 就 是 要 多 练 习 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 -1
很 好 ， 特 别 是 对 有 基 础 的 老 程 序 员,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1
老 师 的 讲 解 逻 辑 清 楚 层 次 分 明 晰 ， 案 例 设 置 恰 到 好 处 ， 一 下 激 起 了 学 习 的 兴 趣 ， 对 于 我 这 中 小 白 来 说 特 备 友 好,B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O,1 1 -1 2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
学 习 体 验 很 好 ， 还 需 要 多 练 习 巩 固 学 习 成 果,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 2 2
特 别 棒 ！ 老 师 讲 的 很 清 晰 ， 听 完 之 后 我 都 膨 胀 了 ， 觉 得 大 厂 在 向 我 招 手 ！,O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
讲 解 深 入 浅 出 ， 内 容 贴 近 实 际 应 用,B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 2
课 程 结 构 完 善 ， 是 p y t h o n 入 门 非 常 好 的 选 择 ， 不 管 是 作 图 还 是 计 算 都 打 下 很 好 的 基 础 。,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1
课 程 安 排 不 错 ， 跟 着 学 算 是 p y t h o n 入 门 了,B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1
本 来 打 算 随 便 看 看 就 走 ， 结 果 这 不 快 学 完 了 。 （ 这 课 真 上 瘾 ）,O O O O O O O O O O O O O O O O O O O O O O B-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1
对 小 白 非 常 友 好 ， 跟 学 了 其 他 课 程 ， 看 这 个 才 觉 得 有 跟 下 去 的 欲 望 。,O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,-1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
视 频 怎 么 看 不 到 ， 哪 里 可 以 下 载 ？,B-ASP I-ASP O O O O O O O O O O O O O,0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
买 过 《 p y t h o n 编 程 从 入 门 到 实 践 》 ， 在 自 学 过 程 中 觉 得 每 个 函 数 或 是 方 法 也 并 没 有 那 么 难 ， 但 在 操 作 应 用 时 就 迷 茫 ， 不 知 道 怎 么 把 这 些 内 容 组 合 起 来 使 用 ， 也 不 知 道 编 程 思 路 。 感 谢 设 计 这 课 程 的 老 师 们 ， 短 短 的 9 周 课 程 ， 涵 盖 绝 大 部 分 知 识 点 ， 让 我 对 知 p y t h o n 有 个 轮 廓 型 的 认 知 ， 一 个 简 单 的 例 子 老 师 先 分 析 问 题 再 讲 编 程 思 路 最 后 编 程 展 示 ， 力 求 将 编 程 思 路 灌 输 给 学 员 ， 当 然 学 这 个 课 程 只 是 开 始 ， 还 有 很 多 细 小 的 知 识 点 老 师 并 没 有 涵 盖 到 ， 但 这 不 影 响 培 养 我 对 p y t h o n 的 热 情 ， 相 反 ， 因 为 学 了 这 个 课 程 ， 在 练 习 时 我 带 问 题 搜 索 p y t h o n 相 关 知 识 时 反 而 更 加 关 注 ， 自 测 也 感 觉 更 扎 实 。 感 谢 老 师 们 对 这 个 课 程 的 巨 大 付 出 ， 课 程 虽 简 单 ， 但 要 同 时 做 到 趣 味 性 ， 丰 富 性 ， 逻 辑 性 ， 流 畅 性 ， 全 面 性 真 的 很 难 ， 非 常 感 谢 ！ 有 个 小 建 议 ： 老 师 ， 那 练 习 题 后 的 讲 解 能 不 能 详 细 点 呀 ！ 比 如 关 于 第 三 章 数 据 的 三 角 形 星 号 的 练 习 ， 这 答 案 有 点 看 不 懂 ， 麻 烦 老 师 对 于 练 习 编 程 题 的 部 分 再 费 点 心 ， 给 个 详 细 讲 解 ， 谢 谢,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 -1 2 2 2 -1 2 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
非 常 好 的 一 门 p y t h o n 语 言 课 程,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2
这 个 课 程 内 容 连 贯 性 较 好 ， 但 是 个 人 认 为 ， 在 整 体 学 习 之 后 再 完 成 练 习 ， 会 比 较 好 ； 在 听 讲 一 课 后 直 接 联 系 ， 对 于 新 手 而 言 还 是 有 难 度 的 。,O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O,-1 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 0 0 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
作 为 一 个 非 计 算 机 专 业 ， 通 过 这 门 课 程 认 识 了 编 程 ， 也 是 基 础 编 程 入 门 ， 还 需 要 多 加 练 习 写 代 码 和 有 针 对 性 的 学 习 ， 希 望 对 自 己 的 工 作 有 帮 助 ！,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1
老 师 的 讲 课 很 细 致 ， 哪 怕 以 前 从 来 没 学 过 的 同 学 都 可 以 听 懂 ， 而 且 拓 展 面 很 广,B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1
老 师 把 知 识 点 讲 得 清 新 好 理 解 ， 作 为 编 程 小 白 ， 没 感 觉 觉 得 挺 容 易 的,B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 就 是 有 些 地 方 会 和 老 师 的 案 例 代 码 一 样 ， 结 果 不 一 样,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 1 1 -1 0 0 0 0 -1 -1 -1 0 0 -1 -1 -1
这 个 课 程 设 计 科 学 清 晰 ， 讲 解 通 俗 易 懂 ， 在 理 论 与 实 践 两 方 面 引 导 学 生 ， 课 后 练 习 测 试 有 针 对 性 、 适 时 ‘ 悄 悄 ’ 引 导 学 生 复 习 ， 线 上 辅 导 及 时 有 效 回 答 用 心 ， 不 愧 为 ‘ 国 家 精 品 课 程 ’ 。 感 谢 老 师 和 同 学 们 的 帮 助 ， 受 益 匪 浅 。,O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 2 2 -1 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1
课 程 很 棒 ， 老 师 讲 授 的 知 识 点 很 棒 ， 但 是 个 人 认 为 某 些 视 频 片 段 有 些 短 ， 总 是 切 换 会 不 会 干 扰 观 看 质 量 和 降 低 集 中 力 呢 ？,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 0 0 0 -1 -1
老 师 讲 得 很 详 细 又 很 容 易 理 解 ， 希 望 还 有 进 阶 版 的 哦 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
嵩 老 师 讲 的 非 常 好 ， 由 浅 入 深 ， 我 一 个 p y t h o n 小 白 ， 用 一 个 多 月 抽 出 晚 上 和 周 末 的 时 间 ， 坚 持 学 下 来 了 ， 基 本 掌 握 了 p y t h o n 的 基 础 语 法 、 数 据 类 型 、 文 件 读 取 、 简 单 画 图 、 逻 辑 循 环 、 第 三 方 库 的 安 装 等 内 容 ， 虽 然 离 自 主 编 程 解 决 科 研 问 题 还 有 一 定 距 离 ， 但 是 我 会 继 续 保 持 学 习 ， 持 续 关 注 嵩 老 师 的 相 关 系 列 课 程 ， 更 加 深 入 学 习 和 应 用 p y t h o n 解 决 实 际 问 题 。 老 师 说 思 想 比 编 程 更 重 要 ， 编 程 只 是 工 具 ， 怎 么 解 决 问 题 才 是 王 道 ， 也 希 望 通 过 不 断 地 学 习 ， 学 会 去 分 析 、 拆 解 问 题 ， 并 且 让 计 算 机 帮 助 实 现 问 题 地 解 决 ， 大 家 一 起 加 油 。,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 1 1 1 1 -1 1 1 -1 2 2 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
人 工 智 能 发 展 太 快 ， 从 事 的 主 业 又 日 薄 西 山 ， 只 能 尝 试 着 学 学 新 知 识 ， 看 看 以 后 失 业 了 能 不 能 再 就 业,B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP,1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 2 2
真 的 很 好 不 用 花 钱 主 要 是 ！ ！ ！,O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 节 奏 非 常 好 ， 老 师 专 业 ， 知 识 易 懂 ， 十 分 清 楚,B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O,1 1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
G o o d ! G o o d ! G o o d !,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 2 2 2 2 -1 2 2 2 2 -1
课 程 深 入 浅 出 ， 加 上 课 程 练 习 ， 效 果 不 错 ！,B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 1 1 -1 2 2 -1 -1 -1
很 棒 ， 没 有 那 种 机 器 人 练 p p t 的 感 觉 ， 很 棒,O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 0 0 0 -1 0 0 0 -1 -1 -1 -1 -1 -1
虽 然 有 点 蒙 ， 自 己 自 认 没 有 好 好 学 ， 但 我 不 可 否 认 课 程 的 详 尽 。,O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
课 程 设 计 层 次 分 明 ， 逻 辑 清 晰 。 我 觉 得 很 好 ！,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 于 我 来 说 ， 内 容 属 于 蹦 一 蹦 能 得 到 的 ， 还 不 错 。 授 课 方 式 也 适 合 我 这 样 的 上 班 族 ， 有 时 间 了 就 来 学 习 一 下 。 非 常 好 。,O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"讲 的 很 好 , 很 全 面 , 非 常 适 合 我 这 种 小 白 学 习 , 就 是 节 奏 快 了 一 点",O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1
嵩 天 老 师 讲 的 特 别 好 ， 特 别 喜 欢 ， 好 想 在 现 实 中 被 老 师 教,B-ASP I-ASP I-ASP I-ASP B-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
老 师 讲 得 很 好 ， 第 一 次 课 程 我 没 有 坚 持 下 去 ， 这 次 坚 持 听 下 去 了 ， 感 觉 很 棒 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 用 心 ， 跟 着 学 就 能 掌 握 大 部 分,B-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 思 维 清 晰 ， 知 识 很 系 统 ， 讲 的 也 很 有 耐 心 ， 非 常 适 合 零 基 础 。,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1
g o o o o o o o o o o o o o o o d,O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 细 致 清 晰 专 业 实 用,B-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 的 讲 解 深 入 浅 出 ， 逻 辑 清 晰 ， 非 常 好 懂 ， 受 益 匪 浅 。 谢 谢 嵩 天 老 师 ， 还 有 中 国 大 学 这 样 免 费 的 学 习 平 台 ， 给 你 们 点 赞 ！,B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 细 致 ， 零 基 础 也 能 跟 得 上 ， 理 解 得 了 ， 谢 谢 老 师 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
这 个 课 程 学 起 来 让 人 感 觉 很 顺 畅 ， 只 要 努 力 一 点 就 能 学 会 ， 只 是 课 程 都 结 束 这 么 久 了 ， 我 的 电 子 证 书 也 没 收 到 ， 也 不 知 道 什 么 原 因 。,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 非 常 好 ， 深 入 浅 出 ， 非 常 值 得 学 习 ！,B-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
通 过 边 看 对 应 的 教 材 边 学 习 ， 掌 握 了 基 本 的 知 识 ， 视 频 课 程 还 是 很 不 错 了 。 非 常 感 谢 嵩 天 老 师 及 团 队 ！,O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1
课 程 融 汇 知 识 性 与 趣 味 性 ， 在 不 知 不 觉 中 就 能 将 知 识 融 会 贯 通 ， 连 完 全 没 有 接 触 过 编 程 的 我 ， 都 深 深 爱 上 了 编 程 ， 并 将 它 与 自 己 的 日 常 生 活 牵 连 到 了 一 起 ！ A n y w a y s ， 课 程 真 的 是 棒 到 无 与 伦 比 ！,B-ASP I-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 2 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 喜 欢 嵩 天 老 师 讲 的 课 ， 讲 得 细 好 理 解,O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP O O O O O O O,-1 -1 -1 1 1 1 1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1
这 课 程 通 俗 易 懂 ， 结 合 各 种 案 例 ， 并 举 一 反 三 能 够 以 极 大 的 兴 趣 对 各 知 识 点 快 速 掌 握 并 应 用 ~,O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O,-1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 思 路 清 晰 ， 逻 辑 缜 密 ， 讲 的 通 俗 易 懂 ， 贴 别 好,B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 严 谨 ， 感 谢 各 位 老 师 以 及 后 台 人 员 的 付 出 。 ღ ( ´ ･ ᴗ ･ ` ) 比 心,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
十 分 有 条 理 ， 对 p y t h o n 的 整 体 有 了 较 为 清 晰 的 认 识 ， 感 谢 老 师 。,O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 2 2 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
讲 的 认 真 详 尽 ， 新 手 也 能 听 得 懂 ， 真 的 很 棒,B-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O,2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
作 为 一 个 工 科 生 ， 我 学 科 能 接 触 的 估 计 也 就 是 C 语 言 了 ， 但 我 对 计 算 机 有 点 感 兴 趣 ， 听 说 P y t h o n 比 较 火 ， 就 选 择 这 门 课 了 ， 老 师 讲 课 也 比 较 生 动 形 象 ， 给 老 师 个 大 赞 ， 哈 哈,O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 非 常 棒 ， 老 师 授 课 有 水 平 有 耐 心,B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP,2 2 -1 -1 -1 -1 1 1 2 2 -1 2 2 -1 2 2
课 程 很 详 细 ， 案 例 也 很 实 用 ， 很 不 错 ！ ！ ！,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 是 我 需 要 的 内 容 ， 我 只 是 需 要 大 概 了 解 下 P Y T H O N 以 及 产 品 设 计 就 好 ， 该 课 程 能 让 非 专 业 学 生 大 概 了 解 编 程 情 况 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1
"课 程 内 容 很 丰 富 , 老 师 讲 解 非 常 清 晰",B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1
非 常 的 好 ， 深 入 浅 出 ， 通 俗 易 懂 。,O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 上 课 讲 解 细 腻 ， 消 化 起 来 很 容 易,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 形 象 ， 习 题 有 利 于 帮 助 实 践 ！,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 的 非 常 清 晰 ， 真 是 好 老 师 ！ ！ ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
非 常 喜 欢 老 师 的 讲 课 方 式 ， 简 单 易 懂 ， 对 P y t h o n 产 生 了 兴 趣,O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1
内 容 详 实 ， 课 里 有 代 表 性 ， 老 师 棒 棒 的,B-ASP I-ASP O O O B-ASP O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O,2 2 -1 -1 -1 2 -1 -1 2 2 2 -1 2 2 -1 -1 -1
嵩 天 老 师 讲 的 很 扎 实 ， 体 系 扎 实 ， 学 的 很 愉 快 ， 很 好 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 授 课 思 路 清 晰 ， 案 例 详 实 ， 课 程 安 排 节 奏 n i c e,B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O,2 2 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 2 2 -1 -1 -1 -1
内 容 很 好 ， 很 清 晰 明 了 ， 也 很 全 面 ， 视 频 对 于 小 白 有 点 不 友 好 ， 但 是 如 果 严 格 按 照 先 下 载 课 件 预 习 ， 然 后 再 来 听 老 师 讲 课 ， 最 后 独 立 自 主 完 成 作 业 ， 完 成 作 业 以 后 对 照 参 考 答 案 ， 分 析 差 距 和 思 维 方 式 ， 这 样 就 完 美 了 ， 总 之 课 程 质 量 非 常 好 ， 学 习 的 方 法 就 是 靠 个 人 了,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 细 节 、 有 层 次 、 有 深 度 、 有 思 考 。,O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP O,-1 2 2 -1 -1 2 2 -1 -1 2 2 -1 -1 2 2 -1
老 师 讲 解 的 很 详 细 ， 学 到 的 也 很 多 ， 课 程 设 置 合 理,B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O,2 2 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1
太 棒 了 ！ “ ” ）,O O O O O O O,-1 -1 -1 -1 -1 -1 -1
这 个 课 程 里 嵩 天 老 师 讲 的 真 的 非 常 详 细 ! ! ! ! ! !,O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 非 常 好 ， 适 合 快 速 上 手 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 好 的 课 程 ， 讲 解 很 细 致 ， 内 容 通 俗 易 懂,O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 课 严 谨 ， 有 实 例 讲 解 ， 实 例 非 常 有 助 于 学 习 。,B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O,1 1 2 2 -1 -1 -1 -1 1 1 2 2 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
老 师 好 ， 可 以 把 一 个 章 节 的 课 放 在 一 起 么 ， 分 成 这 么 多 段 看 的 话 很 浪 费 时 间 ， 而 且 思 路 容 易 断 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 0 0 -1 -1 -1 -1
老 师 讲 课 非 常 好 ， 先 铺 垫 基 本 语 法 知 识 ， 然 后 利 用 具 体 程 序 案 例 进 行 引 导 ， 步 步 深 入 ， 效 果 很 好,B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
课 程 实 用 易 懂 ， 既 不 特 别 强 的 学 术 性 ， 也 不 太 浅 。 语 法 和 实 操 相 结 合 ， 能 够 更 好 掌 握 知 识 点 。,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
本 课 程 设 计 简 明 ， 讲 解 浅 显 易 懂 。,O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O,-1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1
我 觉 得 老 师 讲 得 很 详 细 ， 思 路 十 分 清 晰,O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
分 类 清 晰 ， 结 合 课 本 ， 讲 解 清 晰 ， 举 例 恰 当,B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1
自 己 的 学 习 状 态 ： 学 习 了 几 门 课 程 ， 都 没 学 完 ， 主 要 是 自 己 的 懒 惰 。 课 程 内 容 ： 比 如 p y t h o n ， 嵩 天 等 老 师 讲 的 ， 我 感 觉 是 简 明 易 懂 ， 点 出 了 很 多 知 识 点 ， 还 有 很 多 实 例 有 趣 又 实 际 ， 很 赞 。 还 有 计 算 机 网 络 ， 钱 燕 等 老 师 讲 的 ， 虽 然 知 识 点 是 几 年 前 的 ， 但 是 也 让 我 了 解 了 网 络 中 的 云 计 算 ， 大 数 据 的 概 念 。 还 有 两 门 课 程 ， 但 是 学 的 很 少 ， 都 获 取 到 了 不 少 知 识 ， 改 变 了 我 的 一 些 认 知 。 总 体 是 自 己 学 得 太 少 了 ， 想 要 继 续 学 习 。 最 后 非 常 感 谢 老 师 们 的 悉 心 备 课 与 传 授 ， 也 感 谢 慕 课 平 台 给 了 我 一 个 学 习 的 机 会 ， 打 开 了 另 一 片 天 。 谢 谢 ！,O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"嵩 老 师 讲 得 非 常 认 真 ， 每 一 节 课 都 非 常 有 趣 ， 我 好 喜 欢 ， 感 谢 老 师 辛 勤 付 出 。 f o r i i n r a n g e ( 5 2 0 ) : p r i n t ( ' T h a n k y o u , M r . s o n g ! ^ _ ^ ' )",B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
课 程 做 的 很 基 础 ， 很 适 合 初 学 者 ！ 感 谢 老 师 ！,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1
还 是 蛮 不 错 的 ， 是 语 法 和 实 例 的 结 合 ， 适 合 p y t h o n 入 门,O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 1 1 1 1 1 1 2 2
老 师 讲 解 的 非 常 好 ， 逻 辑 清 晰 内 容 详 实 老 师 讲 解 的 非 常 好 ， 逻 辑 清 晰 内 容 详,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1
老 师 很 棒 ， 课 程 讲 的 也 很 好 ， 很 细 致 。,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
我 爱 嵩 天 老 师 ! 拜 读 了 您 编 写 的 教 材 ， 十 分 钦 佩 ! 祝 我 明 天 P y t h o n 考 试 考 好 ， 嘿 嘿,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1
同 学 推 荐 的 课 程 ， 听 后 感 觉 非 常 棒 ！,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O,1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 实 用 ， ， 讲 解 很 透 彻 ， 很 容 易 理 解 。 赞,O O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 很 多 自 己 不 懂 的 小 技 巧 ， 很 高 兴 有 老 师 传 授,O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1
不 仅 有 理 论 说 辞 ， 还 有 实 操 示 范 。 棒 ！,O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 1 1 1 1 -1 -1 -1 2 2 2 2 -1 -1 -1
很 基 础 ， 老 师 讲 得 很 详 细 ， 合 适 入 门,O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 2 2 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
课 程 内 容 组 织 清 晰 ， 作 为 一 个 小 白 ， 学 起 来 也 没 问 题 ， 有 挑 战 ， 也 不 会 过 难 。 为 老 师 及 幕 后 团 队 点 赞,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1
""" p y t h o n 语 言 程 序 设 计 "" ) p r i n t ( "" v e r y g o o d "" )",O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O,-1 2 2 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
暂 时 学 的 知 识 还 可 以 消 化 ， 运 用 方 面 还 有 欠 缺,O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1
非 常 棒 ， 由 浅 入 深 ， 层 层 递 进 ， 讲 解 的 非 常 细 致,O O O O O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 通 俗 易 懂 ， v e r y n i c e ！ ！ ！,B-ASP I-ASP O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 习 效 果 特 别 好 ， 理 解 起 来 也 是 十 分 的 容 易,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 结 构 体 系 清 晰 ， 非 常 适 合 初 学 者 快 速 入 门 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP O,2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 -1
还 不 错 ， 但 是 感 觉 内 容 总 有 些 没 有 讲 全 ， 可 能 这 只 是 基 础 课 程 吧 ！ 总 体 挺 好 的 ！,O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1
非 常 有 用 ， 课 程 画 面 设 计 很 简 洁 ， 咱 一 个 ！,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 课 循 序 渐 进 ， 且 有 理 论 有 实 操 ， 有 课 上 师 生 共 同 进 行 的 考 核 。 另 外 老 师 很 帅 ， 讲 话 很 绅 士 的 样 子 ， 让 人 能 耐 心 听 下 去 。,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 清 晰 ， 明 了 ， 讲 解 透 彻 ， 易 懂 ， 希 望 出 更 多 进 阶 课 程 ， 多 些 案 例 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1
很 清 晰 ， 层 次 好 ， 由 浅 入 深 ， 希 望 有 更 多 的 P Y T H O N 课 程,O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2
深 入 浅 出 ， 老 师 讲 的 超 级 棒 ！ ！ ！,O O O O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
路 线 明 确 ， 讲 解 清 晰 ， 代 入 感 强,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1
最 近 学 习 时 长 出 现 错 误 ， 没 有 增 加 。,O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O,-1 -1 0 0 0 0 -1 -1 0 0 -1 -1 -1 -1 -1 -1
很 好 ， 对 于 初 学 者 很 实 用 ， 要 想 深 入 还 得 多 做 题,O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP,-1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1
学 习 时 间 安 排 非 常 合 理 ， 学 习 内 容 讲 解 清 晰 ， 通 俗 易 懂,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1
小 白 类 型 的 ， 跟 着 嵩 天 老 师 一 节 课 一 节 课 的 学 习 ， 现 在 第 一 周 的 学 完 了 ， 死 记 硬 背 把 温 度 代 码 看 懂 、 默 写 ， 但 还 是 会 出 现 错 误 ， 打 字 慢 ， 作 业 一 直 完 不 成 . . .,O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP O O B-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1
这 课 程 比 较 基 础 ， 适 合 p y t h o n 入 门 。 课 程 中 穿 插 的 一 些 实 用 性 案 例 有 利 于 激 发 学 习 兴 趣 。,O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1
从 中 可 以 学 到 很 多 课 本 学 不 到 的 东 西 ， 在 其 取 其 精 华 ， 去 其 糟 怕 ， 上 课 的 思 维 方 式 清 晰 明 了 ， 让 人 有 种 流 连 忘 返 的 感 觉 ！,O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 0 0 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
对 p y t h o n 有 了 大 致 的 了 解 ， 并 且 产 生 了 浓 厚 的 兴 趣 ， 嵩 天 老 师 讲 的 很 好 ！,O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1
"非 常 好 的 一 门 课 程 , 非 常 感 谢 中 国 大 学 M O O C 平 台",O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2
课 程 很 好 ， 老 师 深 入 浅 出 的 讲 授 让 人 学 起 来 比 较 轻 松 ， 容 易 接 受 。,B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
希 望 一 个 视 频 可 以 长 一 点 ， 不 需 要 好 几 个 视 频 。,O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
全 国 最 好 的 P Y T H O N 基 础 课 程 （ 赞 赏 ）,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 2 2 -1 -1 -1 -1
很 不 错 ！ 老 师 讲 得 很 好 ， 很 认 真 ！,O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
可 以 很 方 便 入 门 p y t h o n 非 常 感 谢 慕 课 提 供 这 样 的 平 台 学 习 到 嵩 天 老 师 团 队 的 课 程,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 2 2 -1 2 2
"课 程 很 详 细 , 课 后 练 习 很 全 面",B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O,2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。,O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
学 到 了 不 少 新 知 识 ， 但 是 感 觉 在 现 实 生 活 很 少 用 到 了,O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1
学 到 了 很 多 P Y T H O N 的 基 础 知 识,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2
学 到 了 很 多 没 见 过 的 知 识 ， 让 我 懂 得 用 程 序 设 计 解 决 一 些 问 题,O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1
穿 插 的 实 例 很 好 ， 能 够 增 加 学 习 的 趣 味 性 和 成 就 感,O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 -1 2 2 2
收 获 很 大 ， 特 别 是 编 程 思 维 的 提 升 。,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1
教 授 讲 得 细 ， 思 维 清 晰 ， 语 言 精 练,B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1
老 师 讲 课 内 容 生 动 有 趣 ， 简 单 易 懂 ， 很 适 合 P y t h o n 初 学 者 学 习,B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 -1 -1
理 论 与 实 践 结 合 ， 有 提 示 有 总 结 ， 很 受 用,B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,2 2 -1 2 2 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1
老 师 团 队 很 棒 ， 内 容 通 俗 易 懂 ， 不 仅 打 实 基 础 ， 而 且 在 课 程 的 最 后 对 P Y T H O N 的 各 个 方 向 功 能 进 行 了 介 绍 ， 方 便 不 同 的 人 选 择 自 己 的 喜 好 与 目 标 ， 开 始 下 一 步 进 阶 学 习,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2
还 行 ， 继 续 探 索 中 ， 等 待 新 的 发 现 。,O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 很 好 ， 用 心 学 下 来 收 获 很 多,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
一 些 基 础 知 识 是 在 练 习 之 后 才 讲 到 ， 造 成 练 习 时 的 一 些 困 难,O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 1 1 1 1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
有 些 地 方 不 够 详 细 ， 哪 怕 花 点 钱 ， 如 果 有 人 能 及 时 指 出 我 的 问 题 ， 我 也 愿 意 。,O O B-ASP I-ASP O O O O O O O O O B-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
老 师 讲 课 很 有 高 度 ， 案 例 也 很 有 趣,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
教 学 由 浅 至 深 ， 通 俗 易 懂 ， 非 常 适 合 初 学 小 白 ！ ！ 感 谢 嵩 天 老 师 ！ ！,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1
首 先 课 程 时 长 设 置 很 合 适 ， 比 较 长 的 话 很 难 保 证 一 直 注 意 力 集 中 其 次 从 实 例 出 发 对 语 句 理 解 有 很 大 帮 助,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
内 容 新 颖 ， 组 织 科 学 ， 难 易 得 当 ， 学 习 精 品,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP,2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2
老 师 讲 的 内 容 浅 显 易 懂 ， 对 于 新 手 非 常 又 好 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
很 喜 欢 这 个 老 师 很 细 心 希 望 一 直 和 老 师 学 下 去 ， 成 为 一 名 编 程 高 手 . 说 学 好 编 程 要 花 4 0 0 个 小 时 ， 好 想 更 深 入 的 学 好 编 程 ， 跟 着 老 师 学 习,O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1
组 织 的 非 常 好 ， 一 直 想 要 学 习 P y t h o n ， 买 了 好 几 本 自 学 的 书 ， 加 入 了 不 少 论 坛 ， 都 是 半 途 而 废 。 可 能 在 计 算 机 专 业 同 学 看 来 一 个 常 识 性 的 东 西 ， 到 了 其 他 文 科 专 业 来 看 如 果 没 有 人 点 透 可 能 就 一 直 蒙 在 鼓 里 。 未 来 是 一 个 交 融 的 时 代 ， 各 个 学 科 之 间 的 相 互 交 融 才 能 推 动 文 明 和 技 术 的 进 一 步 蓬 勃 发 展 。 嵩 老 师 和 他 的 团 队 精 心 做 的 这 门 课 程 功 德 无 量 。,O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
实 例 性 的 教 学 挺 好 的 ， 有 操 作 感 ， 而 且 容 易 上 手 。 就 是 一 些 语 法 还 不 太 牢 固 。,O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
很 好 啊 对 我 这 个 入 门 小 白 来 说 已 经 很 够 用 了 。 这 些 东 西 重 在 实 践 ， 确 实 这 个 知 道 和 能 操 作 能 行 动 有 十 万 八 千 里 之 远 ， 重 在 从 实 践 中 认 识 ， 从 操 作 中 逐 渐 明 白 。 当 不 懂 代 码 意 思 的 时 候 ， 就 先 把 它 背 下 来 ， 默 写 下 来 ， 重 复 的 够 了 自 然 就 熟 能 生 巧 了 。,O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 组 织 新 颖 ， 每 个 实 例 带 动 基 础 语 法 的 学 习 让 学 习 者 感 到 有 趣 ， 也 易 与 记 忆 ， 体 会 到 老 师 的 用 心 良 苦 。,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
"非 常 不 错 的 课 程 , 由 浅 入 深 , 特 别 是 举 例 及 习 题 的 选 择 很 好 , 能 让 学 员 更 深 刻 的 理 解 课 程 内 容 !",O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 2 2 -1
老 师 讲 的 非 常 好 ， 语 速 适 中 ， 内 容 清 晰 ， 具 有 深 厚 的 专 业 功 底 ， 课 程 内 容 丰 富 全 面 ， 很 值 得 学 习 。,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
虽 然 但 是 ， 其 实 不 太 适 合 完 全 零 基 础 ， 之 前 听 过 这 门 课 ， 没 听 太 懂 ， 后 面 补 充 一 些 特 别 基 础 的 计 算 机 知 识 ， 二 刷 就 好 多 了 ， 但 是 课 程 安 排 真 的 很 棒 ， 很 适 合 系 统 性 的 学 习 。,O O O O O O O O O O O O O O O O O O O O O O O B-ASP O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 好 的 一 门 课 程 ， 系 统 地 入 门 p y t h o n ！ 详 略 得 当 ！ 适 合 任 何 小 白 学 习 ！,O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
深 入 浅 出 ， 例 子 都 是 精 心 挑 选 的 ， 进 步 很 大 。 感 谢 嵩 老 师,O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 2
自 身 需 要 多 加 练 习 代 码 才 可 以 更 好 的 掌 握 所 学 知 识 并 对 其 内 化,O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
上 课 内 容 容 易 理 解 ， 非 常 好 ， 适 合 当 作 P Y T H O N 入 门 课 程 学 习,O B-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O,-1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 2 -1 -1
这 可 这 是 个 好 的 学 习 软 件 啊 ！ ！ ！,O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
课 程 对 新 人 很 友 好 ， 支 持 嵩 天 老 师 。,B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
课 程 讲 解 的 逻 辑 很 清 晰 ， 语 言 表 达 很 好 ！ 加 油,B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O O,2 2 2 2 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
作 业 和 考 试 不 放 在 M O O C 平 台 放 在 别 的 地 方 ， 真 就 6 6 6,B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O,0 0 -1 0 0 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
很 喜 欢 这 门 课 的 内 容 ， 能 让 我 有 这 个 机 会 去 学 习 p y t h o n 。,O O O O O B-ASP O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1
对 于 入 门 级 学 生 来 说 ， 讲 课 方 式 独 具 一 格,O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
课 程 设 置 很 用 心 ， 讲 解 的 实 例 很 有 意 思,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1
一 直 都 没 系 统 性 认 真 的 去 学 习 p y t h o n ， 有 这 个 平 台 有 你 们 老 师 们 ， 相 信 对 这 方 面 会 加 深 理 解 和 运 用,O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2
学 了 p y t h o n 和 C ， 老 师 讲 解 细 致 ， 学 到 了 很 多 学 校 里 学 不 到 的 东 西 ， 非 常 开 心,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,-1 -1 1 1 1 1 1 1 -1 1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1
作 为 基 础 较 浅 的 其 他 专 业 学 生 ， 听 老 师 的 讲 解 觉 得 非 常 清 晰 、 比 较 易 于 理 解 ， 课 程 中 设 计 的 案 例 也 非 常 经 典 ， 可 以 学 到 的 点 很 多 。 感 谢 老 师 们 的 用 心 。,O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1
老 师 讲 的 很 细 致 ， 循 序 渐 进 ， 好 理 解 ！,B-ASP I-ASP O O O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 太 难 了 ， 到 现 在 都 不 能 单 独 设 计 代 码,B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP,0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0
非 常 好 ， 给 了 自 己 自 学 的 平 台 和 内 容 干 货,O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2
真 的 棒 ， 感 觉 是 听 过 的 最 好 的 网 课,O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
课 程 体 系 十 分 完 善 ， 有 效 的 帮 助 我 完 成 课 后 学 习 的 辅 导 ， 查 缺 补 漏 ， 感 谢 中 国 大 学 慕 课 这 个 平 台,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 2 2
很 好 ， 很 实 用 ， 通 俗 易 懂 。 建 议 学 习 这 门 课 。,O O O O O O O O O O O O O O O O O O B-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1
"练 习 一 定 要 做 , 编 程 就 是 多 练 习 . 没 有 捷 径",B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 0 0
好 喜 欢 这 个 老 师 ， 当 然 如 果 听 写 这 样 的 巩 固 能 够 更 系 统 化 或 多 一 些 会 更 好 。,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 棒 的 课 程 ， 给 我 这 个 小 白 打 开 了 一 扇 窗 户 。 谢 谢 您 ， 嵩 老 师 ， 谢 谢 所 有 制 作 和 发 布 的 团 队 ！,O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 的 内 容 很 基 础 ， 作 为 入 门 学 习 可 以 听 懂 ， 老 师 也 会 举 一 反 三 ， 让 人 受 益 匪 浅 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 知 识 点 总 是 由 一 个 小 的 编 程 案 例 引 入 ， 然 后 进 一 步 讲 解 相 关 的 语 法 等 知 识 ， 由 浅 入 深 ， 循 序 渐 进 ， 让 人 学 起 来 不 会 反 角 枯 燥 难 懂 ， 而 且 老 师 讲 课 给 人 一 种 很 舒 服 的 感 觉 ， 赞 ！,B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O,2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 得 非 常 清 晰 明 了 ， 例 子 有 趣,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
浅 显 易 懂 ， 最 好 是 推 荐 一 些 阅 读 材 料,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
能 照 顾 到 学 习 者 的 接 受 能 力 ， 提 供 了 足 够 多 的 库 拓 展 。 一 些 高 级 语 法 即 使 不 详 细 讲 ， 如 果 能 提 及 提 供 后 续 了 解 的 途 径 就 更 好 了,O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 的 内 容 很 充 实 ， 没 有 多 余 的 废 话 ， 全 是 干 货 ！,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 2 2 -1
老 师 讲 的 很 通 俗 易 懂 ， 纯 文 科 生 表 示 听 懂 了 虽 然 还 是 不 太 会 写 程 序,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1
适 合 0 基 础 ， 讲 解 到 位 细 致 ， 入 门 好 课 程 。,O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 2 2 -1
学 会 了 p y t h o n 语 言 程 序 设 计,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 2 2 2 2 2 2 2 2 2 2 2 2
h e n h a o h e n y o u b a n g z u,O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 的 授 课 方 式 很 好 的 激 发 了 我 对 于 p y t h o n 学 习 的 乐 趣 ， 我 觉 得 嵩 老 师 的 授 课 方 式 相 较 于 传 统 的 课 堂 ， 更 加 的 有 活 力 和 启 发 ！ ！,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O,1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1
非 常 详 细 ， 通 俗 易 懂 才 是 真 ！ ！ 谢 谢 嵩 天 老 师 ！,O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
课 程 做 的 挺 好 。 建 议 多 一 些 不 计 时 程 序 练 习 题 。 计 时 型 程 序 设 计 题 量 较 大 ， 规 定 时 间 内 常 做 不 完 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 0 0 0 0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
基 础 语 法 ， 穿 插 有 意 思 的 实 例 ， 渗 透 编 程 思 维,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP,1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1
正 在 学 习 ， 跟 着 老 师 一 步 步 学 收 获 很 大,O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1
怎 么 说 呢 ？ 嵩 天 老 师 的 课 听 着 就 是 悦 耳 ， 让 我 可 以 一 直 坚 持 下 来 ， 感 谢,O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 1 1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 细 致 ， 也 很 容 易 理 解 ， 非 常 好,B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O,1 1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
虽 然 由 于 自 身 原 因 ， 没 跟 上 老 师 的 进 度 ， 但 是 后 来 仍 有 继 续 学 习 。 带 来 了 P y t h o n 的 整 体 性 学 习 ， 由 入 门 到 逐 渐 深 入 理 解 。 还 有 无 尽 的 变 成 乐 趣 ， 非 常 感 谢 。,O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
课 程 讲 解 生 动 有 趣 ， 但 实 践 案 例 要 是 再 多 点 就 好 了,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1
从 风 变 入 p y t h o n ， 从 m o o c 学 p y t h o n ， 有 人 教 真 的 比 自 己 摸 索 好 太 多,O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O,-1 1 1 -1 1 1 1 1 1 1 -1 -1 1 1 1 1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
作 为 一 名 大 学 生 学 习 P y t h o n 感 觉 受 益 匪 浅 ， 很 棒 的 一 门 教 程,O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 1 1 1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
非 常 好 ， 老 师 讲 的 很 详 细 。 很 有 用,O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 c 语 言 基 础 ， 学 起 来 还 是 挺 轻 松 的 ， 1 . 5 倍 速 比 较 舒 服 。,O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O,-1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1
p y t h o n 语 言 如 此 简 便 ， 功 能 如 此 强 大 ， 让 我 爱 上 了 这 门 语 言 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
非 常 好 的 课 程 ， 但 理 论 和 实 践 还 是 有 差 距 ， 需 要 不 断 练 习 ！,O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 2 2 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
宝 藏 级 入 门 课 程 ， 内 容 丰 富 ， 讲 解 清 晰 ， 让 我 一 个 真 正 的 小 白 爱 上 了 这 个 方 向,O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
Q 1 5 4 3 5 1 9 2 5 一 起 讨 论 吧,O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 的 课 通 俗 易 懂 ， 十 分 生 动 ， 有 非 常 不 错 的 学 习 体 验 ！ ！ ！,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O,1 1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1
内 容 组 织 深 入 浅 出 ， 既 有 基 础 ， 又 培 养 了 计 算 思 维 ， 很 棒 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
很 好 的 一 门 课 程 ！ 从 实 例 入 手 使 得 学 生 不 会 失 去 对 于 编 程 的 兴 趣 ， 同 时 对 于 难 度 把 握 得 很 好 ， 对 于 编 程 新 手 而 言 非 常 友 善 ， 收 获 很 大 ！,O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
非 常 棒 的 课 程 ， 深 入 浅 出 ， 生 动 有 趣 ！,O O O O B-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 都 比 较 系 统 ， 可 以 学 到,B-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
前 5 周 坚 持 的 比 较 好 ， 后 面 没 有 跟 上 老 师 的 节 奏 ， 希 望 尽 快 补 上 落 下 的 课 程 ， 坚 持 完 整 个 p y t h o n 系 统 的 学 习 ， 毕 竟 老 师 讲 的 真 的 很 系 统 很 详 细 ， 很 有 魅 力 ， 加 油 ， 谢 谢 老 师,O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2
编 程 是 非 常 好 的 工 具 ， 为 提 高 科 研 及 其 他 工 作 效 率 能 发 挥 重 要 作 用,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
这 门 课 老 师 讲 解 的 很 好 ， 也 比 较 容 易 理 解,O O B-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O,-1 -1 2 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 棒 ， 学 到 了 很 多 知 识 ， 比 自 己 看 书 学 的 更 快 ， 老 师 上 课 逻 辑 讲 授 的 很 清 晰 ， 希 望 P P T 能 够 和 新 课 程 的 尽 量 更 新 一 致 ~,O O O O O O O O O B-ASP I-ASP O O O O O B-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
超 级 好 的 一 门 课 程 ， 收 获 很 大 ， 学 习 得 很 轻 松 也 很 扎 实,O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 得 很 细 致 很 认 真 ， 效 果 不 错 ！ 建 议 视 频 不 要 剪 辑 成 太 多 段 。,O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 的 教 学 方 法 很 棒 ， 先 讲 基 本 知 识 点 ， 然 后 又 会 给 出 例 子 ， 通 过 讲 解 例 子 帮 助 同 学 们 更 好 地 掌 握 函 数 用 法 。,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
可 以 简 单 地 编 写 一 些 代 码 了 ， 感 谢 老 师,O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2
很 不 错 的 课 程 ， 可 能 是 自 己 基 础 差 吧 ， 有 些 东 西 还 是 理 解 不 了,O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
建 议 不 要 将 视 频 过 于 分 散 …… 太 多 视 频 都 是 短 于 2 分 钟 …… 实 在 是 让 学 习 的 连 贯 性 受 阻 ， 学 习 效 率 降 低 了,O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 -1
各 方 面 都 很 好 ， 老 师 要 是 可 以 在 课 上 点 评 讲 解 习 题 就 更 棒 了,O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
课 程 内 容 组 织 很 好 ， 案 例 教 学 效 果 很 好,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O,1 1 1 1 2 2 -1 -1 -1 1 1 1 1 2 2 -1 -1
老 师 讲 的 真 的 很 好 ， 每 周 的 练 习 题 也 很 有 针 对 性 ， 很 实 用 的 课 程 ！ ！ ！,O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1
很 清 楚 ， 小 白 也 会 了 n i c e,O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
"特 别 喜 欢 这 样 的 风 格 , 每 节 课 程 都 比 较 短 , 但 是 不 深 入 这 是 一 个 缺 点",O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 0 0
可 以 在 讲 详 细 一 点 ， 细 一 点 ， 可 以 更 好 的 让 零 基 础 的 同 学 学 会,O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1
一 个 编 程 小 白 ， 因 为 学 了 这 个 课 ， 突 然 对 编 程 很 兴 趣 ， 老 师 讲 得 好,O O O O O O O O O O O O O B-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1
老 师 讲 的 很 清 楚 ， 初 学 者 能 很 好 的 掌 握,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1
脉 络 由 浅 入 深 讲 解 深 入 浅 出 很 详 细 也 很 棒 ！,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 解 风 格 鲜 活 生 动 易 于 理 解 易 于 掌 握,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 棒 ， 充 实 而 且 细 致 ， 非 常 适 合 入 门,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
好 课 ， 不 仅 学 到 了 新 语 言 ， 也 对 我 之 前 学 到 的 语 言 进 行 了 巩 固,O B-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O,-1 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 课 程 真 的 很 奶 思 ~ 而 且 知 识 点 覆 盖 很 全 面 ， 课 程 设 计 很 棒,B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1
从 一 个 编 程 小 白 到 如 今 的 略 有 基 础 ， 非 常 感 谢 这 门 课 程 。 老 师 讲 的 很 好 ， 通 俗 易 懂 ， 课 程 架 构 很 清 晰 。,O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
课 程 循 序 渐 进 ， 相 对 习 题 有 点 难 度 ， 可 给 点 扩 展 资 料,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 0 0 -1 -1 -1 -1 1 1 1 1
可 以 有 这 么 优 秀 的 学 习 资 源 ， 真 的 很 满 足,O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 得 特 别 好 ， 学 生 记 住 后 ， 用 得 少 ， 没 有 形 成 能 力 ， 想 多 学 几 遍,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
"老 师 讲 课 非 常 清 晰 , 受 益 良 多",B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1
课 程 由 浅 入 深 ， 循 序 渐 进 ， 实 践 性 和 操 作 性 很 强 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 -1 -1 -1
课 程 简 单 易 懂 ， 程 序 非 常 容 易 上 手,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 非 常 好 ， 结 构 很 好 ， 很 容 易 理 解,B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
课 程 设 计 的 很 到 位 。 学 习 的 知 识 较 为 基 础 ， 适 合 入 门 。 老 师 的 讲 解 也 深 入 浅 出 ， 很 有 条 理 ， 上 课 感 觉 很 不 错 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 深 入 浅 出 ， 让 编 程 小 白 也 能 听 得 懂 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
很 好 的 一 个 平 台 ， 学 习 p y t h o n ， 打 开 了 我 的 编 程 世 界 。 课 程 的 老 师 很 棒 ， 讲 解 细 致 有 道 。,O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1
p y t h o n 需 要 记 的 东 西 比 较 多 ， 算 法 等 对 于 零 基 础 的 我 来 讲 还 是 比 较 吃 力 ， 只 有 通 过 照 葫 芦 画 瓢 多 练 习 才 能 理 解 。,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O,0 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 授 思 路 清 晰 ， 且 有 不 少 知 识 远 超 于 学 校 讲 授 内 容 。,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP O,2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 1 1 -1
总 体 上 感 觉 非 常 棒 ， 还 有 课 件 可 以 看 ， 哪 里 可 以 充 值 充 值 学 习 搞 起 来,O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1
本 科 阶 段 不 喜 欢 编 程 ， 甚 至 排 斥 恐 惧 ， 现 在 毕 业 了 却 爱 上 了 编 程 。 哈 哈,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O,1 1 1 1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 真 的 很 认 真 ， 从 有 一 节 课 给 时 间 默 写 ， 我 就 发 现 这 课 不 一 般 啊 ， 接 着 学 去 了,B-ASP I-ASP O O O O O O O O O O B-ASP O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 2 2 1 1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
大 学 m o o c ， 非 常 棒 ， 为 国 加 油 ， 给 力 ！,B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 易 懂 紧 凑 ， 适 合 各 阶 段 学 生 参 与 学 习,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1
老 师 很 耐 心 ， 讲 的 很 明 白 ， 姿 态 表 情 都 给 人 一 种 轻 松 愉 悦 的 感 觉 ， P P T 也 很 详 细 明 确 。,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1
简 明 易 懂 ， 条 例 清 晰 ， 案 例 很 有 代 表 性 。 知 识 点 清 晰,O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1
基 本 在 讲 基 础 知 识 ， 运 用 案 例 较 少 ， 能 增 加 点 实 际 的 运 用 案 例 更 吸 引 人 。 感 觉 还 是 有 点 枯 燥 了 点 ， 但 还 是 坚 持 下 来 了 。,B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O,1 1 -1 -1 1 1 1 1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 的 课 深 入 浅 出 ， 举 一 反 三 ， 很 有 启 迪 意 义,B-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP,1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2
找 了 很 久 找 到 这 样 好 的 课 程 ， 作 为 P y t h o n 的 启 蒙 必 不 可 少 ， 比 外 面 的 高 价 学 习 班 课 程 不 知 好 了 多 少 ， 而 且 还 可 以 进 阶 学 习 ， 希 望 自 己 以 考 代 学 可 以 学 到 更 多 东 西 ！ 非 常 感 谢 ！,O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 的 非 常 详 细 ， 让 我 对 p y t h o n 有 了 很 深 入 的 了 解 ， 并 且 产 生 浓 厚 的 兴 趣 ， 期 望 老 师 有 更 多 好 的 课 程 推 出,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1
这 简 洁 易 懂 ， 需 要 细 细 消 化 其 中 的 知 识 点 。,O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1
老 师 讲 得 非 常 好 ， 继 续 努 力 学 习 。,B-ASP I-ASP O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 的 课 让 我 对 p y t h o n 有 了 一 个 体 系 结 构 的 认 识,B-ASP I-ASP O B-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O,1 1 -1 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1
对 于 一 个 完 全 不 懂 编 程 的 人 ， 这 个 学 习 还 是 吃 力 的,O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
老 师 讲 解 的 很 清 楚 ， p p t 也 一 一 列 出 来 了 重 点 ， 可 以 试 一 试,B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 地 非 常 好 。 希 望 能 多 讲 一 些 实 用 的 科 研 仿 真 需 要 的 扩 展 包 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1
了 解 了 p y t h o n 语 言 使 用 方 法,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 1
内 容 很 好 ， 由 浅 入 深 ， 老 师 讲 的 非 常 通 俗 易 懂,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
本 人 是 高 三 文 科 生 ， 下 岗 考 生 再 就 业 ， 学 起 了 p y t h o n 。 老 师 讲 解 得 比 较 有 趣 ， 手 把 手 教 的 几 个 实 例 也 很 能 让 学 生 熟 悉 这 门 语 言 。 期 待 老 师 的 进 阶 课 程 。,O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 2 2 2 2 -1
这 个 课 程 设 计 的 很 科 学 ， 浅 显 易 懂 。 老 师 的 讲 解 深 入 浅 出 。 平 台 可 以 倍 速 观 看 ， 根 据 自 己 掌 握 的 程 度 进 行 控 制 听 课 速 度 ， 非 常 非 常 棒 。,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
非 常 棒 的 课 程 ， 对 于 小 白 也 非 常 友 好 ， 合 理 的 课 程 设 计 和 有 趣 的 应 用 实 例 非 常 易 上 手 。 老 师 的 讲 课 不 单 单 是 说 明 编 程 的 方 法 ， 更 多 的 在 传 授 程 序 设 计 的 思 维 ， 这 是 我 认 为 最 有 价 值 的 地 方 有 些 课 程 内 容 没 覆 盖 到 的 知 识 点 和 问 题 也 可 以 通 过 搜 索 引 擎 寻 找 答 案 ， 也 希 望 老 师 能 够 推 荐 一 些 好 的 论 坛 或 者 网 站 供 初 学 者 学 习,O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 1 1 1 -1 0 0 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 细 节 ， 很 好 。 讲 课 风 度 翩 翩 ， 娓 娓 道 来 ， 观 感 很 舒 服 。 感 谢 老 师 ， 只 是 平 台 使 用 上 有 些 问 题 ， 在 每 个 视 频 衔 接 上 的 处 理 可 以 更 好 。,B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O,1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 0 0 -1 -1 -1 -1 -1
非 常 好 的 P y t h o n 入 门 课 程 ， 讲 的 非 常 浅 显 而 详 细 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,-1 -1 -1 -1 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 的 很 好 ， 内 容 深 入 浅 出 ， 对 初 学 者 来 说 ， 容 易 懂 。,B-ASP I-ASP I-ASP B-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 便 于 入 门 。 就 是 讲 义 不 能 下 载 不 太 方 便 。,B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1
言 简 意 赅 ， 通 俗 易 懂 ， 从 基 础 学 习 ， 循 序 渐 进 掌 握 了 基 本 语 法 体 系 ， 句 式 结 构 ， 函 数 表 达 格 式 ， 每 节 章 节 后 都 能 带 领 学 生 回 顾 总 结 ， 真 的 很 感 谢 老 师 这 样 的 安 排 。,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 -1 1 1 1 1 1 1 -1 -1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1
一 开 始 感 觉 P y t h o n 挺 难 的 ， 没 想 到 跟 着 老 师 血 了 几 周 以 后 逐 渐 掌 握 了 一 些 知 识 后 ， 也 可 以 写 一 些 程 序 代 码 ， 还 蛮 有 意 思 的 一 门 课 ， 推 荐 学 习,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP O O O O O,-1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1
内 容 非 常 详 细 ， 理 论 穿 插 实 例 有 助 于 理 解 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1
课 程 很 棒 ， P y t h o n 的 入 门 必 选,B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O,2 2 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1
H e l l o W o r l d ! H e l l o P y t h o n !,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1
虽 然 每 一 节 课 简 短 ， 但 是 内 容 易 懂 ， 并 且 感 觉 比 平 时 课 堂 上 学 的 印 象 更 深,O O O O O B-ASP O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1
老 师 课 堂 讲 授 内 容 深 入 浅 出 ， 让 学 生 很 容 易 就 掌 握 学 习 的 方 法 和 技 能 。,B-ASP I-ASP B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O,2 2 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1
讲 的 很 好 ， 框 架 很 好 ， 还 有 课 件 和 源 代 码 ， 用 来 预 习 和 复 习,O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
通 俗 易 懂 ， 生 动 活 泼 ， 比 用 死 板 的 书 本 要 好 ， 组 织 的 很 好 。,O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 2 2 -1 -1 -1 -1
想 对 理 解 起 来 还 需 要 花 费 更 多 的 时 间,O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0
让 我 了 解 了 新 的 东 西 ， 感 觉 P y t h o n 是 一 门 很 有 意 思 的 课 程,O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
我 是 零 基 础 ， 觉 得 对 于 入 门 来 说 ， 课 程 设 计 得 合 理 ， 容 易 跟 上 进 度 ， 学 习 吸 收 较 快 。 课 程 设 计 也 注 重 互 动 ， 提 出 了 相 关 的 方 法 论 。 是 一 门 不 错 的 课 程 。,O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
老 师 讲 的 很 好 ， 容 易 理 解 。 就 是 p y t h o n 1 2 3 平 台 上 有 些 题 目 用 字 典 推 导 式 会 导 致 延 时 使 得 报 错,B-ASP I-ASP B-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 0 0 -1 1 1 1 1 1 -1 -1 -1 0 0 -1 -1 0 0
基 础 知 识 讲 的 太 细 ， 重 点 难 点 又 讲 的 太 粗 略,B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,0 0 0 0 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1
讲 解 通 俗 易 懂 ， 语 速 也 不 快 ， 新 手 能 适 应 。,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1
课 程 的 内 容 很 适 合 初 学 者 ， 老 师 的 讲 解 清 晰 透 彻 ， 非 常 好 ！,B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O,2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
"让 我 重 新 认 识 了 p y t h o n , 也 更 喜 欢 了",O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
觉 得 还 是 很 实 用 的 ， 也 对 自 己 有 了 一 定 的 帮 助,O O O O O O O O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
在 完 成 作 业 的 过 程 中 ， 体 验 那 种 可 以 随 意 尝 试 自 己 的 想 法 ， 去 调 试 程 序 ， 然 后 看 到 结 果 后 ， 又 回 来 不 断 的 尝 试 和 完 善 程 序 ， 这 种 感 觉 是 好 的 ！ 也 有 不 好 的 感 觉 ， 就 是 当 你 被 一 个 问 题 卡 住 的 时 候 ， 这 个 时 候 由 于 自 己 知 识 的 积 累 不 够 ， 想 不 到 该 如 何 处 理 的 时 候 ， 特 别 痛 苦 ， 但 是 这 个 时 候 越 痛 苦 ， 等 你 解 决 了 这 个 问 题 的 时 候 ， 那 感 觉 就 越 幸 福 。 这 门 课 好 得 地 方 ， 就 是 它 的 很 多 的 作 业 和 练 习 之 间 有 联 系 ， 让 你 一 步 步 的 深 入 ， 享 受 那 种 手 到 擒 来 的 感 觉 ， 虽 然 我 还 没 有 达 到 这 种 境 界 ， 但 是 可 以 体 验 到 原 来 世 界 还 有 这 么 好 玩 的 一 面,O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O B-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 棒 ， 1 2 3 平 台 也 很 用 心,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1
挺 好 的 ， 很 基 础 ， 可 能 节 奏 有 点 慢,O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 2 2 -1 -1 -1 0 0 -1 -1 -1
老 师 讲 的 非 常 好 ， 只 是 自 己 编 程 小 白 ， 概 念 性 的 能 理 解 ， 但 是 运 用 到 编 程 中 就 掉 链 子 ， 还 需 多 多 学 习,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
授 课 方 式 很 好 ， 从 基 础 到 提 高 ， 一 环 扣 一 环 ， 讲 的 内 容 也 是 很 面 向 学 生 的 ， 很 基 础 ， 很 好 操 作 ， 如 果 在 讲 课 过 程 中 突 然 出 现 一 个 没 有 见 过 的 一 个 名 词 或 者 什 么 东 西 ， 别 一 笔 带 过 ， 顺 便 说 了 意 思 就 行 了 ， 本 来 听 的 好 好 的 ， 突 然 卡 顿 一 下 子 脑 子 懵 了 ， 加 油 ！ 奥 利 给 ！,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 课 细 致 ， 学 了 C 再 来 学 p y t h o n 可 以 很 好 接 受,B-ASP I-ASP O O O O O B-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1
深 入 浅 出 ， 不 论 是 小 白 还 是 老 手 ， 都 能 从 这 一 门 课 程 中 获 益 匪 浅,O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 的 通 俗 易 懂 ， 知 识 点 清 楚 ， 辛 苦 老 师 们 了 。,O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 2 -1 -1
非 常 棒 ， 从 实 例 开 始 ， 深 入 浅 出 的 教 学,O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
非 常 好 ， 可 以 激 发 学 生 的 学 习 兴 趣 ！,O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1
每 周 的 小 程 序 有 助 于 理 解 掌 握 知 识 ， 挺 好 的 ， 以 练 促 学,O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O,-1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
安 排 很 细 心 ， 很 适 合 我 这 样 的 零 基 础 的,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1
目 前 刚 刚 开 始 ， 老 师 讲 的 很 细 致 很 容 易 听 懂 ， 希 望 自 己 能 坚 持 下 来 ， 加 油 ！,O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
学 习 起 来 很 有 趣 ， 好 评 ^ . ^ /,O O O O O O O O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
等 了 两 年 ， 终 于 申 请 到 证 书 了 … … 上 课 都 有 很 认 真 的 做 笔 记 ， 嵩 天 老 师 n b ！,O O O O O O O O O O B-ASP I-ASP O O O O B-ASP O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1
老 师 讲 解 的 很 容 易 懂 ， 视 频 录 制 清 晰 ， 很 棒 哦,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
很 细 致 ， 内 容 很 实 用 。 适 合 新 学 者,O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2
对 于 p y t h o n 基 础 入 门 ， 是 非 常 好 的,O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 2 2 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
原 来 学 过 ， 忘 了 ， 又 开 始 复 习 ， 感 觉 棒 极 了,O O O O O O O O O O O B-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1
时 间 自 由 ， 老 师 上 课 认 真 ， 助 教 耐 心 认 真 解 答 ， 谢 谢 。 让 我 爱 上 了 编 程,B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 解 非 常 清 晰 ， 目 前 还 是 初 学 阶 段 ， 希 望 自 己 可 以 早 日 上 手 ~,B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 得 不 错 ， 加 上 实 例 ， 很 有 代 入 感 ， 很 适 合 新 手 。,B-ASP O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 2 -1 -1 -1 -1 2 2 -1
编 程 0 基 础 参 加 学 习 ， 嵩 天 老 师 深 入 浅 出 ， 讲 解 细 致 ， 获 益 匪 浅 。,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1
通 过 学 习 p y 我 的 学 习 方 面 和 处 事 行 为 发 生 改 变,O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 -1 -1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1
从 课 程 中 学 到 了 不 少 老 师 授 课 思 路 清 晰 ， 讲 解 十 分 清 楚,O B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O O,-1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
很 棒 的 课 程 ， 有 字 幕 ， 开 2 倍 速 也 能 听 清 在 说 什 么 ， 实 例 很 多 ， 很 实 用,O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP,-1 -1 -1 2 2 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2
发 现 课 程 发 现 的 晚 了 ， 早 点 发 现 早 点 学 习 就 更 好 了 。 很 喜 欢 这 个 老 师 ， 讲 课 之 前 会 把 所 有 的 大 纲 先 捋 一 遍 ， 不 至 于 不 知 道 自 己 学 的 是 什 么 东 西 ， 总 之 可 能 细 节 还 需 要 不 断 的 使 用 来 记 忆 ， 但 是 这 样 的 授 课 方 式 可 以 让 我 编 写 程 序 的 大 概 的 思 路 一 直 都 在,O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1
这 个 课 程 知 识 讲 解 清 晰 明 了 ， 便 于 理 解 ， 课 后 习 题 安 排 合 理 ， 对 巩 固 知 识 点 起 重 要 作 用,O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O,-1 -1 2 2 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1
相 对 来 说 更 适 合 有 一 定 基 础 的 学 习 ， 某 些 地 方 可 以 适 当 将 详 细 一 点 。 但 是 这 样 会 缺 少 自 己 思 考 的 过 程 。,O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
老 师 讲 课 很 好 ， 刚 好 买 了 一 本 嵩 天 老 师 编 写 的 p y t h o n 二 级 考 试 教 材 ， 学 习 效 果 还 可 以 ， 关 键 还 是 要 多 加 练 习 。 老 师 们 辛 苦 了 ！,B-ASP I-ASP O B-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O,2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 -1 -1 -1 -1
学 到 了 很 多 p y t h o n 编 程 技 巧,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 2
对 零 基 础 小 白 来 说 ， 难 度 有 点 大 。 但 是 多 看 几 遍 深 入 研 究 还 是 能 掌 握 的 ， 加 油 。,O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 思 路 清 晰 又 非 常 耐 心 ， 非 常 喜 欢,B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O,2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 的 很 清 楚 ， 很 容 易 理 解 ， 而 且 也 比 较 高 效 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
1 9 期 前 的 作 业 和 练 习 哪 里 找 呢 ， P y t h o n 只 开 通 了 1 9 期 的 作 业 吗 ？,B-ASP I-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O,1 1 1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 1 -1 1 1 -1 -1
就 是 有 时 候 中 午 看 听 课 特 别 容 易 犯 困 h h,O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 解 清 晰 ， 教 学 目 标 明 确 ， 给 人 的 启 发 性 很 大 。,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O,2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
挺 好 的 ， 有 作 业 和 练 习 ， 而 且 一 周 只 需 花 4 ， 5 个 小 时 就 行 了 。,O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
有 趣 有 料 非 常 棒 ( ๑ • ̀ ㅂ • ́ ) و ✧,O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 讲 得 很 好 ， 符 合 学 习 规 律 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
很 用 心 ， 讲 的 很 好 ， 课 件 做 的 也 很 好 ， 先 过 一 遍,O O O O B-ASP O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
能 坚 持 下 去 ， 对 于 有 一 点 编 程 基 础 知 识 的 学 起 来 很 容 易,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1
前 后 知 识 点 衔 接 有 矛 盾 。 视 频 中 有 错 误 ， 比 如 第 六 章 第 二 节 的 第 五 个 视 频 里 面 第 二 个 元 素 的 序 号 应 该 是 1 ， 建 议 老 师 先 运 行 一 遍 再 放 上 来 呢 : ),O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,-1 -1 0 0 0 0 0 -1 0 0 -1 0 0 -1 -1 0 0 -1 -1 -1 1 1 1 1 1 1 -1 1 1 1 1 1 -1 -1 1 1 1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 非 常 好 ， 难 度 适 中 ， 对 我 这 个 城 乡 规 划 专 业 的 毕 业 生 就 非 常 的 友 好 ， 看 来 我 的 自 动 化 办 公 和 自 动 化 交 易 有 戏 了 。,B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 2 2 2 2 2 -1 -1 -1 -1
以 案 例 为 导 向 ， 通 过 实 际 应 用 学 习 新 知 识 ， 更 清 晰 明 了 ， 更 易 于 理 解 各 语 句 和 库 的 功 能 和 应 用 。 在 教 学 中 自 然 而 然 的 融 入 课 程 思 政 ， 在 学 习 专 业 知 识 的 同 时 ， 收 获 积 极 向 上 的 力 量 ！,O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O B-ASP O B-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O,-1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 -1 1 1 -1 1 1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1
课 程 内 容 丰 富 ， 结 构 合 理 ， 很 适 合 p y t h o n 初 学 者 学 习,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 2 -1 -1
老 师 讲 解 的 很 清 楚 作 为 入 门 非 常 好,B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
老 师 讲 的 很 好 ， 1 . 2 5 倍 速 觉 得 很 棒,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O,1 1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1
这 个 课 程 入 门 学 习 非 常 合 适 ， 嵩 老 师 温 文 尔 雅 ， 讲 课 逻 辑 清 晰 明 了 ， 对 小 白 很 友 好 ， 同 时 课 程 越 来 越 完 善 了 ， 还 加 了 字 幕 和 附 件 ， 强 烈 推 荐 ·,O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1
内 容 十 分 多 ， 很 喜 欢 ， 就 是 有 些 地 方 感 觉 不 详 细,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
我 感 觉 课 程 的 知 识 组 织 结 构 很 好 ， 很 满 意,O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 2 2 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1
听 了 很 多 类 似 的 p y t h o n 课 程 这 门 最 好,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1
每 节 课 都 能 体 会 到 老 师 的 精 心 准 备 和 丰 富 的 学 识 ， 收 获 满 满 ！ ！ ！,O O B-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O,-1 -1 2 -1 -1 -1 -1 -1 1 1 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1
老 师 的 教 学 方 式 很 清 晰 明 了 ， 浅 显 易 懂 ， 即 便 是 小 白 也 还 是 能 听 懂 不 少 内 容,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
内 容 组 织 合 理 ， 讲 解 详 细 ， 看 得 出 来 课 题 组 很 费 心 ， 是 学 习 过 的 课 程 中 非 常 出 色 的 ， 不 愧 于 国 家 级 精 品 ， 向 老 师 学 习 ！,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 2 2 -1 -1 -1
"非 常 棒 , 我 从 来 没 有 学 过 , 但 是 这 几 节 课 下 来 , 感 觉 懂 了 许 多 ( 懂 王 . j p g )",O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 解 很 棒 ， 希 望 自 己 能 多 上 几 次 类 似 的 课 程,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2
课 程 设 置 很 合 理 ， 由 浅 入 深 ， 通 俗 易 懂 ， 作 为 入 门 级 课 程 非 常 好 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 -1 -1 -1 -1
主 修 C 语 言 准 备 考 p y t h o n 计 算 机 二 级 来 的 个 人 觉 得 第 一 周 的 测 试 题 对 于 零 基 础 的 同 学 可 能 会 有 点 难 度 老 师 讲 解 的 很 细 致 很 生 动 加 油 ( ง • ̀ _ • ́ ) ง,O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O,-1 -1 1 1 1 -1 -1 -1 1 1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 0 0 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 解 简 洁 明 了 却 又 不 缺 重 点 内 容 ， 省 时 还 有 用 ， 非 常 完 美 ！,B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
可 以 作 为 p y t h o n 入 门 课 程 来 学 习 ， 课 程 质 量 很 不 错 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP B-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
非 常 不 错 ， 最 好 增 加 一 些 面 向 对 象 的 知 识,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2
感 觉 非 常 好 ， 课 程 讲 得 好 ， 对 于 初 学 者 和 非 计 算 机 专 业 的 大 有 益 处 。,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 解 的 特 别 清 晰 ， 而 且 习 题 也 很 好 ， 很 能 考 验 变 通 思 维 ， 从 此 爱 上 p y t h o n,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2 2 2
特 别 喜 欢 这 种 方 式 ， 通 过 实 例 来 讲 解 内 容 ， 通 俗 易 懂 ， 而 且 每 个 视 频 时 间 不 长 ， 不 会 看 着 很 累 ， 而 且 知 识 很 全 面 ， 最 后 通 过 练 习 以 及 测 验 可 以 巩 固 学 习 内 容,O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2
不 错 啊 ， 很 适 合 小 白 学 习 ， 循 序 渐 进,O O O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
嵩 老 师 讲 课 灵 活 ， 循 序 渐 进 ， 实 例 、 理 论 并 行 。 学 习 本 门 课 程 为 应 用 P y t h o n 打 下 非 常 好 的 基 础 。,B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O,2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 于 零 基 础 学 员 来 说 ， 收 获 颇 多 ， 老 师 给 出 的 很 多 实 例 也 都 是 很 有 意 思 的 例 子,O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
加 快 了 对 语 言 的 理 解 和 掌 握 速 度 ， 很 好 的 老 师 。,O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 -1 -1 1 1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 2 2 -1
非 常 好 ！ 让 我 脱 离 了 p y t h o n 小 白 的 群 体,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 0 0 -1 -1 -1
"p y t h o n 对 我 是 陌 生 的 ， 对 于 学 渣 的 我 来 说 是 一 个 全 新 的 尝 试 ， 怀 着 好 奇 心 学 习 了 四 天 ， 产 生 了 全 所 未 有 的 体 验 ， 真 的 是 太 神 奇 了 ， 我 爱 了 ， 真 的 爱 了 。 第 一 天 兴 趣 使 然 ， 学 写 编 程 ， 按 F 5 后 看 到 电 脑 屏 幕 出 来 的 同 切 圆 和 红 色 五 角 星 着 实 让 我 兴 奋 不 已 ； 第 二 天 的 感 觉 就 是 头 要 爆 炸 ， 完 全 搞 不 懂 温 度 编 码 的 内 容 ， 还 是 耐 心 听 完 了 所 有 课 程 ， 结 果 慢 慢 懂 了 其 中 缘 由 ， 成 就 感 油 然 而 生 ； 第 三 天 克 服 困 难 ， 做 出 了 漂 亮 的 p y t h o n 蟒 蛇 ， 感 觉 太 H 了 ， 又 自 己 做 了 6 条 6 种 颜 色 的 蟒 蛇 朝 向 不 同 方 向 并 分 享 在 了 群 里 。 通 过 四 天 的 学 习 ， 我 这 个 学 渣 对 编 程 有 了 些 许 的 进 步 ， 让 我 对 p y t h o n 有 了 浓 厚 的 兴 趣 ， 同 时 也 让 我 实 践 了 只 要 用 成 长 型 的 心 态 去 学 习 ， 刻 意 练 习 的 方 法 去 实 践 ， 我 是 可 以 踏 进 p y t h o n 的 大 门 ， 我 非 常 喜 欢 这 样 的 成 长 ， 相 信 你 们 也 一 定 会 爱 上 p y t h o n , 爱 上 成 长 的 自 己 。",B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O,1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1
老 师 讲 的 通 俗 易 懂 很 ， 有 趣 味 性 ， 稍 有 不 足 的 是 在 做 一 些 练 习 和 作 业 时 会 用 到 课 上 没 讲 到 的 命 令 ， 做 起 来 稍 显 吃 力,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 非 常 好 ， 循 序 渐 进 ， 很 吸 引 人,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 一 个 初 学 者 来 讲 ， 我 个 人 认 为 很 有 帮 助,O O O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
很 好 的 帮 助 了 我 对 P y t h o n 的 应 用 和 理 解,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 2 2
讲 解 清 晰 ， 可 以 反 复 观 看 ， 加 深 印 象,B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 的 讲 解 很 有 趣 ， 从 另 一 角 度 学 习 p y t h o n ， 有 点 担 心 不 知 能 不 能 学 会 ， 但 感 受 这 个 课 程 的 制 作 团 队 很 用 心 。,B-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1
老 师 讲 课 结 合 了 很 多 实 际 ， 老 师 辛 苦 了 。 P y t h o n 的 理 论 基 础 涉 及 广 泛 ， 应 用 还 得 自 己 多 加 练 习 ， 这 是 慕 课 上 的 第 一 门 课 程 ， 期 待 申 请 到 人 生 的 第 一 本 证 书 （ 除 了 毕 业 证 和 学 位 证 ） ， 让 我 开 启 我 的 开 挂 人 生 。 哈 哈 … …,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O,2 2 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 1 1 1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1
老 师 讲 解 的 非 常 好 ， 课 程 安 排 也 很 合 理,B-ASP I-ASP B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
北 理 的 P y t h o n 课 程 我 学 习 第 二 次 了 ， 感 觉 越 来 越 讲 解 思 路 清 晰 ， 让 大 家 学 习 起 来 更 有 积 极 性 ， 非 常 好 ， 表 白 嵩 老 师 ！,B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O,2 2 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
慕 课 很 好 用 ， 很 方 便 ， 要 是 手 机 端 也 可 以 自 动 播 放 下 一 段 就 更 好 了,B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 授 通 俗 易 懂 ， 最 喜 欢 的 是 每 章 附 带 的 编 程 实 例 ， 很 有 意 思 ， 给 本 来 枯 燥 的 课 程 增 添 了 色 彩 。,B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1
老 师 讲 解 条 理 清 晰 ， 理 论 和 实 例 相 结 合 ， 非 常 适 合 初 学 者 学 习 。,B-ASP I-ASP B-ASP I-ASP B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O,2 2 2 2 2 2 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1
讲 的 非 常 基 础 ， 零 基 础 的 同 学 也 能 跟 得 上,B-ASP O O O O O O O B-ASP I-ASP O O O O O O O O,2 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1
这 个 课 程 安 排 非 常 棒 ， 由 浅 入 深 ， 入 门 学 习 者 虽 然 有 地 方 不 明 白 ， 但 也 被 解 决 实 例 问 题 的 兴 趣 推 着 朝 前 进 ！,O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O,-1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1
嵩 老 师 的 讲 解 引 人 入 胜 ， 条 理 清 晰 ， 简 洁 精 辟 。,B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O,1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
内 容 很 好 ， 学 习 和 实 践 相 结 合 ， 学 习 进 度 也 快 ， 可 以 很 快 入 门 。,B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1
嵩 老 师 的 课 简 洁 精 炼 、 通 俗 易 懂 ， 循 序 渐 进 ， 容 易 接 受 。,B-ASP I-ASP I-ASP O B-ASP O O O O O O O O O O O O O O O O O O O O,1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 非 常 好 ， 感 谢 老 师 们 的 辛 苦 付 出 ！,B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 1 1 1 -1 2 2 2 2 -1
可 很 好 ， 只 怪 自 己 不 努 力 ， 学 到 现 在,O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
嵩 天 老 师 的 课 特 别 清 楚 易 懂 ， 而 且 老 师 人 也 特 别 温 柔,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O O O O O O O B-ASP I-ASP O O O O O O,1 1 1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 真 是 太 好 了 ， 一 个 小 白 学 到 了 老 师 讲 的 真 是 太 好 了 ， 一 个 小 白 学 到 了,B-ASP I-ASP B-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
超 棒 ！ 再 多 一 些 实 例 分 析 就 更 好 了,O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1
师 傅 领 进 门 ， 修 行 在 个 人 。 任 重 道 远 任 重 道,B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O,2 2 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 的 很 详 细 ， 而 且 循 序 渐 进 ， 我 曾 经 学 习 过 c J a v a c 再 来 学 习 老 师 的 课 程 感 觉 事 半 功 倍 ， 在 课 上 跟 着 老 师 一 起 打 代 码 可 以 很 快 学 习 掌 握,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP B-ASP I-ASP I-ASP I-ASP B-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP O O O B-ASP I-ASP O O O O O O O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
基 础 知 识 学 习 ， 感 觉 很 棒 。 但 对 于 从 没 有 接 触 过 编 程 的 新 人 来 说 ， 略 有 点 难,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1
慕 课 平 台 的 全 面 开 放 ， 是 全 社 会 的 福 利 。 对 我 们 工 作 已 久 的 人 来 说 ， 这 是 个 非 常 好 的 学 习 平 台 。 北 理 的 这 门 P y t h o n 课 程 也 同 样 很 棒 ， 老 师 讲 的 很 用 心 ， 教 授 方 法 也 容 易 接 受 ， 非 常 有 利 于 实 操 。,B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 1 1 -1 -1 -1 -1 1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
教 学 容 易 理 解 ， 结 合 书 的 内 容 学 习 体 验 非 常 好 ， 老 师 ， 谢 谢 您 ！ ！,B-ASP I-ASP O O O O O O O B-ASP O B-ASP I-ASP B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 1 -1 1 1 2 2 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
由 浅 入 深 ， 循 序 渐 进 ， 很 有 成 就 感 ， 对 有 c 基 础 的 比 较 友 好 ， 不 知 道 没 学 过 编 程 的 小 伙 伴 感 觉 怎 样,O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 -1 -1 -1 -1
老 师 教 的 很 好 ， 点 赞 ， 桃 李 满 天 下,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
跟 着 老 师 学 习 到 第 七 周 的 课 程 了 ， 嵩 天 老 师 讲 课 很 好 ， 如 果 当 初 学 c ＋ ＋ 时 老 师 也 像 这 样 讲 课 ， 现 在 可 能 也 不 会 一 提 起 c 语 言 就 觉 得 很 难 。 嵩 天 老 师 说 只 要 每 周 抽 出 两 小 时 就 能 完 成 学 习 ， 事 实 上 ， 对 我 来 说 ， 看 视 频 ， 做 练 习 题 ， 做 每 章 测 试 题 所 用 的 时 间 远 远 不 止 两 小 时 。 但 不 管 是 看 视 频 还 是 做 题 ， 我 都 觉 得 很 感 兴 趣 ， 觉 得 我 是 能 学 会 编 程 的 。 学 完 这 门 课 后 我 想 继 续 学 习 嵩 天 老 师 的 爬 虫 课 程 ， 一 路 学 习 下 去 。,O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O O O,-1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 1 1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1
课 程 很 好 ， 老 师 讲 课 逻 辑 流 畅 内 容 丰 富 讲 述 方 式 新 颖 有 趣,B-ASP I-ASP O O O B-ASP I-ASP O B-ASP B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 1 1 -1 1 2 2 -1 -1 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1
学 习 到 p y t h o n 的 第 8 周 ， 但 是 还 没 有 开 始 这 一 周 的 内 容 。 之 前 学 习 编 程 之 类 的 太 理 论 了 ， 这 次 运 用 p y t h o n 自 带 的 i d e l 进 行 练 习 ， 但 是 自 己 写 程 序 经 常 报 错 。 课 后 的 p y t h o n 1 2 3 的 习 题 ， 选 择 题 还 好 ， 编 程 题 自 己 苦 苦 思 考 了 半 天 ， 还 是 0 分 ， 就 很 自 闭 了 吧 。 不 过 有 题 目 促 进 自 己 往 前 赶 进 度 ， 还 是 挺 好 的 事 情 吧 。 在 平 时 学 习 中 ， 每 次 看 见 s h e l l 界 面 的 那 些 错 误 类 型 ， 心 里 面 都 很 累 啊 。 之 前 学 习 过 c 语 言 ， 但 是 也 仅 仅 停 留 在 通 过 考 试 的 程 度 ， 但 是 实 际 应 用 却 一 丁 点 都 不 会 。 现 在 阅 读 很 多 优 秀 的 算 法 ， 但 是 无 从 下 手 。 希 望 自 己 能 够 坚 持 下 来 吧 。,O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
感 谢 嵩 天 老 师 让 我 对 P y t h o n 有 了 很 大 的 学 习 兴 趣,O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP,-1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 2 2
习 题 设 置 的 很 好 ， 跟 着 课 程 走 ， 逐 渐 能 掌 握 一 些 编 程 技 能 ， 很 有 成 就 感,B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP,2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2
学 了 三 周 的 内 容 ， 有 点 收 获 ， 差 距 不 小 ， 跟 着 老 师 听 知 道 是 什 么 ， 还 不 能 举 一 返 三 哟 ， 有 点 难 度 ！,O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 2 2 -1 0 0 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1
学 习 体 验 很 好 ， 很 喜 欢 嵩 天 老 师 的 讲 课,B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,2 2 2 2 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 2 2
简 约 而 不 简 单 ， 特 别 适 合 入 门 学 习 。,O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1
有 了 学 习 的 资 源 了 ， 这 对 于 没 上 过 9 8 5 的 学 生 是 很 好 的 补 偿,O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP,-1 -1 1 1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 2 2
h a o h a o h a o h a o h a o h a o,O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
所 有 的 知 识 都 是 从 理 念 再 到 实 践 ， 理 解 基 础 架 构 后 ， 才 能 更 对 知 识 点 的 熟 悉 ， P Y T H O N 对 于 如 今 社 会 时 代 发 展 具 有 一 定 的 帮 忙 。,O O O B-ASP I-ASP O O O B-ASP I-ASP O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 1 1 1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2 -1
内 容 涵 盖 较 广 ， 理 论 结 合 实 例 ， 讲 解 容 易 接 受,B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 1 1 -1 -1 1 1 -1 2 2 -1 -1 -1 -1
"特 别 特 别 好 , 案 例 丰 富 讲 解 详 细 ! ! ! ! !",O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1
很 详 细 的 p y t h o n 教 程 ， 对 小 白 来 讲 十 分 友 好 ， 感 谢 m o o c 平 台 能 提 供 这 么 优 质 还 亲 民 的 课 程,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP,-1 -1 -1 -1 1 1 1 1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
嵩 天 老 师 讲 的 很 好 ， 学 完 这 门 p y t h o n ， 我 拥 有 解 决 一 些 实 际 问 题 的 能 力 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
"h a o , h e n h a o , f e i c h a n g h a o",O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
很 棒 学 到 了 很 多 P Y T H O N 相 关 的 知 识,O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2
我 觉 得 老 师 讲 得 很 容 易 理 解 ， 我 收 获 比 较 大,O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
可 以 ， 是 见 过 的 慕 课 中 讲 得 最 好 ！ 但 是 有 些 地 方 较 为 简 单 ， 另 外 ， 对 于 课 外 自 学 的 内 容 需 要 在 课 堂 中 适 当 提 醒 ， 不 然 做 测 试 会 出 问 题 。,O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 0 0 -1
嵩 天 老 师 教 的 很 好 ， 听 得 很 明 白 很 透 彻 ， 内 容 也 层 层 相 扣 ， 推 荐 大 家 来 学 习 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1
讲 的 非 常 系 统 ， 引 用 的 例 子 也 很 棒,O O O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
非 常 好 ， 谢 谢 嵩 天 老 师 团 队 这 么 用 心 的 备 课 。,O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 -1 -1 -1 -1 2 2 -1
可 以 得 ， 跟 着 老 师 学 习 了 基 础 知 识 ， 练 习 了 实 例 ， 进 步 很 大,O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O B-ASP I-ASP O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 1 1 -1 2 2 -1 -1
课 程 基 础 而 深 入 ， 很 感 谢 老 师 的 深 入 浅 出 的 教 学,B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP,2 2 1 1 -1 1 1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2
从 嵩 老 师 讲 课 可 以 感 受 到 一 种 无 私 的 。 。 。 。,O B-ASP I-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O,-1 2 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
哈 哈 哈 哈 主 讲 老 师 贼 帅 ， 讲 课 非 常 详 细 ， 新 手 也 易 于 理 解 ， 很 喜 欢 ！ ！ ！ ！,O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O,-1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 团 队 很 棒 ， 内 容 通 俗 易 懂 ， 不 仅 打 实 基 础 ， 而 且 在 课 程 的 最 后 对 P Y T H O N 的 各 个 方 向 功 能 进 行 了 介 绍 ， 方 便 不 同 的 人 选 择 自 己 的 喜 好 与 目 标 ， 开 始 下 一 步 进 阶 学 习 ！,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
课 程 不 错 ， 但 是 P y t h o n 1 2 3 的 习 题 ， 选 择 题 总 是 找 不 到 ， 这 个 能 不 能 解 决 一 下 ？,B-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O,2 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 0 0 -1 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 课 的 质 量 和 P P T 还 是 不 错 的 ， 只 是 后 面 两 位 老 师 讲 的 有 点 快 有 点 跟 不 上,O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
这 想 知 道 p y t h o n 1 2 3 的 课 程 代 码 是 多 少 ？,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O,-1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 1 1 1 1 -1 -1 -1 -1
老 师 把 知 识 点 都 归 纳 的 很 清 晰 ， 很 赞,B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O O O O O O O,2 2 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 程 内 容 还 不 错 ， 也 比 较 有 吸 引 力 ！,B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1
很 好 ， 就 是 登 录 的 时 候 有 点 麻 烦,O O O O O B-ASP I-ASP O O O O O O O,-1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1
程 序 设 计 让 我 对 数 学 逻 辑 的 认 识 有 了 新 的 认 识 ， 也 为 国 内 有 这 么 优 质 的 教 学 资 源 感 到 高 兴,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 2 2 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
课 程 很 有 用 啊 ， 老 师 讲 的 也 很 好 ， 知 识 点 清 晰 ， 特 别 是 每 次 课 前 复 习 特 别 有 用,B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O,2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1
老 师 的 讲 解 节 奏 和 内 容 都 非 常 适 合 初 学 者 ， 讲 练 结 合 的 形 式 也 能 有 效 的 提 高 学 习 效 率 ， 课 程 中 的 实 例 涉 及 面 广 ， 丰 富 有 趣 。 非 常 推 荐 这 一 套 课 程,B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP,1 1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
嵩 老 师 讲 的 非 常 通 俗 易 懂 ， 有 些 细 节 不 是 很 清 楚 ， 有 的 听 不 懂 。,B-ASP I-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
课 堂 内 容 精 简 ， 形 式 多 样 ， 很 有 意 思,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP,2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2
可 以 看 出 ， 老 师 真 的 是 下 了 很 大 功 夫 来 备 课 ， 充 分 考 虑 到 “ 小 白 ” 的 要 求 ， 是 P y t h o n 必 选 入 门 级 课 程,O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP,-1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 2 2
收 获 满 满 ， 非 常 感 谢 能 有 这 么 好 的 机 会 ！,B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O,2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
授 课 分 小 节 短 时 间 ， 可 以 利 于 碎 片 化 时 间 自 学,B-ASP I-ASP O B-ASP I-ASP B-ASP I-ASP I-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP O O,2 2 -1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 1 1 1 -1 -1
嵩 老 师 ， 讲 的 仔 细 。 易 学 易 懂 ， 很 感 兴 趣 。 通 过 动 手 编 写 实 例 ， 运 行 后 看 到 直 接 效 果 ， 很 有 成 就 感 。 自 己 找 了 一 些 数 学 公 式 和 工 程 问 题 ， 如 海 伦 公 式 ， 九 宫 格 填 数 ， 逐 一 分 析 ， 设 计 程 序 ， 实 现 效 果 。 感 触 数 学 模 型 对 计 算 机 的 发 展 奠 定 了 基 础 。 明 白 了 计 算 思 维 （ 模 块 化 设 计 ， 自 动 化 执 行 ） ， 开 拓 了 解 决 问 题 的 思 路 。 感 谢 P y t h o n 语 言 ， 感 谢 嵩 老 师 。,B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP O,2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 1 1 1 1 -1 -1 1 1 1 1 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 2 2 2 2 2 2 -1 -1 -1 2 2 2 -1
刚 开 始 学 p y t h o n ， 感 觉 老 师 讲 的 很 好 ， 结 构 很 清 晰 。 就 是 如 果 能 多 讲 解 一 些 实 例 就 好 了 ， 做 练 习 的 时 候 ， 有 时 候 觉 得 自 己 会 转 不 过 弯 来,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
让 我 体 验 到 了 学 习 p y t h o n 的 乐 趣,O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2
谢 谢 老 师 ， 提 供 一 个 如 此 好 的 教 学,O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP,-1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2
老 师 讲 的 挺 简 单 ， 但 和 作 业 不 在 一 个 l e v e l ， 小 白 慎 选,O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1
非 常 棒 ， 适 合 完 全 的 小 白 。 嵩 老 师 讲 课 很 有 亲 和 力 ， 期 待 参 加 后 续 的 课 程 ！ 就 是 最 后 一 章 太 快 太 难 了 点 。,O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O B-ASP O O B-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O B-ASP O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2 2 -1 2 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 0 -1 -1 -1 -1 -1 -1 -1
我 是 小 白 ， 老 师 讲 的 深 入 浅 出 ， 很 棒 ！ 基 本 都 能 听 懂 。 谢 谢 老 师 。,O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1
讲 得 非 常 好 ， 将 理 论 与 实 际 相 结 合 ， 让 我 深 刻 理 解,B-ASP O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP,2 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2
太 棒 了 。 感 谢 老 师 的 辛 苦 付 出 ！ ！ ！ 很 有 用,O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 2 2 2 2 -1 -1 -1 -1 -1 -1
深 入 浅 出 ， 面 向 大 众 ， 有 担 待 ， 有 品 质 ， 期 待 你 们 的 进 阶 课 程 。,O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
不 理 解 里 边 的 字 符 串 啥 的,O O O O O O B-ASP I-ASP I-ASP O O,-1 -1 -1 -1 -1 -1 1 1 1 -1 -1
老 师 讲 的 ， 无 论 是 案 例 还 是 编 程 思 维 ， 都 非 常 有 水 平 ， 比 我 之 前 上 过 的 网 课 好 ， 老 师 的 课 绝 对 比 一 些 付 费 培 训 机 构 的 质 量 高,B-ASP I-ASP O O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O B-ASP O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O,1 1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 1 1 -1 2 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1
老 师 讲 的 特 别 好 ！ 适 合 没 有 基 础 的 同 学 ， 期 待 老 师 后 续 补 充 一 些 知 识 ， 如 面 向 对 象 的 p y 很 多 有 c 或 c 基 础 的 同 学 都 等 着 捏,B-ASP I-ASP O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O B-ASP O B-ASP O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 解 很 细 致 ， 一 步 一 步 地 将 我 们 引 入 编 程 的 世 界 。 课 程 设 计 也 逐 步 递 进 ， 由 浅 入 深 。,B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O,1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
对 程 序 小 白 来 说 课 程 很 好 接 受 ， 很 容 易 上 手,O B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O,-1 1 1 1 1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
教 课 方 式 很 好 ， 且 每 次 带 有 复 习 ， 帮 助 我 们 更 好 理 解 P y t h o n ， 特 别 是 对 编 程 的 思 维 模 式 讲 得 还 可 以 。,B-ASP I-ASP I-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O O O O O O,2 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 1 1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1
这 个 课 程 最 不 一 样 的 就 是 ， 这 不 愧 是 北 京 理 工 大 学 教 P Y T H O N 的 讲 师 。 每 节 课 程 本 身 都 有 强 烈 的 新 鲜 感 。 做 到 这 点 是 需 要 花 费 很 多 心 思 的 ， 感 谢 讲 师 。,O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP O O O O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 2 2 -1
课 程 免 费 ， 有 配 套 作 业 帮 助 强 化 ， 老 师 讲 课 也 通 俗 易 懂 ， 很 赞,B-ASP I-ASP O O O O B-ASP I-ASP I-ASP I-ASP O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O,2 2 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1
我 很 喜 欢 这 个 课 程 的 老 师 ， 讲 解 的 非 常 细 致 ， 对 于 小 白 来 讲 也 是 能 够 快 速 上 手,O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O O O O O O O O,-1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
讲 的 比 较 复 杂 ， 没 有 其 他 的 网 络 上 的 教 程 通 俗 易 懂,O O O O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 -1 2 2 -1 -1 -1 -1
嵩 天 老 师 的 课 真 的 是 专 业 又 不 失 风 趣 ， 总 能 将 复 杂 难 懂 的 知 识 通 过 引 人 入 胜 的 方 式 让 我 们 学 会 ， 真 的 是 国 家 精 品 了 ！ ！ ！ ！,B-ASP I-ASP I-ASP I-ASP O B-ASP O O O B-ASP I-ASP O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O O O,2 2 2 2 -1 2 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 -1 -1
非 常 好 ， 像 我 这 样 零 基 础 的 小 白 都 可 以 跟 上 课 程 进 度 。,O O O O O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1
这 个 课 程 太 好 了 吧 ， 老 师 讲 的 真 的 好 ， 后 期 非 常 精 致 ， 真 的 国 家 精 品 课 程 。,O O B-ASP I-ASP O O O O O B-ASP I-ASP O O O O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O,-1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 -1
老 师 讲 的 真 的 很 好 ， 很 多 东 西 都 是 通 俗 易 懂,B-ASP I-ASP O O O O O O O O O B-ASP I-ASP O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 清 晰 细 致 ， 学 到 了 很 多,B-ASP I-ASP O O O O O O O O O O O O O,1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
炒 很 适 合 小 白 跟 着 学 ， 讲 的 通 熟 易 懂 ， 超 好,O O O O B-ASP I-ASP O O O O B-ASP O O O O O O O O,-1 -1 -1 -1 2 2 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1
老 师 讲 的 很 好 ， 后 面 有 没 有 进 阶 的 课 啊 ， 还 想 接 着 学 。,B-ASP I-ASP B-ASP O O O O O O O O O O O O B-ASP O O O O O O O O,1 1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 -1 -1 -1 -1 -1 -1 -1 -1
很 好 ， 案 例 带 入 ， 有 很 强 的 实 践 性,O O O B-ASP I-ASP O O O O O O O B-ASP I-ASP I-ASP,-1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 2 2 2
单 元 设 置 合 理 ， 内 容 安 排 恰 当 ， 题 型 难 度 适 中 。 单 元 设 置 合 理 ， 内 容 安 排 恰 当 ， 题 型 难 度 适 中 。,B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP O O O,2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 2 2 -1 -1 -1
重 开 始 的 对 p y t h o n 什 么 都 不 懂 到 会 使 用 基 本 的 p y t h o n 语 言,O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP,-1 -1 -1 -1 -1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 1 1
好 好 好 好 好 不 好 呢 ？ 我 也 不 知 道 ！,O O O O O O O O O O O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
容 易 理 解 ， 很 清 晰 ， 老 师 思 路 缜 密 ， 是 入 门 的 好 课 程 ， 感 恩,O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O B-ASP I-ASP O O O,-1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
挺 好 的 ， 加 油 ， 增 加 自 己 的 见 识 与 能 力 。,O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 2 2 -1
老 师 的 课 讲 的 很 生 动 ， 循 序 渐 进 ， 收 获 很 大 。,B-ASP I-ASP O B-ASP O O O O O O O O O O O B-ASP I-ASP O O O,1 1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1
早 就 听 说 P Y T I O N 编 程 课 了 ， 但 一 直 未 涉 及 ， 现 在 放 假 了 ， 计 划 把 它 攻 下 来 。,O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP B-ASP I-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O O,-1 -1 -1 -1 1 1 1 1 1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1
连 续 三 周 的 学 习 ， 第 一 次 课 就 打 破 了 我 多 年 以 来 对 编 程 的 恐 惧 感 ， 第 二 次 课 和 第 三 次 课 进 一 步 激 发 了 我 对 P y t h o n 语 言 的 兴 趣 ， 暗 下 决 心 一 定 掌 握 这 门 语 言 ， 感 谢 嵩 天 老 师 的 精 彩 讲 授 、 感 谢 精 美 的 P P T ！,O O O O O O O O O O O B-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP I-ASP O O O O B-ASP O O O O B-ASP O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O O O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 0 0 -1 -1 -1 -1 2 -1 -1 -1 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 -1 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1
非 常 棒 ， 我 特 别 喜 欢 嵩 天 师 得 讲 课 方 式 。,O O O O O O O O O B-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 -1 2 2 2 2 -1
老 师 教 得 很 棒 ， 就 是 视 频 切 割 得 太 碎 了 ， 尽 管 有 自 动 播 放 下 一 段 视 频 功 能 ， 但 仍 旧 让 人 抓 狂 。 很 打 断 注 意 力 。 如 果 是 文 科 类 ， 这 么 切 割 编 排 还 行 。 1 0 小 时 总 共 播 放 了 2 0 0 多 个 视 频 ， 这 跳 转 。 。 。 体 会 一 下,B-ASP I-ASP O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O B-ASP I-ASP I-ASP O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP B-ASP I-ASP O O O O O O O O O O O O O O O O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O,2 2 -1 -1 -1 -1 -1 -1 -1 0 0 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 0 0 0 -1 -1 -1 -1 1 1 1 -1 -1 -1 1 1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1
改 变 从 现 在 开 始 ， 一 起 享 受 编 程 的 乐 趣,O O O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP,-1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 2 2
喜 欢 慕 课 这 个 平 台 ， 提 供 我 们 ， 不 受 时 间 ， 空 间 ， 年 龄 的 限 制 ， 让 我 们 可 以 按 需 ， 按 爱 好 ， 尽 情 选 择 ， 学 习 ， 真 正 实 现 了 海 纳 百 川 有 容 乃 大 ， 知 识 的 无 价 ， 学 到 老 ， 活 到 老 ， 希 望 有 更 多 这 样 精 品 的 平 台 ， 促 进 全 民 学 习 和 理 想 的 碰 撞 ，,O O B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O B-ASP I-ASP O O O B-ASP I-ASP O B-ASP I-ASP O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O O O O O O O O O O O O O O O O O O B-ASP I-ASP O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O,-1 -1 2 2 -1 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 1 1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1 -1 -1 -1 2 2 -1 2 2 -1 -1 -1 -1
不 错 不 错 就 是 有 些 内 容 不 适 合 新 手,O O O O O O O O B-ASP I-ASP O O O O O,-1 -1 -1 -1 -1 -1 -1 -1 0 0 -1 -1 -1 -1 -1
自 从 看 了 嵩 老 师 的 课 ， 计 算 机 二 级 过 了,O O O O B-ASP I-ASP I-ASP O B-ASP O B-ASP I-ASP I-ASP I-ASP I-ASP O O,-1 -1 -1 -1 1 1 1 -1 2 -1 1 1 1 1 1 -1 -1
老 师 讲 课 很 详 细 ， 我 也 学 到 了 很 多 关 于 P y t h o n 的 知 识 ， 很 满 足 。,B-ASP I-ASP O B-ASP O O O O O O O O O O O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O,2 2 -1 2 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 1 1 1 1 -1 2 2 -1 -1 -1 -1 -1
课 程 分 为 小 结 ， 这 样 学 一 节 ， 练 一 节 ， 效 果 很 好,B-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O O B-ASP I-ASP O O,2 2 -1 -1 1 1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 -1 -1
学 起 来 很 轻 松 ， 很 有 学 习 体 验 ， 收 货 也 很 多 ， 感 谢 嵩 老 师 ， 感 谢 北 京 理 工 大 学 的 制 作 团 队 ！,O O O O O O O O O B-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP O O O O O O B-ASP I-ASP I-ASP O O O B-ASP I-ASP I-ASP I-ASP I-ASP I-ASP O B-ASP I-ASP I-ASP I-ASP O,-1 -1 -1 -1 -1 -1 -1 -1 -1 2 2 2 2 -1 2 2 -1 -1 -1 -1 -1 -1 2 2 2 -1 -1 -1 2 2 2 2 2 2 -1 2 2 2 2 -1
教 学 形 式 许 多 课 程 还 是 很 老 套 ， 可 以 获 得 证 书 的 学 习 只 能 在 上 学 时 间 （ 有 学 习 时 间 规 定 ）,B-ASP I-ASP I-ASP I-ASP O O B-ASP I-ASP O O O O O O O O O O B-ASP I-ASP O B-ASP I-ASP O O O O O B-ASP I-ASP O O B-ASP I-ASP I-ASP I-ASP O O O,0 0 0 0 -1 -1 0 0 -1 -1 -1 -1 -1 -1 -1 -1 -1 -1 1 1 -1 0 0 -1 -1 -1 -1 -1 0 0 -1 -1 0 0 0 0 -1 -1 -1

from utils import *
import torch.utils.data as data
from model import Model
import numpy as np
import os
from torch.utils.tensorboard import SummaryWriter
import torch.nn.functional as F
from torch.optim.lr_scheduler import ReduceLROnPlateau, CosineAnnealingLR
from torch.optim import <PERSON><PERSON>


def migrate_to_device(batch_data, device):
    """将数据批量迁移到指定设备"""
    try:
        return [data.to(device) for data in batch_data]
    except RuntimeError as e:
        print(f"数据迁移到{device}失败: {str(e)}")
        print("可能是显存不足，尝试减小batch_size或数据大小")
        raise
    except Exception as e:
        print(f"数据迁移过程发生未知错误: {str(e)}")
        raise


def train_epoch(model, train_loader, optimizer, epoch, writer=None):
    model.train()
    total_loss = 0
    ent_loss_total = 0  # 实体识别损失
    pola_loss_total = 0  # 情感分析损失
    correct_cnt = pred_cnt = 0

    for b, batch in enumerate(train_loader):
        input_ids, mask, ent_label, ent_cdm, ent_cdw, pola_label, pairs = batch

        # 确保不超过BERT最大长度限制
        if input_ids.size(1) > BERT_MAX_LEN:
            input_ids = input_ids[:, :BERT_MAX_LEN]
            mask = mask[:, :BERT_MAX_LEN]
            ent_label = ent_label[:, :BERT_MAX_LEN]
            ent_cdm = ent_cdm[:, :BERT_MAX_LEN]
            ent_cdw = ent_cdw[:, :BERT_MAX_LEN]
            pairs = [(p[0], p[1]) for p in pairs if p[0] < BERT_MAX_LEN]

        try:
            # 使用改进的数据迁移函数
            input_ids, mask, ent_label, ent_cdm, ent_cdw, pola_label = migrate_to_device(
                [input_ids, mask, ent_label, ent_cdm, ent_cdw, pola_label],
                DEVICE
            )

            # 前向传播
            _ = model.get_entity(input_ids, mask)  # 实体识别结果不直接使用
            pred_pola = model.get_pola(input_ids, mask, ent_cdm, ent_cdw)

            # 分别计算实体识别和情感分析的损失
            ent_loss = model.ent_loss_fn(input_ids, ent_label, mask)
            pola_loss = model.pola_loss_fn(pred_pola, pola_label)

            # 总损失 - 使用加权损失：从配置中读取权重
            loss = TASK_WEIGHT_ENT * ent_loss + TASK_WEIGHT_POLA * pola_loss

            # 累加各类损失
            total_loss += loss.item()
            ent_loss_total += ent_loss.item()
            pola_loss_total += pola_loss.item()

            # 反向传播前清空梯度
            optimizer.zero_grad()
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()

            # 每20个batch打印一次损失
            if b % 20 == 0:
                print(f'>> epoch {epoch} batch: {b} loss: {loss.item():.4f}')
                # TensorBoard记录已移除
                # 不再记录train/batch_loss、gradients和weights

            # 统计正确预测数
            with torch.no_grad():
                pred_pola_labels = torch.argmax(pred_pola, dim=1)
                correct_cnt += (pred_pola_labels == pola_label).sum().item()
                pred_cnt += len(pred_pola_labels)

        except RuntimeError as e:
            if "out of memory" in str(e):
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                print(f"GPU显存不足，跳过当前batch: {str(e)}")
                continue
            else:
                raise e

    # 计算关键指标
    avg_ent_loss = ent_loss_total / len(train_loader)
    avg_pola_loss = pola_loss_total / len(train_loader)

    # 计算总体Loss（使用与总体F1相同的权重）
    total_loss = calculate_overall_loss(avg_ent_loss, avg_pola_loss)

    accuracy = correct_cnt / pred_cnt if pred_cnt > 0 else 0

    # 记录每个训练轮次的指标
    if writer is not None:
        # 记录总体指标
        writer.add_scalar('train/epoch_loss', total_loss, epoch)  # 加权总体损失
        writer.add_scalar('train/accuracy', accuracy, epoch)

        # 记录分开的损失
        writer.add_scalar('train/ent_loss', avg_ent_loss, epoch)  # 实体识别损失
        writer.add_scalar('train/pola_loss', avg_pola_loss, epoch)  # 情感分析损失

    return {
        'loss': total_loss,  # 加权总体损失
        'ent_loss': avg_ent_loss,
        'pola_loss': avg_pola_loss,
        'accuracy': round(accuracy, 3)
    }


def evaluate(model, loader, epoch=None, writer=None, save_best_cm=False):
    """验证阶段保持详细的评估指标

    Args:
        model: 模型
        loader: 数据加载器
        epoch: 当前轮次
        writer: TensorBoard写入器
        save_best_cm: 是否保存最佳混淆矩阵，默认为False
    """
    model.eval()
    total_loss = 0
    ent_loss_total = 0  # 实体识别损失
    pola_loss_total = 0  # 情感分析损失
    all_true_labels = []
    all_pred_labels = []

    # 用于实体识别混淆矩阵
    all_true_ent_labels = []
    all_pred_ent_labels = []

    try:
        with torch.no_grad():
            for batch in loader:
                input_ids, mask, ent_label, ent_cdm, ent_cdw, pola_label, pairs = batch

                # 数据长度检查和截断
                if input_ids.size(1) > BERT_MAX_LEN:
                    input_ids = input_ids[:, :BERT_MAX_LEN]
                    mask = mask[:, :BERT_MAX_LEN]
                    ent_label = ent_label[:, :BERT_MAX_LEN]
                    ent_cdm = ent_cdm[:, :BERT_MAX_LEN]
                    ent_cdw = ent_cdw[:, :BERT_MAX_LEN]
                    pairs = [(p[0], p[1])
                             for p in pairs if p[0] < BERT_MAX_LEN]

                # 使用改进的数据迁移函数
                input_ids, mask, ent_label, ent_cdm, ent_cdw, pola_label = migrate_to_device(
                    [input_ids, mask, ent_label, ent_cdm, ent_cdw, pola_label],
                    DEVICE
                )

                # 前向传播
                pred_ent_label = model.get_entity(
                    input_ids, mask)  # 获取实体识别预测结果
                pred_pola = model.get_pola(input_ids, mask, ent_cdm, ent_cdw)

                # 分别计算实体识别和情感分析的损失
                ent_loss = model.ent_loss_fn(input_ids, ent_label, mask)
                pola_loss = model.pola_loss_fn(pred_pola, pola_label)

                # 总损失 - 使用加权损失：从配置中读取权重
                loss = TASK_WEIGHT_ENT * ent_loss + TASK_WEIGHT_POLA * pola_loss

                # 累加各类损失
                total_loss += loss.item()
                ent_loss_total += ent_loss.item()
                pola_loss_total += pola_loss.item()

                # 收集情感分析预测结果
                pred_labels = torch.argmax(pred_pola, dim=1).cpu().numpy()
                true_labels = pola_label.cpu().numpy()

                all_true_labels.extend(true_labels)
                all_pred_labels.extend(pred_labels)

                # 收集实体识别预测结果
                # 将CRF预测结果和真实标签展平，只保留有效部分（非padding）
                for pred, true, m in zip(pred_ent_label, ent_label, mask):
                    valid_len = m.sum().item()  # 有效长度（非padding部分）
                    all_pred_ent_labels.extend(pred[:valid_len])
                    all_true_ent_labels.extend(true[:valid_len].cpu().numpy())

        # 计算情感分析评估指标
        sentiment_metrics = calculate_metrics(
            all_true_labels, all_pred_labels, metric_type='sentiment')

        # 计算实体识别评估指标
        entity_metrics = calculate_metrics(
            all_true_ent_labels, all_pred_ent_labels, labels=[0, 1, 2], metric_type='entity')

        # 计算总体Macro F1
        overall_f1 = calculate_overall_f1(entity_metrics, sentiment_metrics)

        # 合并指标
        metrics = {}
        metrics.update(sentiment_metrics)
        metrics.update(entity_metrics)
        metrics['overall_f1'] = overall_f1

        # 计算平均损失
        avg_ent_loss = ent_loss_total / len(loader)  # 实体识别损失
        avg_pola_loss = pola_loss_total / len(loader)  # 情感分析损失

        # 计算总体Loss（使用与总体F1相同的权重）
        total_loss = calculate_overall_loss(avg_ent_loss, avg_pola_loss)

        # 将损失添加到指标字典中
        metrics['loss'] = total_loss  # 总体损失（加权平均）
        metrics['ent_loss'] = avg_ent_loss  # 实体识别损失
        metrics['pola_loss'] = avg_pola_loss  # 情感分析损失

        # 记录验证指标到TensorBoard
        if writer is not None and epoch is not None:
            # 记录总体指标
            writer.add_scalar('val/loss', metrics['loss'], epoch)  # 加权总体损失
            writer.add_scalar('val/accuracy', metrics['accuracy'], epoch)
            writer.add_scalar('val/overall_f1',
                              metrics['overall_f1'], epoch)  # 总体Macro F1

            # 记录实体识别指标
            writer.add_scalar('val/entity_macro_f1',
                              # 实体识别Macro F1
                              metrics['entity_macro_f1'], epoch)

            # 记录情感分析指标
            writer.add_scalar('val/sentiment_macro_f1',
                              # 情感分析Macro F1
                              metrics['sentiment_macro_f1'], epoch)
            writer.add_scalar(
                'val/macro_f1', metrics['macro_f1'], epoch)  # 保留原来的指标名称
            writer.add_scalar('val/macro_precision',
                              metrics['macro_precision'], epoch)
            writer.add_scalar('val/macro_recall',
                              metrics['macro_recall'], epoch)

            # 记录分开的损失
            writer.add_scalar(
                'val/ent_loss', metrics['ent_loss'], epoch)  # 实体识别损失
            writer.add_scalar(
                'val/pola_loss', metrics['pola_loss'], epoch)  # 情感分析损失

            # 添加情感分析混淆矩阵可视化
            if len(all_true_labels) > 0 and len(all_pred_labels) > 0:
                cm = confusion_matrix(all_true_labels, all_pred_labels)
                fig = plot_confusion_matrix_for_tensorboard(
                    cm, class_names=POLA_MAP)
                writer.add_figure('val/sentiment_confusion_matrix', fig, epoch)

                # 添加归一化的情感分析混淆矩阵
                cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
                fig_norm = plot_confusion_matrix_for_tensorboard(
                    cm_norm, class_names=POLA_MAP)
                writer.add_figure(
                    'val/sentiment_confusion_matrix_normalized', fig_norm, epoch)

                # 如果需要保存最佳混淆矩阵，则添加到best目录下
                if save_best_cm:
                    # 标准情感混淆矩阵
                    best_fig = plot_confusion_matrix_for_tensorboard(
                        cm, class_names=POLA_MAP, title="Best Model Sentiment Confusion Matrix")
                    writer.add_figure(
                        'best/sentiment_confusion_matrix', best_fig, epoch)

                    # 归一化情感混淆矩阵
                    best_fig_norm = plot_confusion_matrix_for_tensorboard(
                        cm_norm, class_names=POLA_MAP, title="Best Model Normalized Sentiment Confusion Matrix")
                    writer.add_figure(
                        'best/sentiment_confusion_matrix_normalized', best_fig_norm, epoch)

            # 添加实体识别混淆矩阵可视化
            if len(all_true_ent_labels) > 0 and len(all_pred_ent_labels) > 0:
                # 计算实体识别的混淆矩阵
                ent_cm = confusion_matrix(
                    all_true_ent_labels, all_pred_ent_labels, labels=[0, 1, 2])
                ent_fig = plot_confusion_matrix_for_tensorboard(
                    ent_cm, class_names=ENT_MAP)
                writer.add_figure(
                    'val/entity_confusion_matrix', ent_fig, epoch)

                # 添加归一化的实体识别混淆矩阵
                ent_cm_norm = ent_cm.astype(
                    'float') / ent_cm.sum(axis=1)[:, np.newaxis]
                ent_fig_norm = plot_confusion_matrix_for_tensorboard(
                    ent_cm_norm, class_names=ENT_MAP)
                writer.add_figure(
                    'val/entity_confusion_matrix_normalized', ent_fig_norm, epoch)

                # 如果需要保存最佳混淆矩阵，则添加到best目录下
                if save_best_cm:
                    # 标准实体混淆矩阵
                    best_ent_fig = plot_confusion_matrix_for_tensorboard(
                        ent_cm, class_names=ENT_MAP, title="Best Model Entity Confusion Matrix")
                    writer.add_figure(
                        'best/entity_confusion_matrix', best_ent_fig, epoch)

                    # 归一化实体混淆矩阵
                    best_ent_fig_norm = plot_confusion_matrix_for_tensorboard(
                        ent_cm_norm, class_names=ENT_MAP, title="Best Model Normalized Entity Confusion Matrix")
                    writer.add_figure(
                        'best/entity_confusion_matrix_normalized', best_ent_fig_norm, epoch)

        return metrics

    except Exception as e:
        print(f"评估过程发生错误: {str(e)}")
        raise


if __name__ == '__main__':
    try:
        # 设置随机种子确保可重复性
        torch.manual_seed(42)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(42)

        writer = SummaryWriter(TENSORBOARD_DIR)
        print(f"TensorBoard日志已初始化，路径: {TENSORBOARD_DIR}")
        print(f"运行 'tensorboard --logdir={TENSORBOARD_DIR}' 来启动TensorBoard")

        # 将config.py复制到MODEL_DIR目录中，以保存训练配置
        import shutil
        config_path = os.path.join(os.path.dirname(
            os.path.abspath(__file__)), 'config.py')
        config_dest = os.path.join(MODEL_DIR, 'config.py')
        shutil.copy2(config_path, config_dest)
        print(f"已将配置文件复制到: {config_dest}")

        model = Model().to(DEVICE)

        # 获取不同组件的参数
        bert_params = model.get_bert_lora_params()
        entity_params = model.get_entity_params()
        pola_params = model.get_pola_params()

        # 打印LoRA配置信息
        if LORA_ENABLED:
            print("\nLoRA微调已启用:")
            print(f"  秩参数(r): {LORA_R}")
            print(f"  缩放参数(alpha): {LORA_ALPHA}")
            print(f"  Dropout率: {LORA_DROPOUT}")
            print(f"  目标模块: {LORA_TARGET_MODULES}")
        else:
            print("\nLoRA微调未启用")

        # 打印优化器配置信息
        print("\n优化器配置:")
        print(f"  类型: AdamW")
        print(f"  BERT参数学习率: {LR_BERT}")
        print(f"  实体识别学习率: {LR_ENT}")
        print(f"  情感分析学习率: {LR_POLA}")
        print(f"  BERT参数权重衰减率: {WEIGHT_DECAY_BERT}")
        print(f"  实体识别权重衰减率: {WEIGHT_DECAY_ENT}")
        print(f"  情感分析权重衰减率: {WEIGHT_DECAY_POLA}")

        # 打印学习率调度器配置信息
        print("\n学习率调度器配置:")
        print(f"  类型: {LR_SCHEDULER_TYPE}")
        if LR_SCHEDULER_TYPE == 'cosine':
            print(f"  半周期长度(T_max): {COSINE_T_MAX} epochs")
            print(f"  最小学习率(eta_min): {COSINE_ETA_MIN}")
        elif LR_SCHEDULER_TYPE == 'plateau':
            print(f"  学习率衰减因子: {LR_FACTOR}")
            print(f"  耐心值: {LR_PATIENCE} epochs")
            print(f"  改善阈值: {LR_THRESHOLD}")
            print(f"  最小学习率: {LR_MIN}")

        # 打印CRF类别权重配置信息
        if CRF_CLASS_WEIGHTS_ENABLED:
            print("\nCRF类别权重已启用:")
            print(f"  O标签权重: {CRF_CLASS_WEIGHTS[BIO_O_ID]:.2f}")
            print(f"  B-ASP标签权重: {CRF_CLASS_WEIGHTS[BIO_B_ID]:.2f}")
            print(f"  I-ASP标签权重: {CRF_CLASS_WEIGHTS[BIO_I_ID]:.2f}")
        else:
            print("\nCRF类别权重未启用")

        # 打印情感分析类别权重配置信息
        if POLA_CLASS_WEIGHTS_ENABLED:
            print("\n情感分析类别权重已启用:")
            print(f"  Negative类别权重: {POLA_CLASS_WEIGHTS[0]:.2f}")
            print(f"  Neutral类别权重: {POLA_CLASS_WEIGHTS[1]:.2f}")
            print(f"  Positive类别权重: {POLA_CLASS_WEIGHTS[2]:.2f}")
        else:
            print("\n情感分析类别权重未启用")

        # 打印参数组信息
        # print(
        #     f"BERT LoRA参数数量: {sum(p.numel() for p in bert_params)}")
        # print(f"实体识别参数数量: {sum(p.numel() for p in entity_params)}")
        # print(f"情感分析参数数量: {sum(p.numel() for p in pola_params)}")

        # 为不同组件设置不同的学习率和权重衰减
        param_groups = [
            {'params': bert_params, 'lr': LR_BERT,
                'weight_decay': WEIGHT_DECAY_BERT},  # LoRA参数
            {'params': entity_params, 'lr': LR_ENT,
                'weight_decay': WEIGHT_DECAY_ENT},  # 实体识别
            {'params': pola_params, 'lr': LR_POLA,
                'weight_decay': WEIGHT_DECAY_POLA}    # 情感分析
        ]

        # 使用AdamW优化器，它在权重衰减方面比Adam更稳定
        optimizer = AdamW(param_groups)

        # 初始化学习率调度器
        schedulers = {}
        if LR_SCHEDULER_ENABLED:
            # 根据配置选择学习率调度器类型
            if LR_SCHEDULER_TYPE == 'plateau':
                # 使用ReduceLROnPlateau调度器（基于验证集指标调整学习率）
                if LR_SCHEDULER_SEPARATE:
                    # 为实体识别和情感分析任务分别创建独立的学习率调度器
                    # 实体识别学习率调度器 - 只监控实体识别验证损失
                    schedulers['entity'] = ReduceLROnPlateau(
                        optimizer,
                        mode='min',  # 监控验证损失，损失减小时认为是改善
                        factor=LR_ENT_FACTOR,  # 学习率衰减因子
                        patience=LR_ENT_PATIENCE,  # 学习率调整耐心值
                        threshold=LR_ENT_THRESHOLD,  # 判断是否改善的阈值
                        min_lr=LR_ENT_MIN,  # 学习率下限
                        cooldown=LR_COOLDOWN,  # 学习率调整后的冷却期
                        threshold_mode='rel'  # 相对阈值模式
                    )
                    # 情感分析学习率调度器 - 只监控情感分析验证损失
                    schedulers['sentiment'] = ReduceLROnPlateau(
                        optimizer,
                        mode='min',  # 监控验证损失，损失减小时认为是改善
                        factor=LR_POLA_FACTOR,  # 学习率衰减因子
                        patience=LR_POLA_PATIENCE,  # 学习率调整耐心值
                        threshold=LR_POLA_THRESHOLD,  # 判断是否改善的阈值
                        min_lr=LR_POLA_MIN,  # 学习率下限
                        cooldown=LR_COOLDOWN,  # 学习率调整后的冷却期
                        threshold_mode='rel'  # 相对阈值模式
                    )
                    print("已启用独立的ReduceLROnPlateau学习率调度器，将根据各自的验证损失动态调整实体识别和情感分析的学习率")
                else:
                    # 使用单一学习率调度器 - 监控总体验证损失
                    schedulers['global'] = ReduceLROnPlateau(
                        optimizer,
                        mode='min',  # 监控验证损失，损失减小时认为是改善
                        factor=LR_FACTOR,  # 学习率衰减因子
                        patience=LR_PATIENCE,  # 学习率调整耐心值
                        threshold=LR_THRESHOLD,  # 判断是否改善的阈值
                        min_lr=LR_MIN,  # 学习率下限
                        cooldown=LR_COOLDOWN  # 学习率调整后的冷却期
                    )
                    print("已启用ReduceLROnPlateau学习率调度器，将根据总体验证损失动态调整学习率")
            elif LR_SCHEDULER_TYPE == 'cosine':
                # 使用余弦退火学习率调度器（基于epoch调整学习率）
                # 简化余弦退火调度器的创建，始终使用单一调度器
                schedulers['global'] = CosineAnnealingLR(
                    optimizer,
                    T_max=COSINE_T_MAX,  # 半周期长度
                    eta_min=COSINE_ETA_MIN,  # 最小学习率
                    last_epoch=COSINE_LAST_EPOCH  # 上次运行的最后一个epoch
                )
                print(
                    f"已启用余弦退火学习率调度器，半周期长度: {COSINE_T_MAX} epochs，最小学习率: {COSINE_ETA_MIN}")
            else:
                print(f"警告: 未知的学习率调度器类型 '{LR_SCHEDULER_TYPE}'，将不使用学习率调度器")

            # 检查是否有检查点文件，如果有则加载学习率调度器状态
            checkpoint_path = os.path.join(MODEL_DIR, 'checkpoint.pth')
            if os.path.exists(checkpoint_path):
                try:
                    checkpoint = torch.load(
                        checkpoint_path, map_location=DEVICE)
                    if 'scheduler_state_dicts' in checkpoint:
                        for scheduler_name, state_dict in checkpoint['scheduler_state_dicts'].items():
                            if scheduler_name in schedulers:
                                schedulers[scheduler_name].load_state_dict(
                                    state_dict)
                        print("从检查点文件加载学习率调度器状态成功")
                except Exception as e:
                    print(f"加载学习率调度器状态失败: {str(e)}")

        train_dataset = Dataset('train')
        val_dataset = Dataset('val')
        train_loader = data.DataLoader(train_dataset, batch_size=BATCH_SIZE,
                                       shuffle=True, collate_fn=train_dataset.collate_fn)
        val_loader = data.DataLoader(val_dataset, batch_size=BATCH_SIZE,
                                     shuffle=False, collate_fn=val_dataset.collate_fn)

        # 记录模型结构到TensorBoard
        dummy_input = torch.zeros(1, BERT_MAX_LEN, dtype=torch.long).to(DEVICE)
        dummy_mask = torch.ones(1, BERT_MAX_LEN, dtype=torch.bool).to(DEVICE)
        dummy_cdm = torch.zeros(1, BERT_MAX_LEN, dtype=torch.float).to(DEVICE)
        dummy_cdw = torch.zeros(1, BERT_MAX_LEN, dtype=torch.float).to(DEVICE)

        # 添加模型图
        try:
            writer.add_graph(
                model, (dummy_input, dummy_mask, dummy_cdm, dummy_cdw))
        except Exception as e:
            print(f"添加模型图失败: {str(e)}")

        best_val_f1 = 0
        best_val_loss = float('inf')
        best_f1_epoch = 0
        best_loss_epoch = 0
        # 从配置中读取早停耐心值
        patience = PATIENCE
        # 记录连续没有改善的epoch数
        no_improvement_count = 0

        for e in range(EPOCH):
            # 训练阶段
            model.train()
            train_metrics = train_epoch(
                model, train_loader, optimizer, e, writer)

            # 验证阶段
            model.eval()
            val_metrics = evaluate(model, val_loader, e, writer)

            # 打印指标
            print(f'\nEpoch {e} Training metrics:')
            print(f'\tTotal Loss: {train_metrics["loss"]:.4f}')
            print(f'\tEntity Loss: {train_metrics["ent_loss"]:.4f}')
            print(f'\tSentiment Loss: {train_metrics["pola_loss"]:.4f}')
            print(f'\tAccuracy: {train_metrics["accuracy"]:.4f}')

            print(f'\nEpoch {e} Validation metrics:')
            print(f'\tTotal Loss: {val_metrics["loss"]:.4f}')
            print(f'\tEntity Loss: {val_metrics["ent_loss"]:.4f}')
            print(f'\tEntity Macro F1: {val_metrics["entity_macro_f1"]:.4f}')
            print(f'\tSentiment Loss: {val_metrics["pola_loss"]:.4f}')
            print(f'\tSentiment Macro F1: {val_metrics["macro_f1"]:.4f}')
            print(f'\tAccuracy: {val_metrics["accuracy"]:.4f}')
            print(f'\tOverall Macro F1: {val_metrics["overall_f1"]:.4f}')

            # 检查当前epoch是否有任何改善（总体F1或Loss是否超过历史最佳值）
            f1_improved = val_metrics['overall_f1'] > best_val_f1
            loss_improved = val_metrics['loss'] < best_val_loss

            # 早停计数器逻辑
            if f1_improved or loss_improved:
                # 有改善，重置计数器
                no_improvement_count = 0
            else:
                # 没有改善，增加计数器
                no_improvement_count += 1

            # 保存总体F1最佳模型
            if f1_improved:
                best_val_f1 = val_metrics['overall_f1']
                best_f1_epoch = e
                # torch.save(model, MODEL_DIR +
                #            f'best_f1_model_epoch-{e}_f1-{best_val_f1:.4f}.pth')
                # # 删除之前的F1最佳模型
                # for file in os.listdir(MODEL_DIR):
                #     if file.startswith('best_f1_model_') and file != f'best_f1_model_epoch-{e}_f1-{best_val_f1:.4f}.pth':
                #         try:
                #             os.remove(os.path.join(MODEL_DIR, file))
                #         except Exception as err:
                #             print(f"删除旧模型文件失败: {str(err)}")
                print(f"保存新的总体F1最佳模型，总体F1分数: {best_val_f1:.4f}，轮次: {e}")
                print(
                    f"  实体F1: {val_metrics['entity_macro_f1']:.4f}, 情感F1: {val_metrics['macro_f1']:.4f}")

                # 重新评估模型，保存最佳混淆矩阵
                print("保存最佳混淆矩阵数据到TensorBoard...")
                # 使用save_best_cm=True重新评估，以保存最佳混淆矩阵
                _ = evaluate(model, val_loader, e, writer, save_best_cm=True)

            # 保存Loss最佳模型
            if loss_improved:
                best_val_loss = val_metrics['loss']
                best_loss_epoch = e
                # torch.save(
                #     model, MODEL_DIR + f'best_loss_model_epoch-{e}_loss-{best_val_loss:.4f}.pth')
                # # 删除之前的Loss最佳模型
                # for file in os.listdir(MODEL_DIR):
                #     if file.startswith('best_loss_model_') and file != f'best_loss_model_epoch-{e}_loss-{best_val_loss:.4f}.pth':
                #         try:
                #             os.remove(os.path.join(MODEL_DIR, file))
                #         except Exception as err:
                #             print(f"删除旧模型文件失败: {str(err)}")
                print(f"保存新的Loss最佳模型，Loss: {best_val_loss:.4f}，轮次: {e}")

            # 学习率调度器调整
            if LR_SCHEDULER_ENABLED and schedulers:
                if LR_SCHEDULER_TYPE == 'plateau':
                    # ReduceLROnPlateau调度器根据验证指标调整学习率
                    if LR_SCHEDULER_SEPARATE:
                        # 根据实体验证损失调整实体识别任务的学习率
                        schedulers['entity'].step(val_metrics['ent_loss'])
                        # 根据情感验证损失调整情感分析任务的学习率
                        schedulers['sentiment'].step(val_metrics['pola_loss'])
                    else:
                        # 根据总体验证损失调整所有任务的学习率
                        schedulers['global'].step(val_metrics['loss'])
                elif LR_SCHEDULER_TYPE == 'cosine':
                    # 余弦退火调度器根据epoch调整学习率，不需要验证指标
                    schedulers['global'].step()

                # 记录当前学习率到TensorBoard
                if writer is not None:
                    # 记录每个参数组的学习率
                    for i, param_group in enumerate(optimizer.param_groups):
                        writer.add_scalar(
                            f'lr/group_{i}', param_group['lr'], e)

                    # 记录平均学习率
                    avg_lr = sum(
                        pg['lr'] for pg in optimizer.param_groups) / len(optimizer.param_groups)
                    writer.add_scalar('lr/average', avg_lr, e)

                # 打印当前学习率
                # print("\n当前学习率:")
                # print(f"\tBERT层: {optimizer.param_groups[0]['lr']:.8f}")
                # print(f"\t实体识别层: {optimizer.param_groups[1]['lr']:.8f}")
                # print(f"\t情感分析层: {optimizer.param_groups[2]['lr']:.8f}")

            # 用于继续训练的模型保存
            # checkpoint = {
            #     'epoch': e,
            #     'model_state_dict': model.state_dict(),
            #     'optimizer_state_dict': optimizer.state_dict(),
            #     'current_overall_f1': val_metrics['overall_f1'],  # 保存当前轮次的总体F1分数
            #     'current_entity_f1': val_metrics['entity_macro_f1'],  # 保存当前轮次的实体F1分数
            #     'current_sentiment_f1': val_metrics['macro_f1'],  # 保存当前轮次的情感F1分数
            #     'current_loss': val_metrics['loss'],  # 保存当前轮次的Loss
            #     'best_val_f1': best_val_f1,  # 保存历史最佳总体F1
            #     'best_val_loss': best_val_loss,  # 保存历史最佳Loss
            #     'best_f1_epoch': best_f1_epoch,  # 保存最佳F1对应的轮次
            #     'best_loss_epoch': best_loss_epoch  # 保存最佳Loss对应的轮次
            # }

            # 如果启用了学习率调度器，保存其状态
            # if LR_SCHEDULER_ENABLED and schedulers:
            #     scheduler_state_dicts = {}
            #     for name, scheduler in schedulers.items():
            #         scheduler_state_dicts[name] = scheduler.state_dict()
            #     checkpoint['scheduler_state_dicts'] = scheduler_state_dicts

            # torch.save(checkpoint, MODEL_DIR + 'checkpoint.pth')

            # 提前停止检查 - 同时检查F1和Loss是否在patience个epoch内都没有最佳
            if no_improvement_count >= patience:
                print(
                    f'Early stopping at epoch {e}')
                print(
                    f'最佳Loss轮次: {best_loss_epoch}, 最佳Loss: {best_val_loss:.4f}')
                print(f'最佳总体F1轮次: {best_f1_epoch}, 最佳总体F1: {best_val_f1:.4f}')
                break

    except Exception as e:
        print(f"训练过程发生错误: {str(e)}")
        raise
    finally:
        # 清理资源
        if 'writer' in locals():
            writer.close()
            print("TensorBoard写入器已关闭")
        if 'model' in locals():
            del model
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

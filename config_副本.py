import torch
TRAIN_FILE_PATH = './output/process/atepc.sample.train.csv'
VAL_FILE_PATH = './output/process/atepc.sample.val.csv'  # Added validation set path
# TEST_FILE_PATH = './output/process/atepc.sample.test.csv'
TEST_FILE_PATH = './output/process/balanced/atepc.sample.test.csv'

TEST_MODEL_DIR = '/Users/<USER>/EdUHK/IndependentProject/ABSA/output/models/65_dropout-0-cos-6/'
TEST_TENSORBOARD_DIR = TEST_MODEL_DIR + 'test/'

BIO_O_ID = 0
BIO_B_ID = 1
BIO_I_ID = 2
BIO_MAP = {'O': BIO_O_ID, 'B-ASP': BIO_B_ID, 'I-ASP': BIO_I_ID}
ENT_SIZE = 3

# Entity label mapping for confusion matrix visualization
ENT_MAP = ['O', 'B-ASP', 'I-ASP']

POLA_O_ID = -1
POLA_MAP = ['Negative', 'Neutral', 'Positive']
POLA_DIM = 3
# POLA_MAP = ['Negative', 'Positive']
# POLA_DIM = 2

BERT_PAD_ID = 0
BERT_MODEL_NAME = "hfl/chinese-roberta-wwm-ext"
# BERT_MODEL_NAME = "google-bert/bert-base-chinese"
BERT_DIM = 768
BERT_MAX_LEN = 512  # Maximum sequence length for BERT model

SRD = 3  # Semantic-Relative Distance
LCF = 'fusion'  # cdw cdm fusion

BATCH_SIZE = 32
EPOCH = 50
# Early stopping configuration
PATIENCE = 3

MODEL_DIR = '/Users/<USER>/dev/Part2_Pytorch_Bert_Atepc/output/models/'

# BERT model configuration - LoRA parameters
LORA_ENABLED = True  # Whether to enable LoRA fine-tuning
LORA_R = 8  # LoRA rank parameter, determines the dimension of low-rank matrices
LORA_ALPHA = 16  # LoRA scaling parameter, typically set to 2x the rank
LORA_DROPOUT = 0.2  # LoRA dropout rate
LORA_TARGET_MODULES = ['query', 'key', 'value', 'dense']
LORA_BIAS = 'none'  # LoRA bias type, options: 'none', 'all', 'lora_only'

# Learning rate configuration
LR_ENT = 3e-4  # Learning rate for entity recognition task
LR_POLA = 1e-4  # Learning rate for sentiment analysis task
LR_BERT = 5e-4  # Learning rate for LoRA parameters, reduced for better stability

# Weight decay configuration (AdamW optimizer)
WEIGHT_DECAY_BERT = 0.01  # Weight decay rate for LoRA parameters
# Weight decay rate for entity recognition task, reduced to minimize overfitting
WEIGHT_DECAY_ENT = 0.02
# Weight decay rate for sentiment analysis task, reduced to minimize overfitting
WEIGHT_DECAY_POLA = 0.001

# Learning rate scheduler configuration
LR_SCHEDULER_ENABLED = True  # Whether to enable learning rate scheduler
# For cosine annealing scheduler, this setting has no effect as we share a single scheduler instance
# For cosine annealing, recommended to set to False to simplify code
LR_SCHEDULER_SEPARATE = False
# Learning rate scheduler type, options: 'plateau' (ReduceLROnPlateau) or 'cosine' (CosineAnnealingLR)
LR_SCHEDULER_TYPE = 'cosine'

# General learning rate scheduler configuration
# ReduceLROnPlateau scheduler configuration
LR_FACTOR = 0.5  # Learning rate decay factor
LR_PATIENCE = 3  # Patience value for learning rate adjustment, number of epochs without improvement before adjusting learning rate
LR_THRESHOLD = 0.01  # Threshold for determining if validation loss has improved
LR_MIN = 1e-6  # Minimum learning rate
LR_COOLDOWN = 1  # Cooldown period after learning rate adjustment (in epochs)
LR_VERBOSE = True  # Whether to print learning rate adjustment information

# Cosine annealing scheduler configuration
# Half period of cosine cycle in epochs, increased for smoother learning rate changes
COSINE_T_MAX = 15
COSINE_ETA_MIN = 5e-6  # Minimum learning rate
COSINE_LAST_EPOCH = -1  # Last epoch from previous run, -1 means start from scratch

# Entity recognition task learning rate scheduler configuration
LR_ENT_FACTOR = 0.5  # Learning rate decay factor for entity recognition
LR_ENT_PATIENCE = 3  # Patience value for entity recognition learning rate adjustment
# Threshold for determining if validation loss has improved for entity recognition
LR_ENT_THRESHOLD = 0.05
LR_ENT_MIN = 1e-6  # Minimum learning rate for entity recognition

# Sentiment analysis task learning rate scheduler configuration
LR_POLA_FACTOR = 0.5  # Learning rate decay factor for sentiment analysis
LR_POLA_PATIENCE = 3  # Patience value for sentiment analysis learning rate adjustment
# Threshold for determining if validation loss has improved for sentiment analysis
LR_POLA_THRESHOLD = 0.005
LR_POLA_MIN = 1e-6  # Minimum learning rate for sentiment analysis

# Task weight configuration - for calculating overall Loss and overall F1
TASK_WEIGHT_ENT = 0.5  # Weight for entity recognition task
TASK_WEIGHT_POLA = 0.5  # Weight for sentiment analysis task

# CRF class weight configuration
CRF_CLASS_WEIGHTS_ENABLED = True  # Whether to enable CRF class weights
CRF_CLASS_WEIGHTS = {  # Weights for each class
    BIO_O_ID: 0.5,     # Weight for O tag
    BIO_B_ID: 2.0,     # Weight for B-ASP tag
    BIO_I_ID: 2.0      # Weight for I-ASP tag
}

# Sentiment analysis class weight configuration
# Whether to enable sentiment analysis class weights
POLA_CLASS_WEIGHTS_ENABLED = True
POLA_CLASS_WEIGHTS = {  # Weights for each class
    0: 1.0,            # Weight for Negative class
    1: 1.0,            # Weight for Neutral class
    2: 1.0             # Weight for Positive class
}

# Dropout configuration - simplified version
DROPOUT_RATE = 0.2  # Unified dropout rate
DROPOUT_TASK = 0.3  # Dropout rate for task-specific layers

# Evaluation output paths
CONFUSION_MATRIX_PATH = MODEL_DIR + 'confusion_matrix.png'
CONFUSION_MATRIX_NORM_PATH = MODEL_DIR + 'confusion_matrix_normalized.png'

# TensorBoard configuration
TENSORBOARD_DIR = MODEL_DIR

DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

EPS = 1e-10
LCF = 'fusion'  # cdw cdm fusion

# Feature processing configuration
# Options: 'none' (no extra processing), 'attention' (use BertAttention), 'linear' (use simple linear layer)
FEATURE_PROCESSOR = 'attention'

# Linear feature processor configuration (used when FEATURE_PROCESSOR='linear')
# Size of linear layer hidden layer, typically set to half of BERT_DIM
LINEAR_HIDDEN_SIZE = 384

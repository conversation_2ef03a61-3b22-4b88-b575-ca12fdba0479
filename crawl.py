from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
import time
import requests
import json
import csv


class CourseCommentScraper:
    def __init__(self, url, csv_file_name):
        self.url = url
        self.csv_file_name = csv_file_name
        self.cookies = None
        self.request_url = None
        self.total_page_count = None

    def setup_driver(self):
        """初始化 Selenium WebDriver"""
        options = Options()
        options.set_capability("goog:loggingPrefs", {
                               "performance": "ALL"})  # 启用性能日志
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        return driver

    def get_cookies_and_api_url(self):
        """获取 Cookies 和 API URL"""
        driver = self.setup_driver()
        driver.get(self.url)
        time.sleep(8)  # 等待页面加载完成

        # 获取网络日志
        logs = driver.get_log("performance")

        # 获取 Cookies
        cookies_list = driver.get_cookies()
        self.cookies = "; ".join(
            [f"{cookie['name']}={cookie['value']}" for cookie in cookies_list])

        # 解析网络日志，找到目标 API 请求的响应数据
        target_url_keyword = "mocCourseV2RpcBean.getCourseEvaluatePaginationByCourseIdOrTermId"
        self.request_url, api_response_body = self.find_api_response(
            logs, target_url_keyword, driver)

        if not self.request_url:
            print("未找到URL")
            driver.quit()
            exit()

        if not api_response_body:
            print("未找到目标 API 响应数据")
            driver.quit()
            exit()

        # 解析 API 响应数据，获取总页数
        self.total_page_count = json.loads(api_response_body)[
            "result"]["query"]["totlePageCount"]
        print("总页数:", self.total_page_count)

        driver.quit()

    def find_api_response(self, logs, target_url_keyword, driver):
        """解析网络日志，找到目标 API 请求的响应数据"""
        for log in logs:
            message = log.get("message", "")
            if target_url_keyword in message:
                try:
                    log_data = json.loads(message)
                    # 检查是否是网络响应
                    if log_data.get("message", {}).get("method") == "Network.responseReceived":
                        request_id = log_data["message"]["params"]["requestId"]
                        # 获取响应体
                        response_body = driver.execute_cdp_cmd(
                            "Network.getResponseBody", {"requestId": request_id})
                        return log_data["message"]["params"]["response"]["url"], response_body["body"]
                except Exception as e:
                    print(f"解析日志时出错: {e}")
        return None, None

    def scrape_comments(self):
        """爬取评论数据并保存到 CSV 文件"""
        if not self.request_url or not self.cookies:
            print("请先调用 get_cookies_and_api_url 方法初始化数据")
            return

        request_headers = {
            "Cookie": self.cookies
        }

        # 打开CSV文件，准备写入数据
        with open(self.csv_file_name, mode='a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            # 如果文件为空，写入表头
            if file.tell() == 0:
                writer.writerow(['评论内容'])

            # 遍历所有页面
            for page_index in range(1, self.total_page_count + 1):
                request_payload = {
                    "courseId": 268001,  # 课程 ID
                    "pageIndex": page_index,  # 当前页码
                    "pageSize": 20,  # 每页评论数
                    "orderBy": 3  # 排序方式
                }
                
                # 添加重试机制
                max_retries = 3
                retry_count = 0
                success = False
                
                while retry_count < max_retries and not success:
                    try:
                        response = requests.post(
                            self.request_url, headers=request_headers, data=request_payload)
                        time.sleep(1)  # 防止请求过快

                        # 检查请求是否成功
                        if response.status_code == 200:
                            comments = response.json()["result"]["list"]
                            for comment in comments:
                                # 去除评论内容中的换行符
                                content = comment['content'].replace('\n', ' ')
                                writer.writerow([content])
                            print(f"进度： {page_index} / {self.total_page_count}")
                            success = True
                        else:
                            print(f"请求失败，状态码: {response.status_code}，重试次数：{retry_count + 1}")
                            retry_count += 1
                            time.sleep(2)  # 失败后等待2秒再重试
                    except Exception as e:
                        print(f"请求异常：{e}，重试次数：{retry_count + 1}")
                        retry_count += 1
                        time.sleep(2)  # 异常后等待2秒再重试

                if not success:
                    print(f"页面 {page_index} 请求失败，已达到最大重试次数")
                    break


# 测试方法
def test_scraper():
    url = "https://www.icourse163.org/course/BIT-268001"
    csv_file_name = "comments.csv"
    scraper = CourseCommentScraper(url, csv_file_name)
    scraper.get_cookies_and_api_url()
    scraper.scrape_comments()


if __name__ == "__main__":
    test_scraper()

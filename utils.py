import pandas as pd
import torch.utils.data as data
from config import *
from transformers import BertTokenizer
import torch
import random
import numpy as np
from sklearn.metrics import precision_recall_fscore_support, accuracy_score, confusion_matrix
import matplotlib.pyplot as plt
import io
from matplotlib.figure import Figure

# 获取实体位置


def get_ent_pos(lst):
    items = []
    for i in range(len(lst)):
        # B-ASP（映射为1） 开头
        if lst[i] == 1:
            item = [i]
            while True:
                i += 1
                # 到 I-ASP（映射为2） 结束
                if i >= len(lst) or lst[i] != 2:
                    items.append(item)
                    break
                else:
                    item.append(i)
        i += 1
    return items

# [CLS]这个手机外观时尚，美中不足的是拍照像素低。[SEP]
# 0 0 0 0 0 1 2 0 0 0 0 0 0 0 0 0 1 2 2 2 0 0 0
# print(get_ent_pos([0,0,0,0,0,1,2,0,0,0,0,0,0,0,0,0,1,2,2,2,0,0,0]))
# [[5, 6], [16, 17, 18, 19]]


# def get_ent_weight(max_len, ent_pos):
#     cdm = []
#     cdw = []

#     for i in range(max_len):
#         dst = min(abs(i - ent_pos[0]), abs(i - ent_pos[-1]))
#         if dst <= SRD:
#             cdm.append(1)
#             cdw.append(1)
#         else:
#             cdm.append(0)
#             cdw.append(1 / (dst - SRD + 1))
#     return cdm, cdw

# 规则：实体+SRD距离内的权重为1，权重值和距离成反比，距离越远权重越低。
def get_ent_weight(max_len, ent_pos):
    cdm = []
    cdw = []

    for i in range(max_len):
        dst = min(abs(i - ent_pos[0]), abs(i - ent_pos[-1]))
        if dst <= SRD:
            cdm.append(1)
            cdw.append(1)
        else:
            cdm.append(0)
            cdw.append(1 - (dst - SRD + 1) / max_len)

    return cdm, cdw

# print(get_ent_weight(23, [5,6]))
# exit()

# 截断序列以适应BERT最大长度限制


def truncate_sequences(input_ids, bio_label, pola_label, max_len=BERT_MAX_LEN):
    if len(input_ids) <= max_len:
        return input_ids, bio_label, pola_label
    # 保留[CLS]和[SEP]标记，截断中间内容
    truncated_input_ids = [input_ids[0]] + \
        input_ids[1:max_len-1] + [input_ids[-1]]
    truncated_bio_label = [bio_label[0]] + \
        bio_label[1:max_len-1] + [bio_label[-1]]
    truncated_pola_label = [pola_label[0]] + \
        pola_label[1:max_len-1] + [pola_label[-1]]
    return truncated_input_ids, truncated_bio_label, truncated_pola_label


class Dataset(data.Dataset):
    def __init__(self, type='train'):
        super().__init__()
        if type == 'train':
            file_path = TRAIN_FILE_PATH
        elif type == 'val':
            file_path = VAL_FILE_PATH
        else:
            file_path = TEST_FILE_PATH
        self.df = pd.read_csv(file_path)
        self.tokenizer = BertTokenizer.from_pretrained(BERT_MODEL_NAME)

    def __len__(self):
        return len(self.df) - 1

    def __getitem__(self, index):
        # 相邻两个句子拼接
        # text1, bio1, pola1 = self.df.loc[index]
        # text2, bio2, pola2 = self.df.loc[index+1]
        # text = text1 + ' ; ' + text2
        # bio = bio1 + ' O ' + bio2
        # pola = pola1 + ' -1 ' + pola2

        # 直接取一个句子不拼接（新数据集）
        text, bio, pola = self.df.loc[index]

        # 按自己的规则分词
        tokens = ['[CLS]'] + text.split(' ') + ['[SEP]']
        input_ids = self.tokenizer.convert_tokens_to_ids(tokens)

        # BIO标签转id
        bio_arr = ['O'] + bio.split(' ') + ['O']
        bio_label = [BIO_MAP[l] for l in bio_arr]

        # 情感值转数字
        pola_arr = ['-1'] + pola.split(' ') + ['-1']
        pola_label = list(map(int, pola_arr))

        # 确保序列不超过BERT最大长度
        input_ids, bio_label, pola_label = truncate_sequences(
            input_ids, bio_label, pola_label)

        return input_ids, bio_label, pola_label

    # 自定义如何将 batch_size 个样本组合成一个批次
    def collate_fn(self, batch):
        # 统计最大句子长度
        batch.sort(key=lambda x: len(x[0]), reverse=True)
        # max_len = len(batch[0][0])
        max_len = min(len(batch[0][0]), BERT_MAX_LEN)  # 确保不超过BERT最大限制

        # 变量初始化
        batch_input_ids = []
        batch_bio_label = []
        batch_mask = []
        batch_ent_cdm = []
        batch_ent_cdw = []
        batch_pola_label = []
        batch_pairs = []

        # 一个句子中有多个实体时，随机取一个，来判断情感极性，另一个也可能在其他轮次中取到。
        for input_ids, bio_label, pola_label in batch:
            # 获取实体位置，没有实体跳过
            ent_pos = get_ent_pos(bio_label)
            if len(ent_pos) == 0:
                continue

            # 填充句子长度
            pad_len = max_len - len(input_ids)
            batch_input_ids.append(input_ids + [BERT_PAD_ID] * pad_len)
            batch_mask.append([1] * len(input_ids) + [0] * pad_len)
            batch_bio_label.append(bio_label + [BIO_O_ID] * pad_len)

            # 实体和情感分类对应
            pairs = []
            for pos in ent_pos:
                pola = pola_label[pos[0]]
                # 异常值替换
                pola = 0 if pola == -1 else pola
                pairs.append((pos, pola))
            batch_pairs.append(pairs)

            # 一个句子可能包含多个实体，为了训练情感分类，代码随机选择一个实体来计算上下文权重（cdm 和 cdw），避免模型过拟合到特定实体。

            # 随机取一个实体
            sg_ent_pos = random.choice(ent_pos)
            cdm, cdw = get_ent_weight(max_len, sg_ent_pos)

            # 计算加权参数
            batch_ent_cdm.append(cdm)
            batch_ent_cdw.append(cdw)
            # 实体第一个字的情感极性
            pola = pola_label[sg_ent_pos[0]]
            pola = 0 if pola == -1 else pola
            batch_pola_label.append(pola)

        return (
            torch.tensor(batch_input_ids),
            torch.tensor(batch_mask).bool(),
            torch.tensor(batch_bio_label),
            torch.tensor(batch_ent_cdm),
            torch.tensor(batch_ent_cdw),
            torch.tensor(batch_pola_label),
            batch_pairs,
        )


def get_pola(model, input_ids, mask, ent_label):
    # 变量初始化
    b_input_ids = []
    b_mask = []
    b_ent_cdm = []
    b_ent_cdw = []
    b_ent_pos = []

    # 根据label解析实体位置
    ent_pos = get_ent_pos(ent_label)
    n = len(ent_pos)
    if n == 0:
        return None, None

    # 确保长度不超过BERT最大限制
    seq_len = min(len(input_ids), BERT_MAX_LEN)
    if len(input_ids) > BERT_MAX_LEN:
        input_ids = input_ids[:BERT_MAX_LEN]
        mask = mask[:BERT_MAX_LEN]

    # n个实体一起预测，同一个句子复制n份，作为一个batch
    b_input_ids.extend([input_ids] * n)
    b_mask.extend([mask] * n)
    b_ent_pos.extend(ent_pos)
    for sg_ent_pos in ent_pos:
        # cdm, cdw = get_ent_weight(len(input_ids), sg_ent_pos)
        cdm, cdw = get_ent_weight(seq_len, sg_ent_pos)
        b_ent_cdm.append(cdm)
        b_ent_cdw.append(cdw)

    # 列表转tensor
    b_input_ids = torch.stack(b_input_ids, dim=0).to(DEVICE)
    b_mask = torch.stack(b_mask, dim=0).to(DEVICE)
    b_ent_cdm = torch.tensor(b_ent_cdm).to(DEVICE)
    b_ent_cdw = torch.tensor(b_ent_cdw).to(DEVICE)
    b_ent_pola = model.get_pola(b_input_ids, b_mask, b_ent_cdm, b_ent_cdw)
    return b_ent_pos, b_ent_pola


# 计算评估指标的函数
def calculate_metrics(y_true, y_pred, labels=None, metric_type='sentiment'):
    """
    计算全局和类别级别的评估指标

    Args:
        y_true: 真实标签列表
        y_pred: 预测标签列表
        labels: 标签列表，默认为None，会自动从数据中推断
        metric_type: 指标类型，'sentiment'表示情感分析指标，'entity'表示实体识别指标

    Returns:
        metrics: 包含所有评估指标的字典
    """
    # 如果没有提供标签，则从数据中推断
    if labels is None:
        labels = sorted(list(set(y_true) | set(y_pred)))

    # 计算准确率
    accuracy = round(accuracy_score(y_true, y_pred), 3)

    # 计算宏平均的精确率、召回率和F1分数
    macro_precision, macro_recall, macro_f1, _ = precision_recall_fscore_support(
        y_true, y_pred, average='macro', labels=labels, zero_division=0
    )

    # 计算微平均的精确率、召回率和F1分数
    micro_precision, micro_recall, micro_f1, _ = precision_recall_fscore_support(
        y_true, y_pred, average='micro', labels=labels, zero_division=0
    )

    # 计算每个类别的精确率、召回率和F1分数
    class_precision, class_recall, class_f1, class_support = precision_recall_fscore_support(
        y_true, y_pred, labels=labels, zero_division=0
    )

    # 四舍五入到3位小数
    macro_precision = round(macro_precision, 3)
    macro_recall = round(macro_recall, 3)
    macro_f1 = round(macro_f1, 3)

    micro_precision = round(micro_precision, 3)
    micro_recall = round(micro_recall, 3)
    micro_f1 = round(micro_f1, 3)

    class_precision = [round(p, 3) for p in class_precision]
    class_recall = [round(r, 3) for r in class_recall]
    class_f1 = [round(f, 3) for f in class_f1]

    # 构建返回的指标字典
    metrics = {
        'accuracy': accuracy,
        'macro_precision': macro_precision,
        'macro_recall': macro_recall,
        'macro_f1': macro_f1,
        'micro_precision': micro_precision,
        'micro_recall': micro_recall,
        'micro_f1': micro_f1,
        'class_precision': class_precision,
        'class_recall': class_recall,
        'class_f1': class_f1,
        'class_support': class_support.tolist(),
        'labels': labels
    }

    # 根据指标类型添加前缀
    if metric_type == 'entity':
        prefixed_metrics = {f'entity_{k}': v for k, v in metrics.items()}
        return prefixed_metrics
    elif metric_type == 'sentiment':
        prefixed_metrics = {f'sentiment_{k}': v for k, v in metrics.items()}
        # 为了兼容性，保留原来的键名
        prefixed_metrics.update(metrics)
        return prefixed_metrics

    return metrics


# 计算混淆矩阵并可视化
def plot_confusion_matrix(y_true, y_pred, labels=None, normalize=False, title=None, cmap=None, save_path=None):
    """
    计算混淆矩阵并可视化

    Args:
        y_true: 真实标签列表
        y_pred: 预测标签列表
        labels: 标签列表，默认为None，会自动从数据中推断
        normalize: 是否归一化，默认为False
        title: 图表标题，默认为None
        cmap: 颜色映射，默认为None
        save_path: 保存路径，默认为None，不保存

    Returns:
        cm: 混淆矩阵
    """
    # 如果没有提供标签，则从数据中推断
    if labels is None:
        labels = sorted(list(set(y_true) | set(y_pred)))

    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred, labels=labels)

    # 归一化
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]

    # 绘制混淆矩阵
    plt.figure(figsize=(10, 8))
    plt.imshow(cm, interpolation='nearest', cmap=cmap or plt.cm.Blues)

    # 设置标题
    if title:
        plt.title(title)

    # 设置刻度标签
    tick_marks = np.arange(len(labels))
    plt.xticks(tick_marks, labels, rotation=45)
    plt.yticks(tick_marks, labels)

    # 添加文本注释
    fmt = '.2f' if normalize else 'd'
    thresh = cm.max() / 2.
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            plt.text(j, i, format(cm[i, j], fmt),
                     ha="center", va="center",
                     color="white" if cm[i, j] > thresh else "black")

    plt.tight_layout()
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')

    # 保存图表
    if save_path:
        plt.savefig(save_path)
        print(f"混淆矩阵已保存到 {save_path}")

    plt.show()

    return cm


# 为TensorBoard创建混淆矩阵图表
def plot_confusion_matrix_for_tensorboard(cm, class_names=None, title=None):
    """
    创建一个适合TensorBoard的混淆矩阵图表

    Args:
        cm: 混淆矩阵
        class_names: 类别名称列表
        title: 图表标题，默认为None

    Returns:
        fig: matplotlib图表对象
    """
    # 创建图表
    fig = Figure(figsize=(8, 8))
    ax = fig.add_subplot(111)
    cax = ax.matshow(cm, cmap=plt.cm.Blues)
    fig.colorbar(cax)

    # 设置标题
    if title:
        ax.set_title(title, pad=20)

    # 设置类别标签
    if class_names is not None:
        # 先设置固定数量的刻度，然后再设置刻度标签
        tick_marks = np.arange(len(class_names))
        ax.set_xticks(tick_marks)
        ax.set_yticks(tick_marks)
        ax.set_xticklabels(class_names, rotation=45, ha='right')
        ax.set_yticklabels(class_names)

    # 设置刻度
    ax.set_xlabel('Predicted')
    ax.set_ylabel('True')
    ax.xaxis.set_label_position('bottom')
    ax.xaxis.tick_bottom()

    # 添加数值标签
    thresh = cm.max() / 2.
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            # 根据数据类型选择合适的格式化方式
            if np.issubdtype(cm.dtype, np.integer):
                value_str = format(cm[i, j], 'd')
            else:
                value_str = format(cm[i, j], '.2f')

            ax.text(j, i, value_str,
                    ha="center", va="center",
                    color="white" if cm[i, j] > thresh else "black")

    fig.tight_layout()
    return fig


# 计算总体Macro F1
def calculate_overall_f1(entity_metrics, sentiment_metrics):
    """
    计算实体识别和情感分析的总体Macro F1

    Args:
        entity_metrics: 实体识别指标字典
        sentiment_metrics: 情感分析指标字典

    Returns:
        overall_f1: 总体Macro F1
    """
    entity_f1 = entity_metrics['entity_macro_f1']
    sentiment_f1 = sentiment_metrics['macro_f1']

    # 使用配置文件中的任务权重计算加权平均
    overall_f1 = TASK_WEIGHT_ENT * entity_f1 + TASK_WEIGHT_POLA * sentiment_f1

    return round(overall_f1, 3)


# 计算总体Loss
def calculate_overall_loss(ent_loss, pola_loss):
    """
    计算实体识别和情感分析的总体Loss

    Args:
        ent_loss: 实体识别损失
        pola_loss: 情感分析损失

    Returns:
        overall_loss: 总体Loss
    """
    # 使用配置文件中的任务权重计算加权平均
    overall_loss = TASK_WEIGHT_ENT * ent_loss + TASK_WEIGHT_POLA * pola_loss

    return overall_loss


if __name__ == '__main__':
    dataset = Dataset()
    # 当样本数据复杂（如长度不一致的序列）时，默认的 collate_fn 可能无法满足需求
    loader = data.DataLoader(dataset, batch_size=2,
                             collate_fn=dataset.collate_fn)
    print(next(iter(loader)))
    # print(iter(loader).next())

Boot B-ASP 2
time I-ASP 2
is O -1
super O -1
fast O -1
, O -1
around O -1
anywhere O -1
from O -1
35 O -1
seconds O -1
to O -1
1 O -1
minute O -1
. O -1

tech B-ASP 0
support I-ASP 0
would O -1
not O -1
fix O -1
the O -1
problem O -1
unless O -1
I O -1
bought O -1
your O -1
plan O -1
for O -1
$ O -1
150 O -1
plus O -1
. O -1

Set B-ASP 2
up I-ASP 2
was O -1
easy O -1
. O -1

Did O -1
not O -1
enjoy O -1
the O -1
new O -1
Windows B-ASP 0
8 I-ASP 0
and O -1
touchscreen B-ASP -1
functions I-ASP -1
. O -1

Did O -1
not O -1
enjoy O -1
the O -1
new O -1
Windows B-ASP -1
8 I-ASP -1
and O -1
touchscreen B-ASP 0
functions I-ASP 0
. O -1

Other O -1
than O -1
not O -1
being O -1
a O -1
fan O -1
of O -1
click B-ASP -1
pads I-ASP -1
-LRB- O -1
industry O -1
standard O -1
these O -1
days O -1
-RRB- O -1
and O -1
the O -1
lousy O -1
internal B-ASP 0
speakers I-ASP 0
, O -1
it O -1
's O -1
hard O -1
for O -1
me O -1
to O -1
find O -1
things O -1
about O -1
this O -1
notebook O -1
I O -1
do O -1
n't O -1
like O -1
, O -1
especially O -1
considering O -1
the O -1
$ O -1
350 O -1
price B-ASP -1
tag I-ASP -1
. O -1

Other O -1
than O -1
not O -1
being O -1
a O -1
fan O -1
of O -1
click B-ASP -1
pads I-ASP -1
-LRB- O -1
industry O -1
standard O -1
these O -1
days O -1
-RRB- O -1
and O -1
the O -1
lousy O -1
internal B-ASP -1
speakers I-ASP -1
, O -1
it O -1
's O -1
hard O -1
for O -1
me O -1
to O -1
find O -1
things O -1
about O -1
this O -1
notebook O -1
I O -1
do O -1
n't O -1
like O -1
, O -1
especially O -1
considering O -1
the O -1
$ O -1
350 O -1
price B-ASP 2
tag I-ASP 2
. O -1

Other O -1
than O -1
not O -1
being O -1
a O -1
fan O -1
of O -1
click B-ASP 0
pads I-ASP 0
-LRB- O -1
industry O -1
standard O -1
these O -1
days O -1
-RRB- O -1
and O -1
the O -1
lousy O -1
internal B-ASP -1
speakers I-ASP -1
, O -1
it O -1
's O -1
hard O -1
for O -1
me O -1
to O -1
find O -1
things O -1
about O -1
this O -1
notebook O -1
I O -1
do O -1
n't O -1
like O -1
, O -1
especially O -1
considering O -1
the O -1
$ O -1
350 O -1
price B-ASP -1
tag I-ASP -1
. O -1

No O -1
installation B-ASP 1
disk I-ASP 1
(DVD) I-ASP 1
is O -1
included O -1
. O -1

It O -1
's O -1
fast O -1
, O -1
light O -1
, O -1
and O -1
simple O -1
to O -1
use B-ASP 2
. O -1

Works B-ASP 2
well O -1
, O -1
and O -1
I O -1
am O -1
extremely O -1
happy O -1
to O -1
be O -1
back O -1
to O -1
an O -1
apple B-ASP -1
OS I-ASP -1
. O -1

Works B-ASP -1
well O -1
, O -1
and O -1
I O -1
am O -1
extremely O -1
happy O -1
to O -1
be O -1
back O -1
to O -1
an O -1
apple B-ASP 2
OS I-ASP 2
. O -1

Sure O -1
it O -1
's O -1
not O -1
light O -1
and O -1
slim O -1
but O -1
the O -1
features B-ASP 2
make O -1
up O -1
for O -1
it O -1
100 O -1
% O -1
. O -1

I O -1
am O -1
pleased O -1
with O -1
the O -1
fast O -1
log B-ASP 2
on I-ASP 2
, O -1
speedy O -1
WiFi B-ASP -1
connection I-ASP -1
and O -1
the O -1
long O -1
battery B-ASP -1
life I-ASP -1
-LRB- O -1
> O -1
6 O -1
hrs O -1
-RRB- O -1
. O -1

I O -1
am O -1
pleased O -1
with O -1
the O -1
fast O -1
log B-ASP -1
on I-ASP -1
, O -1
speedy O -1
WiFi B-ASP 2
connection I-ASP 2
and O -1
the O -1
long O -1
battery B-ASP -1
life I-ASP -1
-LRB- O -1
> O -1
6 O -1
hrs O -1
-RRB- O -1
. O -1

I O -1
am O -1
pleased O -1
with O -1
the O -1
fast O -1
log B-ASP -1
on I-ASP -1
, O -1
speedy O -1
WiFi B-ASP -1
connection I-ASP -1
and O -1
the O -1
long O -1
battery B-ASP 2
life I-ASP 2
-LRB- O -1
> O -1
6 O -1
hrs O -1
-RRB- O -1
. O -1

The O -1
Apple O -1
engineers O -1
have O -1
not O -1
yet O -1
discovered O -1
the O -1
delete B-ASP 0
key I-ASP 0
. O -1

Made O -1
interneting B-ASP 0
-LRB- O -1
part O -1
of O -1
my O -1
business O -1
-RRB- O -1
very O -1
difficult O -1
to O -1
maintain O -1
. O -1

Luckily O -1
, O -1
for O -1
all O -1
of O -1
us O -1
contemplating O -1
the O -1
decision O -1
, O -1
the O -1
Mac O -1
Mini O -1
is O -1
priced B-ASP 2
just O -1
right O -1
. O -1

Super O -1
light O -1
, O -1
super O -1
sexy O -1
and O -1
everything O -1
just O -1
works B-ASP 2
. O -1

Only O -1
problem O -1
that O -1
I O -1
had O -1
was O -1
that O -1
the O -1
track B-ASP 0
pad I-ASP 0
was O -1
not O -1
very O -1
good O -1
for O -1
me O -1
, O -1
I O -1
only O -1
had O -1
a O -1
problem O -1
once O -1
or O -1
twice O -1
with O -1
it O -1
, O -1
But O -1
probably O -1
my O -1
computer O -1
was O -1
a O -1
bit O -1
defective O -1
. O -1

It O -1
is O -1
super O -1
fast O -1
and O -1
has O -1
outstanding O -1
graphics B-ASP 2
. O -1

But O -1
the O -1
mountain B-ASP 0
lion I-ASP 0
is O -1
just O -1
too O -1
slow O -1
. O -1

Strong O -1
build B-ASP -1
though O -1
which O -1
really O -1
adds O -1
to O -1
its O -1
durability B-ASP 2
. O -1

Strong O -1
build B-ASP 2
though O -1
which O -1
really O -1
adds O -1
to O -1
its O -1
durability B-ASP -1
. O -1

The O -1
battery B-ASP 2
life I-ASP 2
is O -1
excellent O -1
- O -1
6-7 O -1
hours O -1
without O -1
charging O -1
. O -1

I O -1
've O -1
had O -1
my O -1
computer O -1
for O -1
2 O -1
weeks O -1
already O -1
and O -1
it O -1
works B-ASP 2
perfectly O -1
. O -1

And O -1
I O -1
may O -1
be O -1
the O -1
only O -1
one O -1
but O -1
I O -1
am O -1
really O -1
liking O -1
Windows B-ASP 2
8 I-ASP 2
. O -1

The O -1
baterry B-ASP 2
is O -1
very O -1
longer O -1
. O -1

Its O -1
size B-ASP 2
is O -1
ideal O -1
and O -1
the O -1
weight B-ASP -1
is O -1
acceptable O -1
. O -1

Its O -1
size B-ASP -1
is O -1
ideal O -1
and O -1
the O -1
weight B-ASP 2
is O -1
acceptable O -1
. O -1

I O -1
can O -1
say O -1
that O -1
I O -1
am O -1
fully O -1
satisfied O -1
with O -1
the O -1
performance B-ASP 2
that O -1
the O -1
computer O -1
has O -1
supplied O -1
. O -1

This O -1
laptop O -1
has O -1
only O -1
2 O -1
USB B-ASP 0
ports I-ASP 0
, O -1
and O -1
they O -1
are O -1
both O -1
on O -1
the O -1
same O -1
side O -1
. O -1

It O -1
has O -1
so O -1
much O -1
more O -1
speed B-ASP 2
and O -1
the O -1
screen B-ASP -1
is O -1
very O -1
sharp O -1
. O -1

It O -1
has O -1
so O -1
much O -1
more O -1
speed B-ASP -1
and O -1
the O -1
screen B-ASP 2
is O -1
very O -1
sharp O -1
. O -1

Everything O -1
I O -1
wanted O -1
and O -1
everything O -1
I O -1
needed O -1
and O -1
the O -1
price B-ASP 2
was O -1
great O -1
! O -1

It O -1
's O -1
not O -1
inexpensive O -1
but O -1
the O -1
Hardware B-ASP 2
performance I-ASP 2
is O -1
impressive O -1
for O -1
a O -1
computer O -1
this O -1
small O -1
. O -1

This O -1
thing O -1
is O -1
awesome O -1
, O -1
everything O -1
always O -1
works B-ASP 2
, O -1
everything O -1
is O -1
always O -1
easy O -1
to O -1
set B-ASP -1
up I-ASP -1
, O -1
everything O -1
is O -1
compatible O -1
, O -1
its O -1
literally O -1
everything O -1
I O -1
could O -1
ask O -1
for O -1
. O -1

This O -1
thing O -1
is O -1
awesome O -1
, O -1
everything O -1
always O -1
works B-ASP -1
, O -1
everything O -1
is O -1
always O -1
easy O -1
to O -1
set B-ASP 2
up I-ASP 2
, O -1
everything O -1
is O -1
compatible O -1
, O -1
its O -1
literally O -1
everything O -1
I O -1
could O -1
ask O -1
for O -1
. O -1

Keyboard B-ASP 2
responds O -1
well O -1
to O -1
presses O -1
. O -1

Lastly O -1
, O -1
Windows B-ASP 0
8 I-ASP 0
is O -1
annoying O -1
. O -1

Everything O -1
is O -1
so O -1
easy O -1
and O -1
intuitive O -1
to O -1
setup B-ASP 2
or O -1
configure B-ASP -1
. O -1

Everything O -1
is O -1
so O -1
easy O -1
and O -1
intuitive O -1
to O -1
setup B-ASP -1
or O -1
configure B-ASP 2
. O -1

Biggest O -1
complaint O -1
is O -1
Windows B-ASP 0
8 I-ASP 0
. O -1

Only O -1
2 O -1
usb B-ASP 0
ports I-ASP 0
... O -1
seems O -1
kind O -1
of O -1
... O -1
limited O -1
. O -1

It O -1
has O -1
all O -1
the O -1
expected O -1
features B-ASP 2
and O -1
more O -1
+ O -1
plus O -1
a O -1
wide O -1
screen B-ASP -1
and O -1
more O -1
than O -1
roomy O -1
keyboard B-ASP -1
. O -1

It O -1
has O -1
all O -1
the O -1
expected O -1
features B-ASP -1
and O -1
more O -1
+ O -1
plus O -1
a O -1
wide O -1
screen B-ASP 2
and O -1
more O -1
than O -1
roomy O -1
keyboard B-ASP -1
. O -1

It O -1
has O -1
all O -1
the O -1
expected O -1
features B-ASP -1
and O -1
more O -1
+ O -1
plus O -1
a O -1
wide O -1
screen B-ASP -1
and O -1
more O -1
than O -1
roomy O -1
keyboard B-ASP 2
. O -1

Amazing O -1
Performance B-ASP 2
for O -1
anything O -1
I O -1
throw O -1
at O -1
it O -1
. O -1

The O -1
receiver O -1
was O -1
full O -1
of O -1
superlatives O -1
for O -1
the O -1
quality B-ASP 2
and O -1
performance B-ASP -1
. O -1

The O -1
receiver O -1
was O -1
full O -1
of O -1
superlatives O -1
for O -1
the O -1
quality B-ASP -1
and O -1
performance B-ASP 2
. O -1

I O -1
was O -1
extremely O -1
happy O -1
with O -1
the O -1
OS B-ASP 2
itself O -1
. O -1

The O -1
new O -1
MBP O -1
offers O -1
great O -1
portability B-ASP 2
and O -1
gives O -1
us O -1
confidence O -1
that O -1
we O -1
are O -1
not O -1
going O -1
to O -1
need O -1
to O -1
purchase O -1
a O -1
new O -1
laptop O -1
in O -1
18 O -1
months O -1
. O -1

The O -1
criticism O -1
has O -1
waned O -1
, O -1
and O -1
now O -1
I O -1
'd O -1
be O -1
the O -1
first O -1
to O -1
recommend O -1
an O -1
Air O -1
for O -1
truly O -1
portable B-ASP 2
computing I-ASP 2
. O -1

I O -1
would O -1
have O -1
given O -1
it O -1
5 O -1
starts O -1
was O -1
it O -1
not O -1
for O -1
the O -1
fact O -1
that O -1
it O -1
had O -1
Windows B-ASP 0
8 I-ASP 0

MS B-ASP 2
Office I-ASP 2
2011 I-ASP 2
for I-ASP 2
Mac I-ASP 2
is O -1
wonderful O -1
, O -1
well O -1
worth O -1
it O -1
. O -1

But O -1
the O -1
performance B-ASP 0
of O -1
Mac O -1
Mini O -1
is O -1
a O -1
huge O -1
disappointment O -1
. O -1

They O -1
do O -1
n't O -1
just O -1
look B-ASP 2
good O -1
; O -1
they O -1
deliver O -1
excellent O -1
performance B-ASP -1
. O -1

They O -1
do O -1
n't O -1
just O -1
look B-ASP -1
good O -1
; O -1
they O -1
deliver O -1
excellent O -1
performance B-ASP 2
. O -1

I O -1
have O -1
had O -1
it O -1
over O -1
a O -1
year O -1
now O -1
with O -1
out O -1
a O -1
Glitch O -1
of O -1
any O -1
kind O -1
. O -1
. O -1
I O -1
love O -1
the O -1
lit B-ASP 2
up I-ASP 2
keys I-ASP 2
and O -1
screen B-ASP -1
display I-ASP -1
... O -1
this O -1
thing O -1
is O -1
Fast O -1
and O -1
clear O -1
as O -1
can O -1
be O -1
. O -1

I O -1
have O -1
had O -1
it O -1
over O -1
a O -1
year O -1
now O -1
with O -1
out O -1
a O -1
Glitch O -1
of O -1
any O -1
kind O -1
. O -1
. O -1
I O -1
love O -1
the O -1
lit B-ASP -1
up I-ASP -1
keys I-ASP -1
and O -1
screen B-ASP 2
display I-ASP 2
... O -1
this O -1
thing O -1
is O -1
Fast O -1
and O -1
clear O -1
as O -1
can O -1
be O -1
. O -1

The O -1
Mountain B-ASP 2
Lion I-ASP 2
OS I-ASP 2
is O -1
not O -1
hard O -1
to O -1
figure O -1
out O -1
if O -1
you O -1
are O -1
familiar O -1
with O -1
Microsoft B-ASP -1
Windows I-ASP -1
. O -1

The O -1
Mountain B-ASP -1
Lion I-ASP -1
OS I-ASP -1
is O -1
not O -1
hard O -1
to O -1
figure O -1
out O -1
if O -1
you O -1
are O -1
familiar O -1
with O -1
Microsoft B-ASP 1
Windows I-ASP 1
. O -1

However O -1
, O -1
I O -1
can O -1
refute O -1
that O -1
OSX B-ASP 0
is O -1
`` O -1
FAST O -1
'' O -1
. O -1

Enjoy O -1
using O -1
Microsoft B-ASP 2
Office I-ASP 2
! O -1

Incredible O -1
graphics B-ASP 2
and O -1
brilliant O -1
colors B-ASP -1
. O -1

Incredible O -1
graphics B-ASP -1
and O -1
brilliant O -1
colors B-ASP 2
. O -1

Built-in B-ASP 2
apps I-ASP 2
are O -1
purely O -1
amazing O -1
. O -1

Cons O -1
: O -1
Screen B-ASP 0
resolution I-ASP 0
. O -1

From O -1
the O -1
speed B-ASP -1
to O -1
the O -1
multi B-ASP -1
touch I-ASP -1
gestures I-ASP -1
this O -1
operating B-ASP 2
system I-ASP 2
beats O -1
Windows B-ASP -1
easily O -1
. O -1

From O -1
the O -1
speed B-ASP -1
to O -1
the O -1
multi B-ASP -1
touch I-ASP -1
gestures I-ASP -1
this O -1
operating B-ASP -1
system I-ASP -1
beats O -1
Windows B-ASP 0
easily O -1
. O -1

From O -1
the O -1
speed B-ASP 2
to O -1
the O -1
multi B-ASP -1
touch I-ASP -1
gestures I-ASP -1
this O -1
operating B-ASP -1
system I-ASP -1
beats O -1
Windows B-ASP -1
easily O -1
. O -1

From O -1
the O -1
speed B-ASP -1
to O -1
the O -1
multi B-ASP 2
touch I-ASP 2
gestures I-ASP 2
this O -1
operating B-ASP -1
system I-ASP -1
beats O -1
Windows B-ASP -1
easily O -1
. O -1

I O -1
really O -1
like O -1
the O -1
size B-ASP 2
and O -1
I O -1
'm O -1
a O -1
fan O -1
of O -1
the O -1
ACERS O -1
. O -1

I O -1
opted O -1
for O -1
the O -1
SquareTrade B-ASP 2
3-Year I-ASP 2
Computer I-ASP 2
Accidental I-ASP 2
Protection I-ASP 2
Warranty I-ASP 2
-LRB- O -1
$ O -1
1500-2000 O -1
-RRB- O -1
which O -1
also O -1
support O -1
`` O -1
accidents O -1
'' O -1
like O -1
drops O -1
and O -1
spills O -1
that O -1
are O -1
NOT O -1
covered O -1
by O -1
AppleCare B-ASP -1
. O -1

I O -1
opted O -1
for O -1
the O -1
SquareTrade B-ASP -1
3-Year I-ASP -1
Computer I-ASP -1
Accidental I-ASP -1
Protection I-ASP -1
Warranty I-ASP -1
-LRB- O -1
$ O -1
1500-2000 O -1
-RRB- O -1
which O -1
also O -1
support O -1
`` O -1
accidents O -1
'' O -1
like O -1
drops O -1
and O -1
spills O -1
that O -1
are O -1
NOT O -1
covered O -1
by O -1
AppleCare B-ASP 0
. O -1

It O -1
's O -1
light O -1
and O -1
easy O -1
to O -1
transport B-ASP 2
. O -1

Once O -1
you O -1
get O -1
past O -1
learning O -1
how O -1
to O -1
use O -1
the O -1
poorly O -1
designed O -1
Windows B-ASP 0
8 I-ASP 0
Set-Up I-ASP 0
you O -1
may O -1
feel O -1
frustrated O -1
. O -1

It O -1
's O -1
been O -1
time O -1
for O -1
a O -1
new O -1
laptop O -1
, O -1
and O -1
the O -1
only O -1
debate O -1
was O -1
which O -1
size B-ASP 1
of O -1
the O -1
Mac O -1
laptops O -1
, O -1
and O -1
whether O -1
to O -1
spring O -1
for O -1
the O -1
retina B-ASP -1
display I-ASP -1
. O -1

It O -1
's O -1
been O -1
time O -1
for O -1
a O -1
new O -1
laptop O -1
, O -1
and O -1
the O -1
only O -1
debate O -1
was O -1
which O -1
size B-ASP -1
of O -1
the O -1
Mac O -1
laptops O -1
, O -1
and O -1
whether O -1
to O -1
spring O -1
for O -1
the O -1
retina B-ASP 1
display I-ASP 1
. O -1

The O -1
reason O -1
why O -1
I O -1
choose O -1
apple O -1
MacBook O -1
because O -1
of O -1
their O -1
design B-ASP 2
and O -1
the O -1
aluminum B-ASP -1
casing I-ASP -1
. O -1

The O -1
reason O -1
why O -1
I O -1
choose O -1
apple O -1
MacBook O -1
because O -1
of O -1
their O -1
design B-ASP -1
and O -1
the O -1
aluminum B-ASP 2
casing I-ASP 2
. O -1

The O -1
aluminum B-ASP 2
body I-ASP 2
sure O -1
makes O -1
it O -1
stand O -1
out O -1
. O -1

It O -1
is O -1
very O -1
easy O -1
to O -1
integrate B-ASP 2
bluetooth I-ASP 2
devices I-ASP 2
, O -1
and O -1
USB B-ASP -1
devices I-ASP -1
are O -1
recognized O -1
almost O -1
instantly O -1
. O -1

It O -1
is O -1
very O -1
easy O -1
to O -1
integrate B-ASP -1
bluetooth I-ASP -1
devices I-ASP -1
, O -1
and O -1
USB B-ASP 2
devices I-ASP 2
are O -1
recognized O -1
almost O -1
instantly O -1
. O -1

And O -1
the O -1
fact O -1
that O -1
Apple O -1
is O -1
driving O -1
the O -1
13 O -1
'' O -1
RMBP O -1
with O -1
the O -1
Intel4000 B-ASP 0
graphic I-ASP 0
chip I-ASP 0
seems O -1
underpowered O -1
-LRB- O -1
to O -1
me O -1
. O -1

Apple O -1
removed O -1
the O -1
DVD B-ASP 1
drive I-ASP 1
Firewire I-ASP 1
port I-ASP 1
-LRB- O -1
will O -1
work O -1
with O -1
adapter B-ASP -1
-RRB- O -1
and O -1
put O -1
the O -1
SDXC B-ASP -1
slot I-ASP -1
in O -1
a O -1
silly O -1
position O -1
on O -1
the O -1
back O -1
. O -1

Apple O -1
removed O -1
the O -1
DVD B-ASP -1
drive I-ASP -1
Firewire I-ASP -1
port I-ASP -1
-LRB- O -1
will O -1
work O -1
with O -1
adapter B-ASP 1
-RRB- O -1
and O -1
put O -1
the O -1
SDXC B-ASP -1
slot I-ASP -1
in O -1
a O -1
silly O -1
position O -1
on O -1
the O -1
back O -1
. O -1

Apple O -1
removed O -1
the O -1
DVD B-ASP -1
drive I-ASP -1
Firewire I-ASP -1
port I-ASP -1
-LRB- O -1
will O -1
work O -1
with O -1
adapter B-ASP -1
-RRB- O -1
and O -1
put O -1
the O -1
SDXC B-ASP 0
slot I-ASP 0
in O -1
a O -1
silly O -1
position O -1
on O -1
the O -1
back O -1
. O -1

The O -1
durability B-ASP 2
of O -1
the O -1
laptop O -1
will O -1
make O -1
it O -1
worth O -1
the O -1
money O -1
. O -1

Well O -1
designed B-ASP 2
and O -1
fast O -1
. O -1

But O -1
I O -1
was O -1
completely O -1
wrong O -1
, O -1
this O -1
computer O -1
is O -1
UNBELIEVABLE O -1
amazing O -1
and O -1
easy O -1
to O -1
use B-ASP 2
. O -1

Exactly O -1
as O -1
posted O -1
plus O -1
a O -1
great O -1
value B-ASP 2
. O -1

The O -1
specs B-ASP 2
are O -1
pretty O -1
good O -1
too O -1
. O -1

Apple O -1
is O -1
unmatched O -1
in O -1
product B-ASP 2
quality I-ASP 2
, O -1
aesthetics B-ASP -1
, O -1
craftmanship B-ASP -1
, O -1
and O -1
customer B-ASP -1
service I-ASP -1
. O -1

Apple O -1
is O -1
unmatched O -1
in O -1
product B-ASP -1
quality I-ASP -1
, O -1
aesthetics B-ASP 2
, O -1
craftmanship B-ASP -1
, O -1
and O -1
customer B-ASP -1
service I-ASP -1
. O -1

Apple O -1
is O -1
unmatched O -1
in O -1
product B-ASP -1
quality I-ASP -1
, O -1
aesthetics B-ASP -1
, O -1
craftmanship B-ASP 2
, O -1
and O -1
customer B-ASP -1
service I-ASP -1
. O -1

Apple O -1
is O -1
unmatched O -1
in O -1
product B-ASP -1
quality I-ASP -1
, O -1
aesthetics B-ASP -1
, O -1
craftmanship B-ASP -1
, O -1
and O -1
customer B-ASP 2
service I-ASP 2
. O -1

It O -1
is O -1
a O -1
great O -1
size B-ASP 2
and O -1
amazing O -1
windows B-ASP -1
8 I-ASP -1
included O -1
! O -1

It O -1
is O -1
a O -1
great O -1
size B-ASP -1
and O -1
amazing O -1
windows B-ASP 2
8 I-ASP 2
included O -1
! O -1

I O -1
do O -1
not O -1
like O -1
too O -1
much O -1
Windows B-ASP 0
8 I-ASP 0
. O -1

Startup B-ASP 0
times I-ASP 0
are O -1
incredibly O -1
long O -1
: O -1
over O -1
two O -1
minutes O -1
. O -1

Also O -1
stunning O -1
colors B-ASP 2
and O -1
speedy O -1

great O -1
price B-ASP 2
free O -1
shipping B-ASP -1
what O -1
else O -1
can O -1
i O -1
ask O -1
for O -1
!! O -1

great O -1
price B-ASP -1
free O -1
shipping B-ASP 2
what O -1
else O -1
can O -1
i O -1
ask O -1
for O -1
!! O -1

This O -1
mouse B-ASP 2
is O -1
terrific O -1
. O -1

It O -1
is O -1
really O -1
thick O -1
around O -1
the O -1
battery B-ASP 1
. O -1

And O -1
windows B-ASP 2
7 I-ASP 2
works O -1
like O -1
a O -1
charm O -1
. O -1

:-RRB- O -1
Great O -1
product O -1
, O -1
great O -1
price B-ASP 2
, O -1
great O -1
delivery B-ASP -1
, O -1
and O -1
great O -1
service B-ASP -1
. O -1

:-RRB- O -1
Great O -1
product O -1
, O -1
great O -1
price B-ASP -1
, O -1
great O -1
delivery B-ASP 2
, O -1
and O -1
great O -1
service B-ASP -1
. O -1

:-RRB- O -1
Great O -1
product O -1
, O -1
great O -1
price B-ASP -1
, O -1
great O -1
delivery B-ASP -1
, O -1
and O -1
great O -1
service B-ASP 2
. O -1

:] O -1
It O -1
arrived O -1
so O -1
fast O -1
and O -1
customer B-ASP 2
service I-ASP 2
was O -1
great O -1
. O -1

tried O -1
windows B-ASP 0
8 I-ASP 0
and O -1
hated O -1
it O -1
!!! O -1

Set B-ASP 2
up I-ASP 2
was O -1
a O -1
breeze O -1
. O -1

But O -1
I O -1
do O -1
NOT O -1
like O -1
Win8 B-ASP 0
. O -1

I O -1
am O -1
still O -1
in O -1
the O -1
process O -1
of O -1
learning O -1
about O -1
its O -1
features B-ASP 1
. O -1

I O -1
had O -1
the O -1
same O -1
reasons O -1
as O -1
most O -1
PC O -1
users O -1
: O -1
the O -1
price B-ASP 0
, O -1
the O -1
overbearing O -1
restrictions O -1
of O -1
OSX B-ASP -1
and O -1
lack O -1
of O -1
support B-ASP -1
for I-ASP -1
games I-ASP -1
. O -1

I O -1
had O -1
the O -1
same O -1
reasons O -1
as O -1
most O -1
PC O -1
users O -1
: O -1
the O -1
price B-ASP -1
, O -1
the O -1
overbearing O -1
restrictions O -1
of O -1
OSX B-ASP 0
and O -1
lack O -1
of O -1
support B-ASP -1
for I-ASP -1
games I-ASP -1
. O -1

I O -1
had O -1
the O -1
same O -1
reasons O -1
as O -1
most O -1
PC O -1
users O -1
: O -1
the O -1
price B-ASP -1
, O -1
the O -1
overbearing O -1
restrictions O -1
of O -1
OSX B-ASP -1
and O -1
lack O -1
of O -1
support B-ASP 0
for I-ASP 0
games I-ASP 0
. O -1

I O -1
wanted O -1
it O -1
for O -1
it O -1
's O -1
mobility B-ASP 2
and O -1
man O -1
, O -1
this O -1
little O -1
bad O -1
boy O -1
is O -1
very O -1
nice O -1
. O -1

I O -1
found O -1
the O -1
mini O -1
to O -1
be O -1
: O -1
Exceptionally O -1
easy O -1
to O -1
set B-ASP 2
up I-ASP 2

Having O -1
USB3 B-ASP 2
is O -1
why O -1
I O -1
bought O -1
this O -1
Mini O -1
. O -1

The O -1
sound B-ASP 2
is O -1
nice O -1
and O -1
loud O -1
; O -1
I O -1
do O -1
n't O -1
have O -1
any O -1
problems O -1
with O -1
hearing O -1
anything O -1
. O -1

It O -1
is O -1
very O -1
slim O -1
, O -1
the O -1
track B-ASP 2
pad I-ASP 2
is O -1
very O -1
much O -1
impressed O -1
with O -1
me O -1
. O -1

The O -1
settings B-ASP 0
are O -1
not O -1
user-friendly O -1
either O -1
. O -1

Thank O -1
goodness O -1
for O -1
OpenOffice B-ASP 2
! O -1

Awesome O -1
form B-ASP 2
factor I-ASP 2
, O -1
great O -1
battery B-ASP -1
life I-ASP -1
, O -1
wonderful O -1
UX B-ASP -1
. O -1

Awesome O -1
form B-ASP -1
factor I-ASP -1
, O -1
great O -1
battery B-ASP 2
life I-ASP 2
, O -1
wonderful O -1
UX B-ASP -1
. O -1

Awesome O -1
form B-ASP -1
factor I-ASP -1
, O -1
great O -1
battery B-ASP -1
life I-ASP -1
, O -1
wonderful O -1
UX B-ASP 2
. O -1

i O -1
love O -1
the O -1
keyboard B-ASP 2
and O -1
the O -1
screen B-ASP -1
. O -1

i O -1
love O -1
the O -1
keyboard B-ASP -1
and O -1
the O -1
screen B-ASP 2
. O -1

However O -1
, O -1
there O -1
are O -1
MAJOR O -1
issues O -1
with O -1
the O -1
touchpad B-ASP 0
which O -1
render O -1
the O -1
device O -1
nearly O -1
useless O -1
. O -1

I O -1
've O -1
already O -1
upgraded O -1
o O -1
Mavericks B-ASP 2
and O -1
I O -1
am O -1
impressed O -1
with O -1
everything O -1
about O -1
this O -1
computer O -1
. O -1

Not O -1
as O -1
fast O -1
as O -1
I O -1
would O -1
have O -1
expect O -1
for O -1
an O -1
i5 B-ASP 0
. O -1

thanks O -1
for O -1
great O -1
service B-ASP 2
and O -1
shipping B-ASP -1
! O -1

thanks O -1
for O -1
great O -1
service B-ASP -1
and O -1
shipping B-ASP 2
! O -1

The O -1
performance B-ASP 2
seems O -1
quite O -1
good O -1
, O -1
and O -1
built-in B-ASP -1
applications I-ASP -1
like O -1
iPhoto B-ASP -1
work O -1
great O -1
with O -1
my O -1
phone O -1
and O -1
camera O -1
. O -1

The O -1
performance B-ASP -1
seems O -1
quite O -1
good O -1
, O -1
and O -1
built-in B-ASP 2
applications I-ASP 2
like O -1
iPhoto B-ASP -1
work O -1
great O -1
with O -1
my O -1
phone O -1
and O -1
camera O -1
. O -1

The O -1
performance B-ASP -1
seems O -1
quite O -1
good O -1
, O -1
and O -1
built-in B-ASP -1
applications I-ASP -1
like O -1
iPhoto B-ASP 2
work O -1
great O -1
with O -1
my O -1
phone O -1
and O -1
camera O -1
. O -1

I O -1
did O -1
swap O -1
out O -1
the O -1
hard B-ASP 1
drive I-ASP 1
for O -1
a O -1
Samsung B-ASP -1
830 I-ASP -1
SSD I-ASP -1
which O -1
I O -1
highly O -1
recommend O -1
. O -1

I O -1
did O -1
swap O -1
out O -1
the O -1
hard B-ASP -1
drive I-ASP -1
for O -1
a O -1
Samsung B-ASP 2
830 I-ASP 2
SSD I-ASP 2
which O -1
I O -1
highly O -1
recommend O -1
. O -1

Starts B-ASP 2
up I-ASP 2
in O -1
a O -1
hurry O -1
and O -1
everything O -1
is O -1
ready O -1
to O -1
go O -1
. O -1

Yes O -1
, O -1
that O -1
's O -1
a O -1
good O -1
thing O -1
, O -1
but O -1
it O -1
's O -1
made O -1
from O -1
aluminum B-ASP 0
that O -1
scratches O -1
easily O -1
. O -1

Quick O -1
and O -1
has O -1
built B-ASP 2
in I-ASP 2
virus I-ASP 2
control I-ASP 2
. O -1

Took O -1
a O -1
long O -1
time O -1
trying O -1
to O -1
decide O -1
between O -1
one O -1
with O -1
retina B-ASP 1
display I-ASP 1
and O -1
one O -1
without O -1
. O -1

I O -1
was O -1
also O -1
informed O -1
that O -1
the O -1
components B-ASP 0
of O -1
the O -1
Mac O -1
Book O -1
were O -1
dirty O -1
. O -1

the O -1
hardware B-ASP 0
problems O -1
have O -1
been O -1
so O -1
bad O -1
, O -1
i O -1
ca O -1
n't O -1
wait O -1
till O -1
it O -1
completely O -1
dies O -1
in O -1
3 O -1
years O -1
, O -1
TOPS O -1
! O -1

It O -1
's O -1
so O -1
nice O -1
that O -1
the O -1
battery B-ASP 2
last O -1
so O -1
long O -1
and O -1
that O -1
this O -1
machine O -1
has O -1
the O -1
snow B-ASP -1
lion I-ASP -1
! O -1

It O -1
's O -1
so O -1
nice O -1
that O -1
the O -1
battery B-ASP -1
last O -1
so O -1
long O -1
and O -1
that O -1
this O -1
machine O -1
has O -1
the O -1
snow B-ASP 2
lion I-ASP 2
! O -1

HOWEVER O -1
I O -1
chose O -1
two O -1
day O -1
shipping B-ASP 0
and O -1
it O -1
took O -1
over O -1
a O -1
week O -1
to O -1
arrive O -1
. O -1

it O -1
's O -1
exactly O -1
what O -1
i O -1
wanted O -1
, O -1
and O -1
it O -1
has O -1
all O -1
the O -1
new O -1
features B-ASP 2
and O -1
whatnot O -1
. O -1

Can O -1
you O -1
buy O -1
any O -1
laptop O -1
that O -1
matches O -1
the O -1
quality B-ASP 2
of O -1
a O -1
MacBook O -1
? O -1

It O -1
feels O -1
cheap O -1
, O -1
the O -1
keyboard B-ASP 0
is O -1
not O -1
very O -1
sensitive O -1
. O -1

Though O -1
please O -1
note O -1
that O -1
sometimes O -1
it O -1
crashes O -1
, O -1
and O -1
the O -1
sound B-ASP 0
quality I-ASP 0
isnt O -1
superb O -1
. O -1

It O -1
is O -1
very O -1
easy O -1
to O -1
navigate B-ASP 2
even O -1
for O -1
a O -1
novice O -1
. O -1

Does O -1
everything O -1
I O -1
need O -1
it O -1
to O -1
, O -1
has O -1
a O -1
wonderful O -1
battery B-ASP 2
life I-ASP 2
and O -1
I O -1
could O -1
n't O -1
be O -1
happier O -1
. O -1

Great O -1
Performance B-ASP 2
and O -1
Quality B-ASP -1
. O -1

Great O -1
Performance B-ASP -1
and O -1
Quality B-ASP 2
. O -1

I O -1
used O -1
windows B-ASP 1
XP I-ASP 1
, O -1
windows B-ASP -1
Vista I-ASP -1
, O -1
and O -1
Windows B-ASP -1
7 I-ASP -1
extensively O -1
. O -1

I O -1
used O -1
windows B-ASP -1
XP I-ASP -1
, O -1
windows B-ASP 1
Vista I-ASP 1
, O -1
and O -1
Windows B-ASP -1
7 I-ASP -1
extensively O -1
. O -1

I O -1
used O -1
windows B-ASP -1
XP I-ASP -1
, O -1
windows B-ASP -1
Vista I-ASP -1
, O -1
and O -1
Windows B-ASP 1
7 I-ASP 1
extensively O -1
. O -1

I O -1
did O -1
add O -1
a O -1
SSD B-ASP 1
drive I-ASP 1
and O -1
memory B-ASP -1

I O -1
did O -1
add O -1
a O -1
SSD B-ASP -1
drive I-ASP -1
and O -1
memory B-ASP 1

On O -1
start B-ASP 0
up I-ASP 0
it O -1
asks O -1
endless O -1
questions O -1
just O -1
so O -1
itune B-ASP -1
can O -1
sell O -1
you O -1
more O -1
of O -1
their O -1
products O -1
. O -1

On O -1
start B-ASP -1
up I-ASP -1
it O -1
asks O -1
endless O -1
questions O -1
just O -1
so O -1
itune B-ASP 0
can O -1
sell O -1
you O -1
more O -1
of O -1
their O -1
products O -1
. O -1

I O -1
Have O -1
been O -1
a O -1
Pc O -1
user O -1
for O -1
a O -1
very O -1
long O -1
time O -1
now O -1
but O -1
I O -1
will O -1
get O -1
used O -1
to O -1
this O -1
new O -1
OS B-ASP 1
. O -1

One O -1
more O -1
thing O -1
, O -1
this O -1
mac O -1
does O -1
NOT O -1
come O -1
with O -1
restore B-ASP 0
disks I-ASP 0
and O -1
I O -1
am O -1
not O -1
sure O -1
if O -1
you O -1
can O -1
make O -1
them O -1
direct O -1
from O -1
the O -1
mac O -1
like O -1
you O -1
can O -1
with O -1
newer O -1
PC O -1
's O -1
, O -1
also O -1
the O -1
charging B-ASP -1
cables I-ASP -1
are O -1
made O -1
of O -1
the O -1
same O -1
cheap O -1
material B-ASP -1
as O -1
the O -1
iPhone/iPod O -1
touch O -1
cables O -1
. O -1

One O -1
more O -1
thing O -1
, O -1
this O -1
mac O -1
does O -1
NOT O -1
come O -1
with O -1
restore B-ASP -1
disks I-ASP -1
and O -1
I O -1
am O -1
not O -1
sure O -1
if O -1
you O -1
can O -1
make O -1
them O -1
direct O -1
from O -1
the O -1
mac O -1
like O -1
you O -1
can O -1
with O -1
newer O -1
PC O -1
's O -1
, O -1
also O -1
the O -1
charging B-ASP 0
cables I-ASP 0
are O -1
made O -1
of O -1
the O -1
same O -1
cheap O -1
material B-ASP -1
as O -1
the O -1
iPhone/iPod O -1
touch O -1
cables O -1
. O -1

One O -1
more O -1
thing O -1
, O -1
this O -1
mac O -1
does O -1
NOT O -1
come O -1
with O -1
restore B-ASP -1
disks I-ASP -1
and O -1
I O -1
am O -1
not O -1
sure O -1
if O -1
you O -1
can O -1
make O -1
them O -1
direct O -1
from O -1
the O -1
mac O -1
like O -1
you O -1
can O -1
with O -1
newer O -1
PC O -1
's O -1
, O -1
also O -1
the O -1
charging B-ASP -1
cables I-ASP -1
are O -1
made O -1
of O -1
the O -1
same O -1
cheap O -1
material B-ASP 0
as O -1
the O -1
iPhone/iPod O -1
touch O -1
cables O -1
. O -1

I O -1
bought O -1
it O -1
to O -1
my O -1
son O -1
who O -1
uses O -1
it O -1
for O -1
graphic B-ASP 1
design I-ASP 1
. O -1

I O -1
never O -1
tried O -1
any O -1
external B-ASP 1
mics I-ASP 1
with O -1
that O -1
iMac O -1
. O -1

The O -1
new O -1
os B-ASP 2
is O -1
great O -1
on O -1
my O -1
macbook O -1
pro O -1
! O -1

I O -1
have O -1
experienced O -1
no O -1
problems O -1
, O -1
works B-ASP 2
as O -1
anticipated O -1
. O -1

System B-ASP 2
is O -1
running O -1
great O -1
. O -1

Easy O -1
to O -1
customize B-ASP 2
setting I-ASP 2
and O -1
even O -1
create O -1
your O -1
own O -1
bookmarks O -1
. O -1

Easy O -1
to O -1
customize O -1
setting O -1
and O -1
even O -1
create B-ASP 2
your I-ASP 2
own I-ASP 2
bookmarks I-ASP 2
. O -1

The O -1
MAC O -1
Mini O -1
, O -1
wireless B-ASP 1
keyboard I-ASP 1
/ I-ASP 1
mouse I-ASP 1
and O -1
a O -1
HDMI B-ASP -1
cable I-ASP -1
is O -1
all O -1
I O -1
need O -1
to O -1
get O -1
some O -1
real O -1
work O -1
done O -1
. O -1

The O -1
MAC O -1
Mini O -1
, O -1
wireless B-ASP -1
keyboard I-ASP -1
/ I-ASP -1
mouse I-ASP -1
and O -1
a O -1
HDMI B-ASP 1
cable I-ASP 1
is O -1
all O -1
I O -1
need O -1
to O -1
get O -1
some O -1
real O -1
work O -1
done O -1
. O -1

it O -1
has O -1
all O -1
the O -1
features B-ASP 2
that O -1
we O -1
expected O -1
and O -1
the O -1
price B-ASP -1
was O -1
good O -1
, O -1
working B-ASP -1
well O -1
so O -1
far O -1
. O -1

it O -1
has O -1
all O -1
the O -1
features B-ASP -1
that O -1
we O -1
expected O -1
and O -1
the O -1
price B-ASP 2
was O -1
good O -1
, O -1
working B-ASP -1
well O -1
so O -1
far O -1
. O -1

it O -1
has O -1
all O -1
the O -1
features B-ASP -1
that O -1
we O -1
expected O -1
and O -1
the O -1
price B-ASP -1
was O -1
good O -1
, O -1
working B-ASP 2
well O -1
so O -1
far O -1
. O -1

I O -1
work O -1
as O -1
a O -1
designer O -1
and O -1
coder O -1
and O -1
I O -1
needed O -1
a O -1
new O -1
buddy O -1
to O -1
work O -1
with O -1
, O -1
not O -1
gaming B-ASP 1
. O -1

The O -1
new O -1
operating B-ASP 2
system I-ASP 2
makes O -1
this O -1
computer O -1
into O -1
a O -1
super O -1
iPad O -1
. O -1

Easy O -1
to O -1
set B-ASP 2
up I-ASP 2
and O -1
go O -1
! O -1

I O -1
ca O -1
n't O -1
believe O -1
how O -1
quiet O -1
the O -1
hard B-ASP 2
drive I-ASP 2
is O -1
and O -1
how O -1
quick O -1
this O -1
thing O -1
boots B-ASP -1
up I-ASP -1
. O -1

I O -1
ca O -1
n't O -1
believe O -1
how O -1
quiet O -1
the O -1
hard B-ASP -1
drive I-ASP -1
is O -1
and O -1
how O -1
quick O -1
this O -1
thing O -1
boots B-ASP 2
up I-ASP 2
. O -1

The O -1
only O -1
issue O -1
came O -1
when O -1
I O -1
tried O -1
scanning B-ASP 0
to O -1
the O -1
mac O -1
. O -1

I O -1
think O -1
this O -1
is O -1
about O -1
as O -1
good O -1
as O -1
it O -1
gets O -1
at O -1
anything O -1
close O -1
to O -1
this O -1
price B-ASP 1
point I-ASP 1
. O -1

It O -1
's O -1
just O -1
what O -1
we O -1
were O -1
looking O -1
for O -1
and O -1
it O -1
works B-ASP 2
great O -1
. O -1

It O -1
's O -1
so O -1
quick O -1
and O -1
responsive O -1
that O -1
it O -1
makes O -1
working B-ASP 2
/ O -1
surfing B-ASP -1
on O -1
a O -1
computer O -1
so O -1
much O -1
more O -1
pleasurable O -1
! O -1

It O -1
's O -1
so O -1
quick O -1
and O -1
responsive O -1
that O -1
it O -1
makes O -1
working B-ASP -1
/ O -1
surfing B-ASP 2
on O -1
a O -1
computer O -1
so O -1
much O -1
more O -1
pleasurable O -1
! O -1

It O -1
works B-ASP 2
fine O -1
, O -1
and O -1
all O -1
the O -1
software B-ASP -1
seems O -1
to O -1
run O -1
pretty O -1
well O -1
. O -1

It O -1
works B-ASP -1
fine O -1
, O -1
and O -1
all O -1
the O -1
software B-ASP 2
seems O -1
to O -1
run O -1
pretty O -1
well O -1
. O -1

I O -1
'm O -1
using O -1
this O -1
computer O -1
for O -1
word B-ASP 1
processing I-ASP 1
, O -1
web B-ASP -1
browsing I-ASP -1
, O -1
some O -1
gaming B-ASP -1
, O -1
and O -1
I O -1
'm O -1
learning O -1
programming B-ASP -1
. O -1

I O -1
'm O -1
using O -1
this O -1
computer O -1
for O -1
word B-ASP -1
processing I-ASP -1
, O -1
web B-ASP 1
browsing I-ASP 1
, O -1
some O -1
gaming B-ASP -1
, O -1
and O -1
I O -1
'm O -1
learning O -1
programming B-ASP -1
. O -1

I O -1
'm O -1
using O -1
this O -1
computer O -1
for O -1
word B-ASP -1
processing I-ASP -1
, O -1
web B-ASP -1
browsing I-ASP -1
, O -1
some O -1
gaming B-ASP 1
, O -1
and O -1
I O -1
'm O -1
learning O -1
programming B-ASP -1
. O -1

I O -1
'm O -1
using O -1
this O -1
computer O -1
for O -1
word B-ASP -1
processing I-ASP -1
, O -1
web B-ASP -1
browsing I-ASP -1
, O -1
some O -1
gaming B-ASP -1
, O -1
and O -1
I O -1
'm O -1
learning O -1
programming B-ASP 1
. O -1

My O -1
wife O -1
was O -1
so O -1
excited O -1
to O -1
open O -1
the O -1
box O -1
, O -1
but O -1
quickly O -1
came O -1
to O -1
see O -1
that O -1
it O -1
did O -1
not O -1
function B-ASP 0
as O -1
it O -1
should O -1
. O -1

I O -1
wanted O -1
a O -1
computer O -1
that O -1
was O -1
quite O -1
, O -1
fast O -1
, O -1
and O -1
that O -1
had O -1
overall O -1
great O -1
performance B-ASP 1
. O -1

Apple B-ASP 0
"Help" I-ASP 0
is O -1
a O -1
mixed O -1
bag O -1
. O -1

It O -1
suddenly O -1
can O -1
not O -1
work B-ASP 0
. O -1

Harddrive B-ASP 0
was O -1
in O -1
poor O -1
condition O -1
, O -1
had O -1
to O -1
replace O -1
it O -1
. O -1

The O -1
on/off B-ASP 0
switch I-ASP 0
is O -1
a O -1
bit O -1
obscure O -1
in O -1
the O -1
rear O -1
corner O -1
. O -1

My O -1
only O -1
complaint O -1
is O -1
the O -1
total O -1
lack O -1
of O -1
instructions B-ASP 0
that O -1
come O -1
with O -1
the O -1
mac O -1
mini O -1
. O -1

The O -1
only O -1
task O -1
that O -1
this O -1
computer O -1
would O -1
not O -1
be O -1
good O -1
enough O -1
for O -1
would O -1
be O -1
gaming B-ASP 0
, O -1
otherwise O -1
the O -1
integrated O -1
Intel O -1
4000 O -1
graphics O -1
work O -1
well O -1
for O -1
other O -1
tasks O -1
. O -1

I O -1
use O -1
it O -1
mostly O -1
for O -1
content B-ASP 2
creation I-ASP 2
-LRB- O -1
Audio B-ASP -1
, O -1
video B-ASP -1
, O -1
photo B-ASP -1
editing I-ASP -1
-RRB- O -1
and O -1
its O -1
reliable O -1
. O -1

I O -1
use O -1
it O -1
mostly O -1
for O -1
content B-ASP -1
creation I-ASP -1
-LRB- O -1
Audio B-ASP 2
, O -1
video B-ASP -1
, O -1
photo B-ASP -1
editing I-ASP -1
-RRB- O -1
and O -1
its O -1
reliable O -1
. O -1

I O -1
use O -1
it O -1
mostly O -1
for O -1
content B-ASP -1
creation I-ASP -1
-LRB- O -1
Audio B-ASP -1
, O -1
video B-ASP 2
, O -1
photo B-ASP -1
editing I-ASP -1
-RRB- O -1
and O -1
its O -1
reliable O -1
. O -1

I O -1
use O -1
it O -1
mostly O -1
for O -1
content B-ASP -1
creation I-ASP -1
-LRB- O -1
Audio B-ASP -1
, O -1
video B-ASP -1
, O -1
photo B-ASP 2
editing I-ASP 2
-RRB- O -1
and O -1
its O -1
reliable O -1
. O -1

Screen B-ASP 2
is O -1
bright O -1
and O -1
gorgeous O -1
. O -1

The O -1
only O -1
solution O -1
is O -1
to O -1
turn O -1
the O -1
brightness B-ASP 1
down O -1
, O -1
etc. O -1
. O -1

If O -1
you O -1
want O -1
more O -1
information O -1
on O -1
macs O -1
I O -1
suggest O -1
going O -1
to O -1
apple.com O -1
and O -1
heading O -1
towards O -1
the O -1
macbook O -1
page O -1
for O -1
more O -1
information O -1
on O -1
the O -1
applications B-ASP 1
. O -1

It O -1
is O -1
robust O -1
, O -1
with O -1
a O -1
friendly O -1
use B-ASP 2
as O -1
all O -1
Apple O -1
products O -1
. O -1

It O -1
is O -1
fast O -1
and O -1
easy O -1
to O -1
use B-ASP 2
. O -1

And O -1
the O -1
fact O -1
that O -1
it O -1
comes O -1
with O -1
an O -1
i5 B-ASP 2
processor I-ASP 2
definitely O -1
speeds O -1
things O -1
up O -1

I O -1
have O -1
been O -1
PC O -1
for O -1
years O -1
but O -1
this O -1
computer O -1
is O -1
intuitive O -1
and O -1
its O -1
built B-ASP 2
in I-ASP 2
features I-ASP 2
are O -1
a O -1
great O -1
help O -1

Nice O -1
screen B-ASP 2
, O -1
keyboard B-ASP -1
works O -1
great O -1
! O -1

Nice O -1
screen B-ASP -1
, O -1
keyboard B-ASP 2
works O -1
great O -1
! O -1

I O -1
was O -1
amazed O -1
at O -1
how O -1
fast O -1
the O -1
delivery B-ASP 2
was O -1
. O -1

I O -1
've O -1
installed O -1
to O -1
it O -1
additional O -1
SSD B-ASP 1
and O -1
16Gb B-ASP -1
RAM I-ASP -1
. O -1

I O -1
've O -1
installed O -1
to O -1
it O -1
additional O -1
SSD B-ASP -1
and O -1
16Gb B-ASP 1
RAM I-ASP 1
. O -1

The O -1
memory B-ASP 0
was O -1
gone O -1
and O -1
it O -1
was O -1
not O -1
able O -1
to O -1
be O -1
used O -1
. O -1

It O -1
works B-ASP 2
great O -1
and O -1
I O -1
am O -1
so O -1
happy O -1
I O -1
bought O -1
it O -1
. O -1

I O -1
like O -1
the O -1
design B-ASP 2
and O -1
ease O -1
of O -1
use O -1
with O -1
the O -1
keyboard B-ASP -1
, O -1
plenty O -1
of O -1
ports B-ASP -1
. O -1

I O -1
like O -1
the O -1
design B-ASP -1
and O -1
ease O -1
of O -1
use O -1
with O -1
the O -1
keyboard B-ASP 2
, O -1
plenty O -1
of O -1
ports B-ASP -1
. O -1

I O -1
like O -1
the O -1
design B-ASP -1
and O -1
ease O -1
of O -1
use O -1
with O -1
the O -1
keyboard B-ASP -1
, O -1
plenty O -1
of O -1
ports B-ASP 2
. O -1

it O -1
definitely O -1
beats O -1
my O -1
old O -1
mac O -1
and O -1
the O -1
service B-ASP 2
was O -1
great O -1
. O -1

Web B-ASP 2
browsing I-ASP 2
is O -1
very O -1
quick O -1
with O -1
Safari B-ASP -1
browser I-ASP -1
. O -1

Web B-ASP -1
browsing I-ASP -1
is O -1
very O -1
quick O -1
with O -1
Safari B-ASP 2
browser I-ASP 2
. O -1

I O -1
like O -1
the O -1
lighted B-ASP 2
screen I-ASP 2
at O -1
night O -1
. O -1

It O -1
is O -1
really O -1
easy O -1
to O -1
use B-ASP 2
and O -1
it O -1
is O -1
quick O -1
to O -1
start B-ASP -1
up I-ASP -1
. O -1

It O -1
is O -1
really O -1
easy O -1
to O -1
use B-ASP -1
and O -1
it O -1
is O -1
quick O -1
to O -1
start B-ASP 2
up I-ASP 2
. O -1

I O -1
've O -1
lived O -1
with O -1
the O -1
crashes O -1
and O -1
slow O -1
operation B-ASP 0
and O -1
restarts O -1
. O -1

USB3 B-ASP 2
Peripherals I-ASP 2
are O -1
noticably O -1
less O -1
expensive O -1
than O -1
the O -1
ThunderBolt B-ASP -1
ones O -1
. O -1

USB3 B-ASP -1
Peripherals I-ASP -1
are O -1
noticably O -1
less O -1
expensive O -1
than O -1
the O -1
ThunderBolt B-ASP 0
ones O -1
. O -1

And O -1
mine O -1
had O -1
broke O -1
but O -1
I O -1
sent O -1
it O -1
in O -1
under O -1
warranty B-ASP 2
- O -1
no O -1
problems O -1
. O -1

It O -1
's O -1
fast O -1
, O -1
light O -1
, O -1
and O -1
is O -1
perfect O -1
for O -1
media B-ASP 2
editing I-ASP 2
, O -1
which O -1
is O -1
mostly O -1
why O -1
I O -1
bought O -1
it O -1
in O -1
the O -1
first O -1
place O -1
. O -1

The O -1
battery B-ASP 2
lasts O -1
as O -1
advertised O -1
-LRB- O -1
give O -1
or O -1
take O -1
15-20 O -1
minutes O -1
-RRB- O -1
, O -1
and O -1
the O -1
entire O -1
user B-ASP -1
experience I-ASP -1
is O -1
very O -1
elegant O -1
. O -1

The O -1
battery B-ASP -1
lasts O -1
as O -1
advertised O -1
-LRB- O -1
give O -1
or O -1
take O -1
15-20 O -1
minutes O -1
-RRB- O -1
, O -1
and O -1
the O -1
entire O -1
user B-ASP 2
experience I-ASP 2
is O -1
very O -1
elegant O -1
. O -1

Thanks O -1
for O -1
the O -1
fast O -1
shipment B-ASP 2
and O -1
great O -1
price B-ASP -1
. O -1

Thanks O -1
for O -1
the O -1
fast O -1
shipment B-ASP -1
and O -1
great O -1
price B-ASP 2
. O -1

! O -1
Excelent O -1
performance B-ASP 2
, O -1
usability B-ASP -1
, O -1
presentation B-ASP -1
and O -1
time B-ASP -1
response I-ASP -1
. O -1

! O -1
Excelent O -1
performance B-ASP -1
, O -1
usability B-ASP 2
, O -1
presentation B-ASP -1
and O -1
time B-ASP -1
response I-ASP -1
. O -1

! O -1
Excelent O -1
performance B-ASP -1
, O -1
usability B-ASP -1
, O -1
presentation B-ASP 2
and O -1
time B-ASP -1
response I-ASP -1
. O -1

! O -1
Excelent O -1
performance B-ASP -1
, O -1
usability B-ASP -1
, O -1
presentation B-ASP -1
and O -1
time B-ASP 2
response I-ASP 2
. O -1

The O -1
smaller O -1
size B-ASP 2
was O -1
a O -1
bonus O -1
because O -1
of O -1
space O -1
restrictions O -1
. O -1

I O -1
blame O -1
the O -1
Mac B-ASP 0
OS I-ASP 0
. O -1

In O -1
fact O -1
I O -1
still O -1
use O -1
many O -1
Legacy B-ASP 1
programs I-ASP 1
-LRB- O -1
Appleworks B-ASP -1
, O -1
FileMaker B-ASP -1
Pro I-ASP -1
, O -1
Quicken B-ASP -1
, O -1
Photoshop B-ASP -1
etc O -1
-RRB- O -1
! O -1

In O -1
fact O -1
I O -1
still O -1
use O -1
many O -1
Legacy B-ASP -1
programs I-ASP -1
-LRB- O -1
Appleworks B-ASP 1
, O -1
FileMaker B-ASP -1
Pro I-ASP -1
, O -1
Quicken B-ASP -1
, O -1
Photoshop B-ASP -1
etc O -1
-RRB- O -1
! O -1

In O -1
fact O -1
I O -1
still O -1
use O -1
many O -1
Legacy B-ASP -1
programs I-ASP -1
-LRB- O -1
Appleworks B-ASP -1
, O -1
FileMaker B-ASP 1
Pro I-ASP 1
, O -1
Quicken B-ASP -1
, O -1
Photoshop B-ASP -1
etc O -1
-RRB- O -1
! O -1

In O -1
fact O -1
I O -1
still O -1
use O -1
many O -1
Legacy B-ASP -1
programs I-ASP -1
-LRB- O -1
Appleworks B-ASP -1
, O -1
FileMaker B-ASP -1
Pro I-ASP -1
, O -1
Quicken B-ASP 1
, O -1
Photoshop B-ASP -1
etc O -1
-RRB- O -1
! O -1

In O -1
fact O -1
I O -1
still O -1
use O -1
many O -1
Legacy B-ASP -1
programs I-ASP -1
-LRB- O -1
Appleworks B-ASP -1
, O -1
FileMaker B-ASP -1
Pro I-ASP -1
, O -1
Quicken B-ASP -1
, O -1
Photoshop B-ASP 1
etc O -1
-RRB- O -1
! O -1

I O -1
like O -1
the O -1
operating B-ASP 2
system I-ASP 2
. O -1

I O -1
like O -1
the O -1
operating B-ASP 2
system I-ASP 2
. O -1

It O -1
's O -1
fast O -1
at O -1
loading B-ASP 2
the I-ASP 2
internet I-ASP 2
. O -1

So O -1
much O -1
faster O -1
and O -1
sleeker O -1
looking B-ASP 2
. O -1

Unfortunately O -1
, O -1
it O -1
runs O -1
XP B-ASP 1
and O -1
Microsoft O -1
is O -1
dropping O -1
support B-ASP -1
next O -1
April O -1
. O -1

Unfortunately O -1
, O -1
it O -1
runs O -1
XP B-ASP -1
and O -1
Microsoft O -1
is O -1
dropping O -1
support B-ASP 0
next O -1
April O -1
. O -1

First O -1
off O -1
, O -1
I O -1
really O -1
do O -1
like O -1
my O -1
MBP O -1
... O -1
once O -1
used O -1
to O -1
the O -1
OS B-ASP 2
it O -1
is O -1
pretty O -1
easy O -1
to O -1
get O -1
around O -1
, O -1
and O -1
the O -1
overall B-ASP -1
build I-ASP -1
is O -1
great O -1
... O -1
eg O -1
the O -1
keyboard B-ASP -1
is O -1
one O -1
of O -1
the O -1
best O -1
to O -1
type O -1
on O -1
. O -1

First O -1
off O -1
, O -1
I O -1
really O -1
do O -1
like O -1
my O -1
MBP O -1
... O -1
once O -1
used O -1
to O -1
the O -1
OS B-ASP -1
it O -1
is O -1
pretty O -1
easy O -1
to O -1
get O -1
around O -1
, O -1
and O -1
the O -1
overall B-ASP 2
build I-ASP 2
is O -1
great O -1
... O -1
eg O -1
the O -1
keyboard B-ASP -1
is O -1
one O -1
of O -1
the O -1
best O -1
to O -1
type O -1
on O -1
. O -1

First O -1
off O -1
, O -1
I O -1
really O -1
do O -1
like O -1
my O -1
MBP O -1
... O -1
once O -1
used O -1
to O -1
the O -1
OS B-ASP -1
it O -1
is O -1
pretty O -1
easy O -1
to O -1
get O -1
around O -1
, O -1
and O -1
the O -1
overall B-ASP -1
build I-ASP -1
is O -1
great O -1
... O -1
eg O -1
the O -1
keyboard B-ASP 2
is O -1
one O -1
of O -1
the O -1
best O -1
to O -1
type O -1
on O -1
. O -1

It O -1
is O -1
made O -1
of O -1
such O -1
solid O -1
construction B-ASP 2
and O -1
since O -1
I O -1
have O -1
never O -1
had O -1
a O -1
Mac O -1
using O -1
my O -1
iPhone O -1
helped O -1
me O -1
get O -1
used O -1
to O -1
the O -1
system B-ASP -1
a O -1
bit O -1
. O -1

It O -1
is O -1
made O -1
of O -1
such O -1
solid O -1
construction B-ASP -1
and O -1
since O -1
I O -1
have O -1
never O -1
had O -1
a O -1
Mac O -1
using O -1
my O -1
iPhone O -1
helped O -1
me O -1
get O -1
used O -1
to O -1
the O -1
system B-ASP 1
a O -1
bit O -1
. O -1

Very O -1
nice O -1
unibody B-ASP 2
construction I-ASP 2
. O -1

This O -1
Macbook O -1
Pro O -1
is O -1
fast O -1
, O -1
powerful O -1
, O -1
and O -1
runs B-ASP 2
super O -1
quiet O -1
and O -1
cool O -1
. O -1

It O -1
's O -1
ok O -1
but O -1
does O -1
n't O -1
have O -1
a O -1
disk B-ASP 1
drive I-ASP 1
which O -1
I O -1
did O -1
n't O -1
know O -1
until O -1
after O -1
I O -1
bought O -1
it O -1
. O -1

There O -1
is O -1
no O -1
HDMI B-ASP 1
receptacle I-ASP 1
, O -1
nor O -1
is O -1
there O -1
an O -1
SD B-ASP -1
card I-ASP -1
slot I-ASP -1
located O -1
anywhere O -1
on O -1
the O -1
device O -1
. O -1

There O -1
is O -1
no O -1
HDMI B-ASP -1
receptacle I-ASP -1
, O -1
nor O -1
is O -1
there O -1
an O -1
SD B-ASP 1
card I-ASP 1
slot I-ASP 1
located O -1
anywhere O -1
on O -1
the O -1
device O -1
. O -1

It O -1
came O -1
in O -1
brand O -1
new O -1
and O -1
works B-ASP 2
perfectly O -1
. O -1

It O -1
should O -1
n't O -1
happen O -1
like O -1
that O -1
, O -1
I O -1
do O -1
n't O -1
have O -1
any O -1
design B-ASP 1
app I-ASP 1
open O -1
or O -1
anything O -1
. O -1

MY O -1
TRACKPAD B-ASP 0
IS O -1
NOT O -1
WORKING O -1
. O -1

It O -1
looks B-ASP -1
and O -1
feels B-ASP -1
solid O -1
, O -1
with O -1
a O -1
flawless O -1
finish B-ASP 2
. O -1

It O -1
looks B-ASP 2
and O -1
feels B-ASP -1
solid O -1
, O -1
with O -1
a O -1
flawless O -1
finish B-ASP -1
. O -1

It O -1
looks B-ASP -1
and O -1
feels B-ASP 2
solid O -1
, O -1
with O -1
a O -1
flawless O -1
finish B-ASP -1
. O -1

Price B-ASP 0
was O -1
higher O -1
when O -1
purchased O -1
on O -1
MAC O -1
when O -1
compared O -1
to O -1
price B-ASP -1
showing O -1
on O -1
PC O -1
when O -1
I O -1
bought O -1
this O -1
product O -1
. O -1

Price B-ASP -1
was O -1
higher O -1
when O -1
purchased O -1
on O -1
MAC O -1
when O -1
compared O -1
to O -1
price B-ASP 2
showing O -1
on O -1
PC O -1
when O -1
I O -1
bought O -1
this O -1
product O -1
. O -1

Then O -1
the O -1
system B-ASP 0
would O -1
many O -1
times O -1
not O -1
power B-ASP -1
down I-ASP -1
without O -1
a O -1
forced O -1
power-off O -1
. O -1

Then O -1
the O -1
system B-ASP -1
would O -1
many O -1
times O -1
not O -1
power B-ASP 0
down I-ASP 0
without O -1
a O -1
forced O -1
power-off O -1
. O -1

The O -1
configuration B-ASP 2
is O -1
perfect O -1
for O -1
my O -1
needs O -1
. O -1

and O -1
the O -1
speakers B-ASP 0
is O -1
the O -1
worst O -1
ever O -1
. O -1

Its O -1
the O -1
best O -1
, O -1
its O -1
got O -1
the O -1
looks B-ASP 2
, O -1
super O -1
easy O -1
to O -1
use B-ASP -1
and O -1
love O -1
all O -1
you O -1
can O -1
do O -1
with O -1
the O -1
trackpad B-ASP -1
! O -1
. O -1
. O -1

Its O -1
the O -1
best O -1
, O -1
its O -1
got O -1
the O -1
looks B-ASP -1
, O -1
super O -1
easy O -1
to O -1
use B-ASP 2
and O -1
love O -1
all O -1
you O -1
can O -1
do O -1
with O -1
the O -1
trackpad B-ASP -1
! O -1
. O -1
. O -1

Its O -1
the O -1
best O -1
, O -1
its O -1
got O -1
the O -1
looks B-ASP -1
, O -1
super O -1
easy O -1
to O -1
use B-ASP -1
and O -1
love O -1
all O -1
you O -1
can O -1
do O -1
with O -1
the O -1
trackpad B-ASP 2
! O -1
. O -1
. O -1

Web B-ASP 2
surfuring I-ASP 2
is O -1
smooth O -1
and O -1
seamless O -1
. O -1

I O -1
'm O -1
overall O -1
pleased O -1
with O -1
the O -1
interface B-ASP 2
and O -1
the O -1
portability B-ASP -1
of O -1
this O -1
product O -1
. O -1

I O -1
'm O -1
overall O -1
pleased O -1
with O -1
the O -1
interface B-ASP -1
and O -1
the O -1
portability B-ASP 2
of O -1
this O -1
product O -1
. O -1

This O -1
item O -1
is O -1
a O -1
beautiful O -1
piece O -1
, O -1
it O -1
works B-ASP 2
well O -1
, O -1
it O -1
is O -1
easy O -1
to O -1
carry B-ASP -1
and O -1
handle B-ASP -1
. O -1

This O -1
item O -1
is O -1
a O -1
beautiful O -1
piece O -1
, O -1
it O -1
works B-ASP -1
well O -1
, O -1
it O -1
is O -1
easy O -1
to O -1
carry B-ASP 2
and O -1
handle B-ASP -1
. O -1

This O -1
item O -1
is O -1
a O -1
beautiful O -1
piece O -1
, O -1
it O -1
works B-ASP -1
well O -1
, O -1
it O -1
is O -1
easy O -1
to O -1
carry B-ASP -1
and O -1
handle B-ASP 2
. O -1

It O -1
was O -1
also O -1
suffering O -1
from O -1
hardware B-ASP -1
-LRB- I-ASP -1
keyboard I-ASP -1
-RRB- I-ASP -1
issues O -1
, O -1
relatively O -1
slow O -1
performance B-ASP 0
and O -1
shortening O -1
battery B-ASP -1
lifetime I-ASP -1
. O -1

It O -1
was O -1
also O -1
suffering O -1
from O -1
hardware B-ASP -1
-LRB- I-ASP -1
keyboard I-ASP -1
-RRB- I-ASP -1
issues O -1
, O -1
relatively O -1
slow O -1
performance B-ASP -1
and O -1
shortening O -1
battery B-ASP 0
lifetime I-ASP 0
. O -1

It O -1
was O -1
also O -1
suffering O -1
from O -1
hardware B-ASP 0
-LRB- I-ASP 0
keyboard I-ASP 0
-RRB- I-ASP 0
issues O -1
, O -1
relatively O -1
slow O -1
performance B-ASP -1
and O -1
shortening O -1
battery B-ASP -1
lifetime I-ASP -1
. O -1

Runs B-ASP 2
good O -1
and O -1
does O -1
the O -1
job O -1
, O -1
can't O -1
complain O -1
about O -1
that O -1
! O -1

It O -1
's O -1
silent O -1
and O -1
has O -1
a O -1
very O -1
small O -1
footprint B-ASP 2
on O -1
my O -1
desk O -1
. O -1

The O -1
exterior B-ASP 2
is O -1
absolutely O -1
gorgeous O -1
. O -1

It O -1
has O -1
a O -1
very O -1
high O -1
performance B-ASP 2
, O -1
just O -1
for O -1
what O -1
I O -1
needed O -1
for O -1
. O -1

Apple O -1
is O -1
aware O -1
of O -1
this O -1
issue O -1
and O -1
this O -1
is O -1
either O -1
old O -1
stock O -1
or O -1
a O -1
defective O -1
design B-ASP -1
involving O -1
the O -1
intel B-ASP 1
4000 I-ASP 1
graphics I-ASP 1
chipset I-ASP 1
. O -1

Apple O -1
is O -1
aware O -1
of O -1
this O -1
issue O -1
and O -1
this O -1
is O -1
either O -1
old O -1
stock O -1
or O -1
a O -1
defective O -1
design B-ASP 1
involving O -1
the O -1
intel B-ASP -1
4000 I-ASP -1
graphics I-ASP -1
chipset I-ASP -1
. O -1

OSX B-ASP 1
Mountain I-ASP 1
Lion I-ASP 1
soon O -1
to O -1
upgrade O -1
to O -1
Mavericks O -1
. O -1

OSX O -1
Mountain O -1
Lion O -1
soon O -1
to O -1
upgrade O -1
to O -1
Mavericks B-ASP 1
. O -1

I O -1
just O -1
bought O -1
the O -1
new O -1
MacBook O -1
Pro O -1
, O -1
the O -1
13 O -1
'' O -1
model O -1
, O -1
and O -1
I O -1
ca O -1
n't O -1
believe O -1
Apple O -1
keeps O -1
making O -1
the O -1
same O -1
mistake O -1
with O -1
regard O -1
to O -1
USB B-ASP 0
ports I-ASP 0
. O -1

It O -1
wakes B-ASP -1
in O -1
less O -1
than O -1
a O -1
second O -1
when O -1
I O -1
open O -1
the O -1
lid B-ASP 1
. O -1

It O -1
wakes B-ASP 2
in O -1
less O -1
than O -1
a O -1
second O -1
when O -1
I O -1
open O -1
the O -1
lid B-ASP -1
. O -1

It O -1
is O -1
the O -1
perfect O -1
size B-ASP 2
and O -1
speed B-ASP -1
for O -1
me O -1
. O -1

It O -1
is O -1
the O -1
perfect O -1
size B-ASP -1
and O -1
speed B-ASP 2
for O -1
me O -1
. O -1

THE O -1
CUSTOMER B-ASP 2
SERVICE I-ASP 2
IS O -1
TERRIFIC O -1
!! O -1

My O -1
last O -1
laptop O -1
was O -1
a O -1
17 O -1
'' O -1
ASUS O -1
gaming O -1
machine O -1
, O -1
which O -1
performed B-ASP 2
admirably O -1
, O -1
but O -1
having O -1
since O -1
built O -1
my O -1
own O -1
desktop O -1
and O -1
really O -1
settling O -1
into O -1
the O -1
college O -1
life O -1
, O -1
I O -1
found O -1
myself O -1
wanting O -1
something O -1
smaller O -1
and O -1
less O -1
cumbersome O -1
, O -1
not O -1
to O -1
mention O -1
that O -1
the O -1
ASUS O -1
had O -1
been O -1
slowly O -1
developing O -1
problems O -1
ever O -1
since O -1
I O -1
bought O -1
it O -1
about O -1
4 O -1
years O -1
ago O -1
. O -1

However O -1
, O -1
it O -1
did O -1
not O -1
have O -1
any O -1
scratches O -1
, O -1
ZERO O -1
battery B-ASP 2
cycle I-ASP 2
count I-ASP 2
-LRB- O -1
pretty O -1
surprised O -1
-RRB- O -1
, O -1
and O -1
all O -1
the O -1
hardware B-ASP -1
seemed O -1
to O -1
be O -1
working O -1
perfectly O -1
. O -1

However O -1
, O -1
it O -1
did O -1
not O -1
have O -1
any O -1
scratches O -1
, O -1
ZERO O -1
battery B-ASP -1
cycle I-ASP -1
count I-ASP -1
-LRB- O -1
pretty O -1
surprised O -1
-RRB- O -1
, O -1
and O -1
all O -1
the O -1
hardware B-ASP 2
seemed O -1
to O -1
be O -1
working O -1
perfectly O -1
. O -1

After O -1
fumbling O -1
around O -1
with O -1
the O -1
OS B-ASP 0
I O -1
started O -1
searching O -1
the O -1
internet O -1
for O -1
a O -1
fix O -1
and O -1
found O -1
a O -1
number O -1
of O -1
forums O -1
on O -1
fixing O -1
the O -1
issue O -1
. O -1

And O -1
as O -1
for O -1
all O -1
the O -1
fancy O -1
finger B-ASP 0
swipes I-ASP 0
-- O -1
I O -1
just O -1
gave O -1
up O -1
and O -1
attached O -1
a O -1
mouse B-ASP -1
. O -1

And O -1
as O -1
for O -1
all O -1
the O -1
fancy O -1
finger B-ASP -1
swipes I-ASP -1
-- O -1
I O -1
just O -1
gave O -1
up O -1
and O -1
attached O -1
a O -1
mouse B-ASP 1
. O -1

I O -1
needed O -1
a O -1
laptop O -1
with O -1
big O -1
storage B-ASP 1
, O -1
a O -1
nice O -1
screen B-ASP -1
and O -1
fast O -1
so O -1
I O -1
can O -1
photoshop B-ASP -1
without O -1
any O -1
problem O -1
. O -1

I O -1
needed O -1
a O -1
laptop O -1
with O -1
big O -1
storage B-ASP -1
, O -1
a O -1
nice O -1
screen B-ASP 1
and O -1
fast O -1
so O -1
I O -1
can O -1
photoshop B-ASP -1
without O -1
any O -1
problem O -1
. O -1

I O -1
needed O -1
a O -1
laptop O -1
with O -1
big O -1
storage B-ASP -1
, O -1
a O -1
nice O -1
screen B-ASP -1
and O -1
fast O -1
so O -1
I O -1
can O -1
photoshop B-ASP 1
without O -1
any O -1
problem O -1
. O -1

I O -1
like O -1
coming O -1
back O -1
to O -1
Mac B-ASP 2
OS I-ASP 2
but O -1
this O -1
laptop O -1
is O -1
lacking O -1
in O -1
speaker B-ASP -1
quality I-ASP -1
compared O -1
to O -1
my O -1
$ O -1
400 O -1
old O -1
HP O -1
laptop O -1
. O -1

I O -1
like O -1
coming O -1
back O -1
to O -1
Mac B-ASP -1
OS I-ASP -1
but O -1
this O -1
laptop O -1
is O -1
lacking O -1
in O -1
speaker B-ASP 0
quality I-ASP 0
compared O -1
to O -1
my O -1
$ O -1
400 O -1
old O -1
HP O -1
laptop O -1
. O -1

Shipped B-ASP 2
very O -1
quickly O -1
and O -1
safely O -1
. O -1

The O -1
thunderbolt B-ASP 2
port I-ASP 2
is O -1
awesome O -1
! O -1

The O -1
performance B-ASP 2
is O -1
definitely O -1
superior O -1
to O -1
any O -1
computer O -1
I O -1
've O -1
ever O -1
put O -1
my O -1
hands O -1
on O -1
. O -1

It O -1
's O -1
great O -1
for O -1
streaming B-ASP 2
video I-ASP 2
and O -1
other O -1
entertainment B-ASP -1
uses I-ASP -1
. O -1

It O -1
's O -1
great O -1
for O -1
streaming B-ASP -1
video I-ASP -1
and O -1
other O -1
entertainment B-ASP 2
uses I-ASP 2
. O -1

I O -1
like O -1
the O -1
design B-ASP 2
and O -1
its O -1
features B-ASP -1
but O -1
there O -1
are O -1
somethings O -1
I O -1
think O -1
needs O -1
to O -1
be O -1
improved O -1
. O -1

I O -1
like O -1
the O -1
design B-ASP -1
and O -1
its O -1
features B-ASP 2
but O -1
there O -1
are O -1
somethings O -1
I O -1
think O -1
needs O -1
to O -1
be O -1
improved O -1
. O -1

There O -1
were O -1
small O -1
problems O -1
with O -1
Mac B-ASP 0
office I-ASP 0
. O -1

I O -1
understand O -1
I O -1
should O -1
call O -1
Apple B-ASP 1
Tech I-ASP 1
Support I-ASP 1
about O -1
any O -1
variables O -1
-LRB- O -1
which O -1
is O -1
my O -1
purpose O -1
of O -1
writing O -1
this O -1
con O -1
-RRB- O -1
as O -1
variables O -1
could O -1
be O -1
a O -1
bigger O -1
future O -1
problem O -1
. O -1

I O -1
ordered O -1
my O -1
2012 O -1
mac O -1
mini O -1
after O -1
being O -1
disappointed O -1
with O -1
spec B-ASP 0
of O -1
the O -1
new O -1
27 O -1
'' O -1
Imacs O -1
. O -1

It O -1
still O -1
works B-ASP 2
and O -1
it O -1
's O -1
extremely O -1
user O -1
friendly O -1
, O -1
so O -1
I O -1
would O -1
recommend O -1
it O -1
for O -1
new O -1
computer O -1
user O -1
and O -1
also O -1
for O -1
those O -1
who O -1
are O -1
just O -1
looking O -1
for O -1
a O -1
more O -1
efficient O -1
way O -1
to O -1
do O -1
things O -1

Its O -1
fast O -1
, O -1
easy O -1
to O -1
use B-ASP 2
and O -1
it O -1
looks B-ASP -1
great O -1
. O -1

Its O -1
fast O -1
, O -1
easy O -1
to O -1
use B-ASP -1
and O -1
it O -1
looks B-ASP 2
great O -1
. O -1

-LRB- O -1
but O -1
Office B-ASP 1
can O -1
be O -1
purchased O -1
-RRB- O -1
IF O -1
I O -1
ever O -1
need O -1
a O -1
laptop O -1
again O -1
I O -1
am O -1
for O -1
sure O -1
purchasing O -1
another O -1
Toshiba O -1
!! O -1

I O -1
have O -1
n't O -1
tried O -1
the O -1
one O -1
with O -1
retina B-ASP 1
display I-ASP 1
... O -1
Maybe O -1
in O -1
the O -1
future O -1
. O -1

Performance B-ASP 2
is O -1
much O -1
much O -1
better O -1
on O -1
the O -1
Pro O -1
, O -1
especially O -1
if O -1
you O -1
install O -1
an O -1
SSD B-ASP -1
on O -1
it O -1
. O -1

Performance B-ASP -1
is O -1
much O -1
much O -1
better O -1
on O -1
the O -1
Pro O -1
, O -1
especially O -1
if O -1
you O -1
install O -1
an O -1
SSD B-ASP 2
on O -1
it O -1
. O -1

Note O -1
, O -1
however O -1
, O -1
that O -1
any O -1
existing O -1
MagSafe B-ASP 1
accessories I-ASP 1
you O -1
have O -1
will O -1
not O -1
work O -1
with O -1
the O -1
MagSafe B-ASP -1
2 I-ASP -1
connection I-ASP -1
. O -1

Note O -1
, O -1
however O -1
, O -1
that O -1
any O -1
existing O -1
MagSafe B-ASP -1
accessories I-ASP -1
you O -1
have O -1
will O -1
not O -1
work O -1
with O -1
the O -1
MagSafe B-ASP 0
2 I-ASP 0
connection I-ASP 0
. O -1

The O -1
only O -1
thing O -1
I O -1
dislike O -1
is O -1
the O -1
touchpad B-ASP 0
, O -1
alot O -1
of O -1
the O -1
times O -1
its O -1
unresponsive O -1
and O -1
does O -1
things O -1
I O -1
dont O -1
want O -1
it O -1
too O -1
, O -1
I O -1
would O -1
recommend O -1
using O -1
a O -1
mouse B-ASP -1
with O -1
it O -1
. O -1

The O -1
only O -1
thing O -1
I O -1
dislike O -1
is O -1
the O -1
touchpad B-ASP -1
, O -1
alot O -1
of O -1
the O -1
times O -1
its O -1
unresponsive O -1
and O -1
does O -1
things O -1
I O -1
dont O -1
want O -1
it O -1
too O -1
, O -1
I O -1
would O -1
recommend O -1
using O -1
a O -1
mouse B-ASP 1
with O -1
it O -1
. O -1

The O -1
Mac O -1
mini O -1
is O -1
about O -1
8x O -1
smaller O -1
than O -1
my O -1
old O -1
computer O -1
which O -1
is O -1
a O -1
huge O -1
bonus O -1
and O -1
runs B-ASP 2
very O -1
quiet O -1
, O -1
actually O -1
the O -1
fans B-ASP -1
are O -1
n't O -1
audible O -1
unlike O -1
my O -1
old O -1
pc O -1

The O -1
Mac O -1
mini O -1
is O -1
about O -1
8x O -1
smaller O -1
than O -1
my O -1
old O -1
computer O -1
which O -1
is O -1
a O -1
huge O -1
bonus O -1
and O -1
runs B-ASP -1
very O -1
quiet O -1
, O -1
actually O -1
the O -1
fans B-ASP 2
are O -1
n't O -1
audible O -1
unlike O -1
my O -1
old O -1
pc O -1

MAYBE O -1
The O -1
Mac B-ASP 0
OS I-ASP 0
improvement O -1
were O -1
not O -1
The O -1
product O -1
they O -1
Want O -1
to O -1
offer O -1
. O -1

I O -1
thought O -1
the O -1
transition O -1
would O -1
be O -1
difficult O -1
at O -1
best O -1
and O -1
would O -1
take O -1
some O -1
time O -1
to O -1
fully O -1
familiarize O -1
myself O -1
with O -1
the O -1
new O -1
Mac B-ASP 1
ecosystem I-ASP 1
. O -1

It O -1
's O -1
absolutely O -1
wonderful O -1
and O -1
worth O -1
the O -1
price B-ASP 2
! O -1

I O -1
am O -1
please O -1
with O -1
the O -1
products O -1
ease O -1
of O -1
use B-ASP 2
; O -1
out O -1
of O -1
the O -1
box O -1
ready O -1
; O -1
appearance B-ASP -1
and O -1
functionality B-ASP -1
. O -1

I O -1
am O -1
please O -1
with O -1
the O -1
products O -1
ease O -1
of O -1
use B-ASP -1
; O -1
out O -1
of O -1
the O -1
box O -1
ready O -1
; O -1
appearance B-ASP 2
and O -1
functionality B-ASP -1
. O -1

I O -1
am O -1
please O -1
with O -1
the O -1
products O -1
ease O -1
of O -1
use B-ASP -1
; O -1
out O -1
of O -1
the O -1
box O -1
ready O -1
; O -1
appearance B-ASP -1
and O -1
functionality B-ASP 2
. O -1

Perfect O -1
for O -1
all O -1
my O -1
graphic B-ASP 2
design I-ASP 2
classes O -1
I O -1
'm O -1
taking O -1
this O -1
year O -1
in O -1
college O -1
: O -1
- O -1
-RRB- O -1

I O -1
will O -1
not O -1
be O -1
using O -1
that O -1
slot B-ASP 0
again O -1
. O -1

The O -1
OS B-ASP 2
is O -1
fast O -1
and O -1
fluid O -1
, O -1
everything O -1
is O -1
organized O -1
and O -1
it O -1
's O -1
just O -1
beautiful O -1
. O -1

They O -1
are O -1
simpler O -1
to O -1
use B-ASP 2
. O -1

! O -1
so O -1
nice O -1
. O -1
. O -1
stable O -1
. O -1
. O -1
fast O -1
. O -1
. O -1
now O -1
i O -1
got O -1
my O -1
SSD B-ASP 2
! O -1

Also O -1
, O -1
in O -1
using O -1
the O -1
built-in B-ASP 1
camera I-ASP 1
, O -1
my O -1
voice B-ASP -1
recording I-ASP -1
for O -1
my O -1
vlog O -1
sounds O -1
like O -1
the O -1
interplanetary O -1
transmissions O -1
in O -1
the O -1
`` O -1
Star O -1
Wars O -1
'' O -1
saga O -1
. O -1

Also O -1
, O -1
in O -1
using O -1
the O -1
built-in B-ASP -1
camera I-ASP -1
, O -1
my O -1
voice B-ASP 0
recording I-ASP 0
for O -1
my O -1
vlog O -1
sounds O -1
like O -1
the O -1
interplanetary O -1
transmissions O -1
in O -1
the O -1
`` O -1
Star O -1
Wars O -1
'' O -1
saga O -1
. O -1

I O -1
love O -1
the O -1
quick O -1
start B-ASP 2
up I-ASP 2
. O -1

You O -1
just O -1
can O -1
not O -1
beat O -1
the O -1
functionality B-ASP 2
of O -1
an O -1
Apple O -1
device O -1
. O -1

Yet O -1
my O -1
mac O -1
continues O -1
to O -1
function B-ASP 2
properly O -1
. O -1

Graphics B-ASP 2
are O -1
much O -1
improved O -1
. O -1

Here O -1
are O -1
the O -1
things O -1
that O -1
made O -1
me O -1
confident O -1
with O -1
my O -1
purchase O -1
: O -1
Build B-ASP 2
Quality I-ASP 2
- O -1
Seriously O -1
, O -1
you O -1
ca O -1
n't O -1
beat O -1
a O -1
unibody B-ASP -1
construction I-ASP -1
. O -1

Here O -1
are O -1
the O -1
things O -1
that O -1
made O -1
me O -1
confident O -1
with O -1
my O -1
purchase O -1
: O -1
Build B-ASP -1
Quality I-ASP -1
- O -1
Seriously O -1
, O -1
you O -1
ca O -1
n't O -1
beat O -1
a O -1
unibody B-ASP 2
construction I-ASP 2
. O -1

It O -1
provides O -1
much O -1
more O -1
flexibility B-ASP 2
for I-ASP 2
connectivity I-ASP 2
. O -1

I O -1
want O -1
the O -1
portability B-ASP 2
of O -1
a O -1
tablet O -1
, O -1
without O -1
the O -1
limitations O -1
of O -1
a O -1
tablet O -1
and O -1
that O -1
's O -1
where O -1
this O -1
laptop O -1
comes O -1
into O -1
play O -1
. O -1

Mac B-ASP 2
tutorials I-ASP 2
do O -1
help O -1
. O -1

The O -1
technical B-ASP 0
support I-ASP 0
was O -1
not O -1
helpful O -1
as O -1
well O -1
. O -1

I O -1
got O -1
the O -1
new O -1
adapter B-ASP 1
and O -1
there O -1
was O -1
no O -1
change O -1
. O -1

so O -1
i O -1
called O -1
technical B-ASP 1
support I-ASP 1
. O -1

Came O -1
with O -1
iPhoto B-ASP 1
and O -1
garage B-ASP -1
band I-ASP -1
already O -1
loaded O -1
. O -1

Came O -1
with O -1
iPhoto B-ASP -1
and O -1
garage B-ASP 1
band I-ASP 1
already O -1
loaded O -1
. O -1

Logic B-ASP 2
board I-ASP 2
utterly O -1
fried O -1
, O -1
cried O -1
, O -1
and O -1
laid O -1
down O -1
and O -1
died O -1
. O -1

The O -1
sound B-ASP 0
was O -1
crappy O -1
even O -1
when O -1
you O -1
turn O -1
up O -1
the O -1
volume B-ASP -1
still O -1
the O -1
same O -1
results O -1
. O -1

The O -1
sound B-ASP -1
was O -1
crappy O -1
even O -1
when O -1
you O -1
turn O -1
up O -1
the O -1
volume B-ASP 0
still O -1
the O -1
same O -1
results O -1
. O -1

OSX B-ASP 2
Lion I-ASP 2
is O -1
a O -1
great O -1
performer O -1
. O -1
. O -1
extremely O -1
fast O -1
and O -1
reliable O -1
. O -1

Having O -1
heard O -1
from O -1
friends O -1
and O -1
family O -1
about O -1
how O -1
reliable O -1
a O -1
Mac O -1
product O -1
is O -1
, O -1
I O -1
never O -1
expected O -1
to O -1
have O -1
an O -1
application B-ASP 0
crash O -1
within O -1
the O -1
first O -1
month O -1
, O -1
but O -1
I O -1
did O -1
. O -1

The O -1
Macbook O -1
pro O -1
's O -1
physical B-ASP 2
form I-ASP 2
is O -1
wonderful O -1
. O -1

The O -1
Mini O -1
's O -1
body B-ASP 2
has O -1
n't O -1
changed O -1
since O -1
late O -1
2010 O -1
- O -1
and O -1
for O -1
a O -1
good O -1
reason O -1
. O -1

The O -1
unibody B-ASP 2
construction I-ASP 2
really O -1
does O -1
feel O -1
lot O -1
more O -1
solid O -1
than O -1
Apple O -1
's O -1
previous O -1
laptops O -1
. O -1

3D B-ASP 0
rendering I-ASP 0
slows O -1
it O -1
down O -1
considerably O -1
. O -1

Got O -1
this O -1
Mac O -1
Mini O -1
with O -1
OS B-ASP 1
X I-ASP 1
Mountain I-ASP 1
Lion I-ASP 1
for O -1
my O -1
wife O -1
. O -1

fast O -1
, O -1
great O -1
screen B-ASP 2
, O -1
beautiful O -1
apps B-ASP -1
for O -1
a O -1
laptop O -1
; O -1
priced B-ASP -1
at O -1
1100 O -1
on O -1
the O -1
apple O -1
website O -1
; O -1
amazon O -1
had O -1
it O -1
for O -1
1098 O -1
+ O -1
tax O -1
- O -1
plus O -1
i O -1
had O -1
a O -1
10 O -1
% O -1
off O -1
coupon O -1
from O -1
amazon O -1
- O -1
cost B-ASP -1
me O -1
998 O -1
plus O -1
tax O -1
- O -1
1070 O -1
- O -1
OTD O -1
! O -1

fast O -1
, O -1
great O -1
screen B-ASP -1
, O -1
beautiful O -1
apps B-ASP 2
for O -1
a O -1
laptop O -1
; O -1
priced B-ASP -1
at O -1
1100 O -1
on O -1
the O -1
apple O -1
website O -1
; O -1
amazon O -1
had O -1
it O -1
for O -1
1098 O -1
+ O -1
tax O -1
- O -1
plus O -1
i O -1
had O -1
a O -1
10 O -1
% O -1
off O -1
coupon O -1
from O -1
amazon O -1
- O -1
cost B-ASP -1
me O -1
998 O -1
plus O -1
tax O -1
- O -1
1070 O -1
- O -1
OTD O -1
! O -1

fast O -1
, O -1
great O -1
screen B-ASP -1
, O -1
beautiful O -1
apps B-ASP -1
for O -1
a O -1
laptop O -1
; O -1
priced B-ASP 1
at O -1
1100 O -1
on O -1
the O -1
apple O -1
website O -1
; O -1
amazon O -1
had O -1
it O -1
for O -1
1098 O -1
+ O -1
tax O -1
- O -1
plus O -1
i O -1
had O -1
a O -1
10 O -1
% O -1
off O -1
coupon O -1
from O -1
amazon O -1
- O -1
cost B-ASP -1
me O -1
998 O -1
plus O -1
tax O -1
- O -1
1070 O -1
- O -1
OTD O -1
! O -1

fast O -1
, O -1
great O -1
screen B-ASP -1
, O -1
beautiful O -1
apps B-ASP -1
for O -1
a O -1
laptop O -1
; O -1
priced B-ASP -1
at O -1
1100 O -1
on O -1
the O -1
apple O -1
website O -1
; O -1
amazon O -1
had O -1
it O -1
for O -1
1098 O -1
+ O -1
tax O -1
- O -1
plus O -1
i O -1
had O -1
a O -1
10 O -1
% O -1
off O -1
coupon O -1
from O -1
amazon O -1
- O -1
cost B-ASP 1
me O -1
998 O -1
plus O -1
tax O -1
- O -1
1070 O -1
- O -1
OTD O -1
! O -1

12.44 O -1
seconds O -1
to O -1
boot B-ASP 1
. O -1

All O -1
the O -1
ports B-ASP 1
are O -1
much O -1
needed O -1
since O -1
this O -1
is O -1
my O -1
main O -1
computer O -1
. O -1

The O -1
Like O -1
New O -1
condition O -1
of O -1
the O -1
iMac O -1
MC309LL/A O -1
on O -1
Amazon O -1
is O -1
at O -1
$ O -1
900 O -1
+ O -1
level O -1
only O -1
, O -1
and O -1
it O -1
is O -1
a O -1
Quad-Core B-ASP 1
2.5 I-ASP 1
GHz I-ASP 1
CPU I-ASP 1
-LRB- O -1
similar O -1
to O -1
the O -1
$ O -1
799 O -1
Mini O -1
-RRB- O -1
, O -1
with O -1
Radeon B-ASP -1
HD I-ASP -1
6750M I-ASP -1
512MB I-ASP -1
graphic I-ASP -1
card I-ASP -1
-LRB- O -1
this O -1
mini O -1
is O -1
integrated B-ASP -1
Intel I-ASP -1
4000 I-ASP -1
card I-ASP -1
-RRB- O -1
, O -1
and O -1
it O -1
even O -1
comes O -1
with O -1
wireless B-ASP -1
Apple I-ASP -1
Keyboard I-ASP -1
and I-ASP -1
Mouse I-ASP -1
, O -1
all O -1
put O -1
together O -1
in O -1
neat O -1
and O -1
nice O -1
package B-ASP -1
. O -1

The O -1
Like O -1
New O -1
condition O -1
of O -1
the O -1
iMac O -1
MC309LL/A O -1
on O -1
Amazon O -1
is O -1
at O -1
$ O -1
900 O -1
+ O -1
level O -1
only O -1
, O -1
and O -1
it O -1
is O -1
a O -1
Quad-Core B-ASP -1
2.5 I-ASP -1
GHz I-ASP -1
CPU I-ASP -1
-LRB- O -1
similar O -1
to O -1
the O -1
$ O -1
799 O -1
Mini O -1
-RRB- O -1
, O -1
with O -1
Radeon B-ASP 1
HD I-ASP 1
6750M I-ASP 1
512MB I-ASP 1
graphic I-ASP 1
card I-ASP 1
-LRB- O -1
this O -1
mini O -1
is O -1
integrated B-ASP -1
Intel I-ASP -1
4000 I-ASP -1
card I-ASP -1
-RRB- O -1
, O -1
and O -1
it O -1
even O -1
comes O -1
with O -1
wireless B-ASP -1
Apple I-ASP -1
Keyboard I-ASP -1
and I-ASP -1
Mouse I-ASP -1
, O -1
all O -1
put O -1
together O -1
in O -1
neat O -1
and O -1
nice O -1
package B-ASP -1
. O -1

The O -1
Like O -1
New O -1
condition O -1
of O -1
the O -1
iMac O -1
MC309LL/A O -1
on O -1
Amazon O -1
is O -1
at O -1
$ O -1
900 O -1
+ O -1
level O -1
only O -1
, O -1
and O -1
it O -1
is O -1
a O -1
Quad-Core B-ASP -1
2.5 I-ASP -1
GHz I-ASP -1
CPU I-ASP -1
-LRB- O -1
similar O -1
to O -1
the O -1
$ O -1
799 O -1
Mini O -1
-RRB- O -1
, O -1
with O -1
Radeon B-ASP -1
HD I-ASP -1
6750M I-ASP -1
512MB I-ASP -1
graphic I-ASP -1
card I-ASP -1
-LRB- O -1
this O -1
mini O -1
is O -1
integrated B-ASP 1
Intel I-ASP 1
4000 I-ASP 1
card I-ASP 1
-RRB- O -1
, O -1
and O -1
it O -1
even O -1
comes O -1
with O -1
wireless B-ASP -1
Apple I-ASP -1
Keyboard I-ASP -1
and I-ASP -1
Mouse I-ASP -1
, O -1
all O -1
put O -1
together O -1
in O -1
neat O -1
and O -1
nice O -1
package B-ASP -1
. O -1

The O -1
Like O -1
New O -1
condition O -1
of O -1
the O -1
iMac O -1
MC309LL/A O -1
on O -1
Amazon O -1
is O -1
at O -1
$ O -1
900 O -1
+ O -1
level O -1
only O -1
, O -1
and O -1
it O -1
is O -1
a O -1
Quad-Core B-ASP -1
2.5 I-ASP -1
GHz I-ASP -1
CPU I-ASP -1
-LRB- O -1
similar O -1
to O -1
the O -1
$ O -1
799 O -1
Mini O -1
-RRB- O -1
, O -1
with O -1
Radeon B-ASP -1
HD I-ASP -1
6750M I-ASP -1
512MB I-ASP -1
graphic I-ASP -1
card I-ASP -1
-LRB- O -1
this O -1
mini O -1
is O -1
integrated B-ASP -1
Intel I-ASP -1
4000 I-ASP -1
card I-ASP -1
-RRB- O -1
, O -1
and O -1
it O -1
even O -1
comes O -1
with O -1
wireless B-ASP 1
Apple I-ASP 1
Keyboard I-ASP 1
and I-ASP 1
Mouse I-ASP 1
, O -1
all O -1
put O -1
together O -1
in O -1
neat O -1
and O -1
nice O -1
package B-ASP -1
. O -1

The O -1
Like O -1
New O -1
condition O -1
of O -1
the O -1
iMac O -1
MC309LL/A O -1
on O -1
Amazon O -1
is O -1
at O -1
$ O -1
900 O -1
+ O -1
level O -1
only O -1
, O -1
and O -1
it O -1
is O -1
a O -1
Quad-Core B-ASP -1
2.5 I-ASP -1
GHz I-ASP -1
CPU I-ASP -1
-LRB- O -1
similar O -1
to O -1
the O -1
$ O -1
799 O -1
Mini O -1
-RRB- O -1
, O -1
with O -1
Radeon B-ASP -1
HD I-ASP -1
6750M I-ASP -1
512MB I-ASP -1
graphic I-ASP -1
card I-ASP -1
-LRB- O -1
this O -1
mini O -1
is O -1
integrated B-ASP -1
Intel I-ASP -1
4000 I-ASP -1
card I-ASP -1
-RRB- O -1
, O -1
and O -1
it O -1
even O -1
comes O -1
with O -1
wireless B-ASP -1
Apple I-ASP -1
Keyboard I-ASP -1
and I-ASP -1
Mouse I-ASP -1
, O -1
all O -1
put O -1
together O -1
in O -1
neat O -1
and O -1
nice O -1
package B-ASP 2
. O -1

Put O -1
a O -1
cover B-ASP 1
on O -1
it O -1
and O -1
is O -1
a O -1
little O -1
better O -1
but O -1
that O -1
is O -1
my O -1
only O -1
complaint O -1
. O -1

Within O -1
a O -1
few O -1
hours O -1
I O -1
was O -1
using O -1
the O -1
gestures B-ASP 2
unconsciously O -1
. O -1

This O -1
mac O -1
does O -1
come O -1
with O -1
an O -1
extender B-ASP 1
cable I-ASP 1
and O -1
I O -1
'm O -1
using O -1
mine O -1
right O -1
now O -1
hoping O -1
the O -1
cable B-ASP -1
will O -1
stay O -1
nice O -1
for O -1
the O -1
many O -1
years O -1
I O -1
plan O -1
on O -1
using O -1
this O -1
mac O -1
. O -1

This O -1
mac O -1
does O -1
come O -1
with O -1
an O -1
extender B-ASP -1
cable I-ASP -1
and O -1
I O -1
'm O -1
using O -1
mine O -1
right O -1
now O -1
hoping O -1
the O -1
cable B-ASP 2
will O -1
stay O -1
nice O -1
for O -1
the O -1
many O -1
years O -1
I O -1
plan O -1
on O -1
using O -1
this O -1
mac O -1
. O -1

The O -1
2.9ghz B-ASP 2
dual-core I-ASP 2
i7 I-ASP 2
chip I-ASP 2
really O -1
out O -1
does O -1
itself O -1
. O -1

It O -1
is O -1
pretty O -1
snappy O -1
and O -1
starts B-ASP 2
up I-ASP 2
in O -1
about O -1
30 O -1
seconds O -1
which O -1
is O -1
good O -1
enough O -1
for O -1
me O -1
. O -1

Not O -1
sure O -1
on O -1
Windows B-ASP 1
8 I-ASP 1
. O -1

My O -1
one O -1
complaint O -1
is O -1
that O -1
there O -1
was O -1
no O -1
internal B-ASP 0
CD I-ASP 0
drive I-ASP 0
. O -1

This O -1
newer O -1
netbook O -1
has O -1
no O -1
hard B-ASP 1
drive I-ASP 1
or O -1
network B-ASP -1
lights I-ASP -1
. O -1

This O -1
newer O -1
netbook O -1
has O -1
no O -1
hard B-ASP -1
drive I-ASP -1
or O -1
network B-ASP 1
lights I-ASP 1
. O -1

I O -1
was O -1
having O -1
a O -1
though O -1
time O -1
deciding O -1
between O -1
the O -1
13 O -1
'' O -1
MacBook O -1
Air O -1
or O -1
the O -1
MacBook O -1
Pro O -1
13 O -1
'' O -1
-LRB- O -1
Both O -1
baseline O -1
models O -1
, O -1
priced B-ASP 1
at O -1
1,200 O -1
$ O -1
retail O -1
-RRB- O -1

Not O -1
too O -1
expense O -1
and O -1
has O -1
enough O -1
storage B-ASP 2
for O -1
most O -1
users O -1
and O -1
many O -1
ports B-ASP -1
. O -1

Not O -1
too O -1
expense O -1
and O -1
has O -1
enough O -1
storage B-ASP -1
for O -1
most O -1
users O -1
and O -1
many O -1
ports B-ASP 2
. O -1

The O -1
audio B-ASP 0
volume I-ASP 0
is O -1
quite O -1
low O -1
and O -1
virtually O -1
unusable O -1
in O -1
a O -1
room O -1
with O -1
any O -1
background O -1
activity O -1
. O -1

It O -1
is O -1
lightweight O -1
and O -1
the O -1
perfect O -1
size B-ASP 2
to O -1
carry O -1
to O -1
class O -1
. O -1

I O -1
was O -1
given O -1
a O -1
demonstration O -1
of O -1
Windows B-ASP 1
8 I-ASP 1
. O -1

The O -1
MBP O -1
is O -1
beautiful O -1
has O -1
many O -1
wonderful O -1
capabilities B-ASP 2
. O -1

I O -1
thought O -1
that O -1
it O -1
will O -1
be O -1
fine O -1
, O -1
if O -1
i O -1
do O -1
some O -1
settings B-ASP 1
. O -1

Runs B-ASP 2
very O -1
smoothly O -1
. O -1

Boot-up B-ASP 0
slowed O -1
significantly O -1
after O -1
all O -1
Windows B-ASP -1
updates I-ASP -1
were O -1
installed O -1
. O -1

Boot-up B-ASP -1
slowed O -1
significantly O -1
after O -1
all O -1
Windows B-ASP 0
updates I-ASP 0
were O -1
installed O -1
. O -1

More O -1
likely O -1
it O -1
will O -1
require O -1
replacing O -1
the O -1
logic B-ASP 0
board I-ASP 0
once O -1
they O -1
admit O -1
they O -1
have O -1
a O -1
problem O -1
and O -1
come O -1
up O -1
with O -1
a O -1
solution O -1
. O -1

It O -1
was O -1
important O -1
that O -1
it O -1
was O -1
powerful O -1
enough O -1
to O -1
do O -1
all O -1
of O -1
the O -1
tasks O -1
he O -1
needed O -1
on O -1
the O -1
internet B-ASP 2
, O -1
word B-ASP -1
processing I-ASP -1
, O -1
graphic B-ASP -1
design I-ASP -1
and O -1
gaming B-ASP -1
. O -1

It O -1
was O -1
important O -1
that O -1
it O -1
was O -1
powerful O -1
enough O -1
to O -1
do O -1
all O -1
of O -1
the O -1
tasks O -1
he O -1
needed O -1
on O -1
the O -1
internet B-ASP -1
, O -1
word B-ASP 2
processing I-ASP 2
, O -1
graphic B-ASP -1
design I-ASP -1
and O -1
gaming B-ASP -1
. O -1

It O -1
was O -1
important O -1
that O -1
it O -1
was O -1
powerful O -1
enough O -1
to O -1
do O -1
all O -1
of O -1
the O -1
tasks O -1
he O -1
needed O -1
on O -1
the O -1
internet B-ASP -1
, O -1
word B-ASP -1
processing I-ASP -1
, O -1
graphic B-ASP 2
design I-ASP 2
and O -1
gaming B-ASP -1
. O -1

It O -1
was O -1
important O -1
that O -1
it O -1
was O -1
powerful O -1
enough O -1
to O -1
do O -1
all O -1
of O -1
the O -1
tasks O -1
he O -1
needed O -1
on O -1
the O -1
internet B-ASP -1
, O -1
word B-ASP -1
processing I-ASP -1
, O -1
graphic B-ASP -1
design I-ASP -1
and O -1
gaming B-ASP 2
. O -1

I O -1
like O -1
the O -1
Mini O -1
Mac O -1
it O -1
was O -1
easy O -1
to O -1
setup B-ASP 2
and O -1
install B-ASP -1
, O -1
but O -1
I O -1
am O -1
learning O -1
as O -1
I O -1
go O -1
and O -1
could O -1
use O -1
a O -1
tutorial B-ASP -1
to O -1
learn O -1
how O -1
to O -1
use O -1
some O -1
of O -1
the O -1
features B-ASP -1
I O -1
was O -1
use O -1
to O -1
on O -1
the O -1
PC O -1
especially O -1
the O -1
right B-ASP -1
mouse I-ASP -1
click I-ASP -1
menu I-ASP -1
. O -1

I O -1
like O -1
the O -1
Mini O -1
Mac O -1
it O -1
was O -1
easy O -1
to O -1
setup B-ASP -1
and O -1
install B-ASP 2
, O -1
but O -1
I O -1
am O -1
learning O -1
as O -1
I O -1
go O -1
and O -1
could O -1
use O -1
a O -1
tutorial B-ASP -1
to O -1
learn O -1
how O -1
to O -1
use O -1
some O -1
of O -1
the O -1
features B-ASP -1
I O -1
was O -1
use O -1
to O -1
on O -1
the O -1
PC O -1
especially O -1
the O -1
right B-ASP -1
mouse I-ASP -1
click I-ASP -1
menu I-ASP -1
. O -1

I O -1
like O -1
the O -1
Mini O -1
Mac O -1
it O -1
was O -1
easy O -1
to O -1
setup B-ASP -1
and O -1
install B-ASP -1
, O -1
but O -1
I O -1
am O -1
learning O -1
as O -1
I O -1
go O -1
and O -1
could O -1
use O -1
a O -1
tutorial B-ASP 1
to O -1
learn O -1
how O -1
to O -1
use O -1
some O -1
of O -1
the O -1
features B-ASP -1
I O -1
was O -1
use O -1
to O -1
on O -1
the O -1
PC O -1
especially O -1
the O -1
right B-ASP -1
mouse I-ASP -1
click I-ASP -1
menu I-ASP -1
. O -1

I O -1
like O -1
the O -1
Mini O -1
Mac O -1
it O -1
was O -1
easy O -1
to O -1
setup B-ASP -1
and O -1
install B-ASP -1
, O -1
but O -1
I O -1
am O -1
learning O -1
as O -1
I O -1
go O -1
and O -1
could O -1
use O -1
a O -1
tutorial B-ASP -1
to O -1
learn O -1
how O -1
to O -1
use O -1
some O -1
of O -1
the O -1
features B-ASP 1
I O -1
was O -1
use O -1
to O -1
on O -1
the O -1
PC O -1
especially O -1
the O -1
right B-ASP -1
mouse I-ASP -1
click I-ASP -1
menu I-ASP -1
. O -1

I O -1
like O -1
the O -1
Mini O -1
Mac O -1
it O -1
was O -1
easy O -1
to O -1
setup B-ASP -1
and O -1
install B-ASP -1
, O -1
but O -1
I O -1
am O -1
learning O -1
as O -1
I O -1
go O -1
and O -1
could O -1
use O -1
a O -1
tutorial B-ASP -1
to O -1
learn O -1
how O -1
to O -1
use O -1
some O -1
of O -1
the O -1
features B-ASP -1
I O -1
was O -1
use O -1
to O -1
on O -1
the O -1
PC O -1
especially O -1
the O -1
right B-ASP 1
mouse I-ASP 1
click I-ASP 1
menu I-ASP 1
. O -1

Runs B-ASP 2
real O -1
quick O -1
. O -1

Buy O -1
the O -1
separate O -1
RAM B-ASP 2
memory I-ASP 2
and O -1
you O -1
will O -1
have O -1
a O -1
rocket O -1
! O -1

Since O -1
the O -1
machine O -1
's O -1
slim O -1
profile B-ASP 0
is O -1
critical O -1
to O -1
me O -1
, O -1
that O -1
was O -1
a O -1
problem O -1
. O -1

WiFi B-ASP -1
capability I-ASP -1
, O -1
disk B-ASP 2
drive I-ASP 2
and O -1
multiple O -1
USB B-ASP -1
ports I-ASP -1
to O -1
connect O -1
scale O -1
and O -1
printers O -1
was O -1
all O -1
that O -1
was O -1
required O -1
. O -1

WiFi B-ASP -1
capability I-ASP -1
, O -1
disk B-ASP -1
drive I-ASP -1
and O -1
multiple O -1
USB B-ASP 2
ports I-ASP 2
to O -1
connect O -1
scale O -1
and O -1
printers O -1
was O -1
all O -1
that O -1
was O -1
required O -1
. O -1

WiFi B-ASP 2
capability I-ASP 2
, O -1
disk B-ASP -1
drive I-ASP -1
and O -1
multiple O -1
USB B-ASP -1
ports I-ASP -1
to O -1
connect O -1
scale O -1
and O -1
printers O -1
was O -1
all O -1
that O -1
was O -1
required O -1
. O -1

The O -1
SD B-ASP 0
card I-ASP 0
reader I-ASP 0
is O -1
slightly O -1
recessed O -1
and O -1
upside O -1
down O -1
-LRB- O -1
the O -1
nail B-ASP -1
slot I-ASP -1
on I-ASP -1
the I-ASP -1
card I-ASP -1
can O -1
not O -1
be O -1
accessed O -1
-RRB- O -1
, O -1
if O -1
this O -1
was O -1
a O -1
self O -1
ejecting O -1
slot B-ASP -1
this O -1
would O -1
not O -1
be O -1
an O -1
issue O -1
, O -1
but O -1
its O -1
not O -1
. O -1

The O -1
SD B-ASP -1
card I-ASP -1
reader I-ASP -1
is O -1
slightly O -1
recessed O -1
and O -1
upside O -1
down O -1
-LRB- O -1
the O -1
nail B-ASP 0
slot I-ASP 0
on I-ASP 0
the I-ASP 0
card I-ASP 0
can O -1
not O -1
be O -1
accessed O -1
-RRB- O -1
, O -1
if O -1
this O -1
was O -1
a O -1
self O -1
ejecting O -1
slot B-ASP -1
this O -1
would O -1
not O -1
be O -1
an O -1
issue O -1
, O -1
but O -1
its O -1
not O -1
. O -1

The O -1
SD B-ASP -1
card I-ASP -1
reader I-ASP -1
is O -1
slightly O -1
recessed O -1
and O -1
upside O -1
down O -1
-LRB- O -1
the O -1
nail B-ASP -1
slot I-ASP -1
on I-ASP -1
the I-ASP -1
card I-ASP -1
can O -1
not O -1
be O -1
accessed O -1
-RRB- O -1
, O -1
if O -1
this O -1
was O -1
a O -1
self O -1
ejecting O -1
slot B-ASP 0
this O -1
would O -1
not O -1
be O -1
an O -1
issue O -1
, O -1
but O -1
its O -1
not O -1
. O -1

Soft O -1
touch B-ASP 2
, O -1
anodized B-ASP -1
aluminum I-ASP -1
with O -1
laser O -1
cut O -1
precision O -1
and O -1
no O -1
flaws O -1
. O -1

Soft O -1
touch B-ASP -1
, O -1
anodized B-ASP 2
aluminum I-ASP 2
with O -1
laser O -1
cut O -1
precision O -1
and O -1
no O -1
flaws O -1
. O -1

Simple O -1
details O -1
, O -1
crafted O -1
aluminium B-ASP 2
and O -1
real O -1
glass B-ASP -1
make O -1
this O -1
laptop O -1
blow O -1
away O -1
the O -1
other O -1
plastic O -1
ridden O -1
, O -1
bulky O -1
sticker O -1
filled O -1
laptops O -1
. O -1

Simple O -1
details O -1
, O -1
crafted O -1
aluminium B-ASP -1
and O -1
real O -1
glass B-ASP 2
make O -1
this O -1
laptop O -1
blow O -1
away O -1
the O -1
other O -1
plastic O -1
ridden O -1
, O -1
bulky O -1
sticker O -1
filled O -1
laptops O -1
. O -1

First O -1
of O -1
all O -1
yes O -1
this O -1
is O -1
a O -1
mac O -1
and O -1
it O -1
has O -1
that O -1
nice O -1
brushed O -1
aluminum B-ASP 2
. O -1

After O -1
all O -1
was O -1
said O -1
and O -1
done O -1
, O -1
I O -1
essentially O -1
used O -1
that O -1
$ O -1
450 O -1
savings O -1
to O -1
buy O -1
16GB B-ASP 1
of I-ASP 1
RAM I-ASP 1
, O -1
TWO O -1
Seagate B-ASP -1
Momentus I-ASP -1
XT I-ASP -1
hybrid I-ASP -1
drives I-ASP -1
and O -1
an O -1
OWC B-ASP -1
upgrade I-ASP -1
kit I-ASP -1
to O -1
install O -1
the O -1
second O -1
hard B-ASP -1
drive I-ASP -1
. O -1

After O -1
all O -1
was O -1
said O -1
and O -1
done O -1
, O -1
I O -1
essentially O -1
used O -1
that O -1
$ O -1
450 O -1
savings O -1
to O -1
buy O -1
16GB B-ASP -1
of I-ASP -1
RAM I-ASP -1
, O -1
TWO O -1
Seagate B-ASP 1
Momentus I-ASP 1
XT I-ASP 1
hybrid I-ASP 1
drives I-ASP 1
and O -1
an O -1
OWC B-ASP -1
upgrade I-ASP -1
kit I-ASP -1
to O -1
install O -1
the O -1
second O -1
hard B-ASP -1
drive I-ASP -1
. O -1

After O -1
all O -1
was O -1
said O -1
and O -1
done O -1
, O -1
I O -1
essentially O -1
used O -1
that O -1
$ O -1
450 O -1
savings O -1
to O -1
buy O -1
16GB B-ASP -1
of I-ASP -1
RAM I-ASP -1
, O -1
TWO O -1
Seagate B-ASP -1
Momentus I-ASP -1
XT I-ASP -1
hybrid I-ASP -1
drives I-ASP -1
and O -1
an O -1
OWC B-ASP 1
upgrade I-ASP 1
kit I-ASP 1
to O -1
install O -1
the O -1
second O -1
hard B-ASP -1
drive I-ASP -1
. O -1

After O -1
all O -1
was O -1
said O -1
and O -1
done O -1
, O -1
I O -1
essentially O -1
used O -1
that O -1
$ O -1
450 O -1
savings O -1
to O -1
buy O -1
16GB B-ASP -1
of I-ASP -1
RAM I-ASP -1
, O -1
TWO O -1
Seagate B-ASP -1
Momentus I-ASP -1
XT I-ASP -1
hybrid I-ASP -1
drives I-ASP -1
and O -1
an O -1
OWC B-ASP -1
upgrade I-ASP -1
kit I-ASP -1
to O -1
install O -1
the O -1
second O -1
hard B-ASP 1
drive I-ASP 1
. O -1

The O -1
Dell O -1
Inspiron O -1
is O -1
fast O -1
and O -1
has O -1
a O -1
number B-ASP 2
pad I-ASP 2
on I-ASP 2
the I-ASP 2
keyboard I-ASP 2
, O -1
which O -1
I O -1
miss O -1
on O -1
my O -1
Apple O -1
laptops O -1
. O -1

I O -1
was O -1
concerned O -1
that O -1
the O -1
downgrade O -1
to O -1
the O -1
regular B-ASP 2
hard I-ASP 2
drive I-ASP 2
would O -1
make O -1
it O -1
unacceptably O -1
slow O -1
but O -1
I O -1
need O -1
not O -1
have O -1
worried O -1
- O -1
this O -1
machine O -1
is O -1
the O -1
fastest O -1
I O -1
have O -1
ever O -1
owned O -1
... O -1

This O -1
one O -1
still O -1
has O -1
the O -1
CD B-ASP 1
slot I-ASP 1
. O -1

No O -1
HDMI B-ASP 1
port I-ASP 1
. O -1

I O -1
had O -1
to O -1
install B-ASP 0
Mountain I-ASP 0
Lion I-ASP 0
and O -1
it O -1
took O -1
a O -1
good O -1
two O -1
hours O -1
. O -1

Customization B-ASP 0
on O -1
mac O -1
is O -1
impossible O -1
. O -1

I O -1
am O -1
replacing O -1
the O -1
HD B-ASP 1
with O -1
a O -1
Micron B-ASP -1
SSD I-ASP -1
soon O -1
. O -1

I O -1
am O -1
replacing O -1
the O -1
HD B-ASP -1
with O -1
a O -1
Micron B-ASP 1
SSD I-ASP 1
soon O -1
. O -1

Plus O -1
two B-ASP 2
finger I-ASP 2
clicking I-ASP 2
as O -1
a O -1
replacement O -1
for O -1
the O -1
right B-ASP -1
click I-ASP -1
button I-ASP -1
is O -1
surprisingly O -1
intuitive O -1
. O -1

Plus O -1
two B-ASP -1
finger I-ASP -1
clicking I-ASP -1
as O -1
a O -1
replacement O -1
for O -1
the O -1
right B-ASP 1
click I-ASP 1
button I-ASP 1
is O -1
surprisingly O -1
intuitive O -1
. O -1

The O -1
SuperDrive B-ASP 2
is O -1
quiet O -1
. O -1

The O -1
power B-ASP 0
plug I-ASP 0
has O -1
to O -1
be O -1
connected O -1
to O -1
the O -1
power B-ASP -1
adaptor I-ASP -1
to O -1
charge O -1
the O -1
battery B-ASP -1
but O -1
wo O -1
n't O -1
stay O -1
connected O -1
. O -1

The O -1
power B-ASP -1
plug I-ASP -1
has O -1
to O -1
be O -1
connected O -1
to O -1
the O -1
power B-ASP 1
adaptor I-ASP 1
to O -1
charge O -1
the O -1
battery B-ASP -1
but O -1
wo O -1
n't O -1
stay O -1
connected O -1
. O -1

The O -1
power B-ASP -1
plug I-ASP -1
has O -1
to O -1
be O -1
connected O -1
to O -1
the O -1
power B-ASP -1
adaptor I-ASP -1
to O -1
charge O -1
the O -1
battery B-ASP 1
but O -1
wo O -1
n't O -1
stay O -1
connected O -1
. O -1

The O -1
battery B-ASP 0
was O -1
completely O -1
dead O -1
, O -1
in O -1
fact O -1
it O -1
had O -1
grown O -1
about O -1
a O -1
quarter O -1
inch O -1
thick O -1
lump O -1
on O -1
the O -1
underside O -1
. O -1

if O -1
yo O -1
like O -1
practicality B-ASP 2
this O -1
is O -1
the O -1
laptop O -1
for O -1
you O -1
. O -1

The O -1
OS B-ASP 2
is O -1
great O -1
. O -1

I O -1
tried O -1
several O -1
monitors B-ASP 1
and O -1
several O -1
HDMI B-ASP -1
cables I-ASP -1
and O -1
this O -1
was O -1
the O -1
case O -1
each O -1
time O -1
. O -1

I O -1
tried O -1
several O -1
monitors B-ASP -1
and O -1
several O -1
HDMI B-ASP 1
cables I-ASP 1
and O -1
this O -1
was O -1
the O -1
case O -1
each O -1
time O -1
. O -1

CONS O -1
: O -1
Price B-ASP 0
is O -1
a O -1
bit O -1
ridiculous O -1
, O -1
kinda O -1
heavy O -1
. O -1

The O -1
troubleshooting O -1
said O -1
it O -1
was O -1
the O -1
AC B-ASP 1
adaptor I-ASP 1
so O -1
we O -1
ordered O -1
a O -1
new O -1
one O -1
. O -1

Fan B-ASP 1
only O -1
comes O -1
on O -1
when O -1
you O -1
are O -1
playing B-ASP -1
a I-ASP -1
game I-ASP -1
. O -1

Fan B-ASP -1
only O -1
comes O -1
on O -1
when O -1
you O -1
are O -1
playing B-ASP 1
a I-ASP 1
game I-ASP 1
. O -1

Which O -1
it O -1
did O -1
not O -1
have O -1
, O -1
only O -1
3 O -1
USB B-ASP 1
2 I-ASP 1
ports I-ASP 1
. O -1

No O -1
startup B-ASP 1
disk I-ASP 1
was O -1
not O -1
included O -1
but O -1
that O -1
may O -1
be O -1
my O -1
fault O -1
. O -1

There O -1
is O -1
no O -1
"tools" B-ASP 1
menu I-ASP 1
. O -1

It O -1
is O -1
very O -1
fast O -1
and O -1
has O -1
everything O -1
that O -1
I O -1
need O -1
except O -1
for O -1
a O -1
word B-ASP 0
program I-ASP 0
. O -1

Needs O -1
a O -1
CD/DVD B-ASP 1
drive I-ASP 1
and O -1
a O -1
bigger O -1
power B-ASP -1
switch I-ASP -1
. O -1

Needs O -1
a O -1
CD/DVD B-ASP -1
drive I-ASP -1
and O -1
a O -1
bigger O -1
power B-ASP 0
switch I-ASP 0
. O -1

My O -1
laptop O -1
with O -1
Windows B-ASP 0
7 I-ASP 0
crashed O -1
and O -1
I O -1
did O -1
not O -1
want O -1
Windows B-ASP -1
8 I-ASP -1
. O -1

My O -1
laptop O -1
with O -1
Windows B-ASP -1
7 I-ASP -1
crashed O -1
and O -1
I O -1
did O -1
not O -1
want O -1
Windows B-ASP 0
8 I-ASP 0
. O -1

Easy O -1
to O -1
install B-ASP 2
also O -1
small O -1
to O -1
leave O -1
anywhere O -1
at O -1
your O -1
bedroom O -1
also O -1
very O -1
easy O -1
to O -1
configure B-ASP -1
for I-ASP -1
ADSl I-ASP -1
cable I-ASP -1
or I-ASP -1
wifi I-ASP -1
. O -1

Easy O -1
to O -1
install B-ASP -1
also O -1
small O -1
to O -1
leave O -1
anywhere O -1
at O -1
your O -1
bedroom O -1
also O -1
very O -1
easy O -1
to O -1
configure B-ASP 2
for I-ASP 2
ADSl I-ASP 2
cable I-ASP 2
or I-ASP 2
wifi I-ASP 2
. O -1

Nice O -1
packing B-ASP 2
. O -1

I O -1
switched O -1
to O -1
this O -1
because O -1
I O -1
wanted O -1
something O -1
different O -1
, O -1
even O -1
though O -1
I O -1
miss O -1
windows B-ASP 2
. O -1

Apple O -1
no O -1
longer O -1
includes O -1
iDVD B-ASP 0
with O -1
the O -1
computer O -1
and O -1
furthermore O -1
, O -1
Apple O -1
does O -1
n't O -1
even O -1
offer O -1
it O -1
anymore O -1
! O -1

I O -1
also O -1
wanted O -1
Windows B-ASP 2
7 I-ASP 2
, O -1
which O -1
this O -1
one O -1
has O -1
. O -1

At O -1
first O -1
, O -1
I O -1
feel O -1
a O -1
little O -1
bit O -1
uncomfortable O -1
in O -1
using O -1
the O -1
Mac B-ASP 0
system I-ASP 0
. O -1

I O -1
am O -1
used O -1
to O -1
computers O -1
with O -1
windows B-ASP 1
so O -1
I O -1
am O -1
having O -1
a O -1
little O -1
difficulty O -1
finding O -1
my O -1
way O -1
around O -1
. O -1

It O -1
just O -1
works B-ASP 2
out O -1
of O -1
the O -1
box O -1
and O -1
you O -1
have O -1
a O -1
lot O -1
of O -1
cool O -1
software B-ASP -1
included O -1
with O -1
the O -1
OS B-ASP -1
. O -1

It O -1
just O -1
works B-ASP -1
out O -1
of O -1
the O -1
box O -1
and O -1
you O -1
have O -1
a O -1
lot O -1
of O -1
cool O -1
software B-ASP 2
included O -1
with O -1
the O -1
OS B-ASP -1
. O -1

It O -1
just O -1
works B-ASP -1
out O -1
of O -1
the O -1
box O -1
and O -1
you O -1
have O -1
a O -1
lot O -1
of O -1
cool O -1
software B-ASP -1
included O -1
with O -1
the O -1
OS B-ASP 1
. O -1

its O -1
as O -1
advertised O -1
on O -1
here O -1
... O -1
it O -1
works B-ASP 2
great O -1
and O -1
is O -1
not O -1
a O -1
waste O -1
of O -1
money O -1
! O -1

Runs B-ASP 2
like O -1
a O -1
champ O -1
... O -1

Premium O -1
price B-ASP 2
for O -1
the O -1
OS B-ASP -1
more O -1
than O -1
anything O -1
else O -1
. O -1

Premium O -1
price B-ASP -1
for O -1
the O -1
OS B-ASP 1
more O -1
than O -1
anything O -1
else O -1
. O -1

I O -1
was O -1
a O -1
little O -1
concerned O -1
about O -1
the O -1
touch B-ASP 2
pad I-ASP 2
based O -1
on O -1
reviews O -1
, O -1
but O -1
I O -1
've O -1
found O -1
it O -1
fine O -1
to O -1
work O -1
with O -1
. O -1

The O -1
sound O -1
as O -1
mentioned O -1
earlier O -1
is O -1
n't O -1
the O -1
best O -1
, O -1
but O -1
it O -1
can O -1
be O -1
solved O -1
with O -1
headphones B-ASP 1
. O -1

However O -1
, O -1
the O -1
experience O -1
was O -1
great O -1
since O -1
the O -1
OS B-ASP 2
does O -1
not O -1
become O -1
unstable O -1
and O -1
the O -1
application B-ASP -1
will O -1
simply O -1
shutdown O -1
and O -1
reopen O -1
. O -1

However O -1
, O -1
the O -1
experience O -1
was O -1
great O -1
since O -1
the O -1
OS B-ASP -1
does O -1
not O -1
become O -1
unstable O -1
and O -1
the O -1
application B-ASP 2
will O -1
simply O -1
shutdown O -1
and O -1
reopen O -1
. O -1

If O -1
you O -1
ask O -1
me O -1
, O -1
for O -1
this O -1
price B-ASP 0
it O -1
should O -1
be O -1
included O -1
. O -1

The O -1
battery B-ASP 0
is O -1
not O -1
as O -1
shown O -1
in O -1
the O -1
product O -1
photos O -1
. O -1

Shipping B-ASP 2
was O -1
quick O -1
and O -1
product O -1
described O -1
was O -1
the O -1
product O -1
sent O -1
and O -1
so O -1
much O -1
more O -1
... O -1

the O -1
retina B-ASP 2
display I-ASP 2
display I-ASP 2
make O -1
pictures O -1
i O -1
took O -1
years O -1
ago O -1
jaw O -1
dropping O -1
. O -1

The O -1
Mac O -1
Mini O -1
is O -1
probably O -1
the O -1
simplest O -1
example O -1
of O -1
compact B-ASP 2
computing I-ASP 2
out O -1
there O -1
. O -1

Instead O -1
, O -1
I O -1
'll O -1
focus O -1
more O -1
on O -1
the O -1
actual O -1
performance B-ASP 1
and I-ASP 1
feature I-ASP 1
set I-ASP 1
of I-ASP 1
the I-ASP 1
hardware I-ASP 1
itself O -1
so O -1
you O -1
can O -1
make O -1
an O -1
educated O -1
decision O -1
on O -1
which O -1
Mac O -1
to O -1
buy O -1
. O -1

Other O -1
ports B-ASP 1
include O -1
FireWire B-ASP -1
800 I-ASP -1
, O -1
Gigabit B-ASP -1
Ethernet I-ASP -1
, O -1
MagSafe B-ASP -1
port I-ASP -1
, O -1
Microphone B-ASP -1
jack I-ASP -1
. O -1

Other O -1
ports B-ASP -1
include O -1
FireWire B-ASP 1
800 I-ASP 1
, O -1
Gigabit B-ASP -1
Ethernet I-ASP -1
, O -1
MagSafe B-ASP -1
port I-ASP -1
, O -1
Microphone B-ASP -1
jack I-ASP -1
. O -1

Other O -1
ports B-ASP -1
include O -1
FireWire B-ASP -1
800 I-ASP -1
, O -1
Gigabit B-ASP 1
Ethernet I-ASP 1
, O -1
MagSafe B-ASP -1
port I-ASP -1
, O -1
Microphone B-ASP -1
jack I-ASP -1
. O -1

Other O -1
ports B-ASP -1
include O -1
FireWire B-ASP -1
800 I-ASP -1
, O -1
Gigabit B-ASP -1
Ethernet I-ASP -1
, O -1
MagSafe B-ASP 1
port I-ASP 1
, O -1
Microphone B-ASP -1
jack I-ASP -1
. O -1

Other O -1
ports B-ASP -1
include O -1
FireWire B-ASP -1
800 I-ASP -1
, O -1
Gigabit B-ASP -1
Ethernet I-ASP -1
, O -1
MagSafe B-ASP -1
port I-ASP -1
, O -1
Microphone B-ASP 1
jack I-ASP 1
. O -1

Additionally O -1
, O -1
there O -1
is O -1
barely O -1
a O -1
ventilation B-ASP 0
system I-ASP 0
in O -1
the O -1
computer O -1
, O -1
and O -1
even O -1
the O -1
simple O -1
activity O -1
of O -1
watching B-ASP -1
videos I-ASP -1
let O -1
alone O -1
playing B-ASP -1
steam I-ASP -1
games I-ASP -1
causes O -1
the O -1
laptop O -1
to O -1
get O -1
very O -1
very O -1
hot O -1
, O -1
and O -1
in O -1
fact O -1
impossible O -1
to O -1
keep O -1
on O -1
lap O -1
. O -1

Additionally O -1
, O -1
there O -1
is O -1
barely O -1
a O -1
ventilation B-ASP -1
system I-ASP -1
in O -1
the O -1
computer O -1
, O -1
and O -1
even O -1
the O -1
simple O -1
activity O -1
of O -1
watching B-ASP 1
videos I-ASP 1
let O -1
alone O -1
playing B-ASP -1
steam I-ASP -1
games I-ASP -1
causes O -1
the O -1
laptop O -1
to O -1
get O -1
very O -1
very O -1
hot O -1
, O -1
and O -1
in O -1
fact O -1
impossible O -1
to O -1
keep O -1
on O -1
lap O -1
. O -1

Additionally O -1
, O -1
there O -1
is O -1
barely O -1
a O -1
ventilation B-ASP -1
system I-ASP -1
in O -1
the O -1
computer O -1
, O -1
and O -1
even O -1
the O -1
simple O -1
activity O -1
of O -1
watching B-ASP -1
videos I-ASP -1
let O -1
alone O -1
playing B-ASP 1
steam I-ASP 1
games I-ASP 1
causes O -1
the O -1
laptop O -1
to O -1
get O -1
very O -1
very O -1
hot O -1
, O -1
and O -1
in O -1
fact O -1
impossible O -1
to O -1
keep O -1
on O -1
lap O -1
. O -1

Chatting O -1
with O -1
Acer B-ASP 1
support I-ASP 1
, O -1
I O -1
was O -1
advised O -1
the O -1
problem O -1
was O -1
corrupted O -1
operating B-ASP -1
system I-ASP -1
files I-ASP -1
. O -1

Chatting O -1
with O -1
Acer B-ASP -1
support I-ASP -1
, O -1
I O -1
was O -1
advised O -1
the O -1
problem O -1
was O -1
corrupted O -1
operating B-ASP 1
system I-ASP 1
files I-ASP 1
. O -1

It O -1
's O -1
been O -1
a O -1
couple O -1
weeks O -1
since O -1
the O -1
purchase O -1
and O -1
I O -1
'm O -1
struggle O -1
with O -1
finding O -1
the O -1
correct O -1
keys B-ASP 1
-LRB- O -1
but O -1
that O -1
was O -1
expected O -1
-RRB- O -1
. O -1

Many O -1
people O -1
complain O -1
about O -1
the O -1
new O -1
OS B-ASP 0
, O -1
and O -1
it O -1
's O -1
urgent O -1
for O -1
Apple O -1
to O -1
fix O -1
it O -1
asap O -1
! O -1

Now O -1
that O -1
I O -1
have O -1
upgraded O -1
to O -1
Lion B-ASP 2
I O -1
am O -1
much O -1
happier O -1
about O -1
MAC B-ASP -1
OS I-ASP -1
and O -1
have O -1
just O -1
bought O -1
an O -1
iMac O -1
for O -1
office O -1
. O -1

Now O -1
that O -1
I O -1
have O -1
upgraded O -1
to O -1
Lion B-ASP -1
I O -1
am O -1
much O -1
happier O -1
about O -1
MAC B-ASP 2
OS I-ASP 2
and O -1
have O -1
just O -1
bought O -1
an O -1
iMac O -1
for O -1
office O -1
. O -1

User O -1
upgradeable O -1
RAM B-ASP 2
and O -1
HDD B-ASP -1
. O -1

User O -1
upgradeable O -1
RAM B-ASP -1
and O -1
HDD B-ASP 2
. O -1

But O -1
I O -1
wanted O -1
the O -1
Pro O -1
for O -1
the O -1
CD/DVD B-ASP 2
player I-ASP 2
. O -1

I O -1
was O -1
a O -1
little O -1
worry O -1
at O -1
first O -1
because O -1
I O -1
do O -1
n't O -1
have O -1
a O -1
lot O -1
of O -1
experience O -1
with O -1
os.x B-ASP 1
and O -1
windows B-ASP -1
has O -1
always O -1
been O -1
second O -1
nature O -1
to O -1
me O -1
after O -1
many O -1
years O -1
of O -1
using O -1
windows B-ASP -1
. O -1

I O -1
was O -1
a O -1
little O -1
worry O -1
at O -1
first O -1
because O -1
I O -1
do O -1
n't O -1
have O -1
a O -1
lot O -1
of O -1
experience O -1
with O -1
os.x B-ASP -1
and O -1
windows B-ASP 2
has O -1
always O -1
been O -1
second O -1
nature O -1
to O -1
me O -1
after O -1
many O -1
years O -1
of O -1
using O -1
windows B-ASP -1
. O -1

I O -1
was O -1
a O -1
little O -1
worry O -1
at O -1
first O -1
because O -1
I O -1
do O -1
n't O -1
have O -1
a O -1
lot O -1
of O -1
experience O -1
with O -1
os.x B-ASP -1
and O -1
windows B-ASP -1
has O -1
always O -1
been O -1
second O -1
nature O -1
to O -1
me O -1
after O -1
many O -1
years O -1
of O -1
using O -1
windows B-ASP 1
. O -1

With O -1
the O -1
softwares B-ASP -1
supporting O -1
the O -1
use O -1
of O -1
other O -1
OS B-ASP 1
makes O -1
it O -1
much O -1
better O -1
. O -1

With O -1
the O -1
softwares B-ASP 1
supporting O -1
the O -1
use O -1
of O -1
other O -1
OS B-ASP -1
makes O -1
it O -1
much O -1
better O -1
. O -1

I O -1
then O -1
upgraded O -1
to O -1
Mac B-ASP 1
OS I-ASP 1
X I-ASP 1
10.8 I-ASP 1
"Mountain I-ASP 1
Lion" I-ASP 1
. O -1

I O -1
was O -1
considering O -1
buying O -1
the O -1
Air O -1
, O -1
but O -1
in O -1
reality O -1
, O -1
this O -1
one O -1
has O -1
a O -1
more O -1
powerful O -1
performance B-ASP 2
and O -1
seems O -1
much O -1
more O -1
convenient O -1
, O -1
even O -1
though O -1
it O -1
's O -1
about O -1
.20 O -1
inch O -1
thicker O -1
and O -1
2 O -1
lbs O -1
heavier O -1
. O -1

At O -1
home O -1
and O -1
the O -1
office O -1
it O -1
gets O -1
plugged O -1
into O -1
an O -1
external B-ASP -1
24' I-ASP -1
LCD I-ASP -1
screen I-ASP -1
, O -1
so O -1
built B-ASP 1
in I-ASP 1
screen I-ASP 1
size I-ASP 1
is O -1
not O -1
terribly O -1
important O -1
. O -1

At O -1
home O -1
and O -1
the O -1
office O -1
it O -1
gets O -1
plugged O -1
into O -1
an O -1
external B-ASP 1
24' I-ASP 1
LCD I-ASP 1
screen I-ASP 1
, O -1
so O -1
built B-ASP -1
in I-ASP -1
screen I-ASP -1
size I-ASP -1
is O -1
not O -1
terribly O -1
important O -1
. O -1

Just O -1
beware O -1
no O -1
DVD B-ASP -1
slot I-ASP -1
so O -1
when O -1
I O -1
went O -1
to O -1
install B-ASP 0
software I-ASP 0
I O -1
had O -1
on O -1
CD O -1
I O -1
could O -1
n't O -1
. O -1

Just O -1
beware O -1
no O -1
DVD B-ASP 1
slot I-ASP 1
so O -1
when O -1
I O -1
went O -1
to O -1
install B-ASP -1
software I-ASP -1
I O -1
had O -1
on O -1
CD O -1
I O -1
could O -1
n't O -1
. O -1

I O -1
bought O -1
it O -1
to O -1
be O -1
able O -1
to O -1
dedicate O -1
a O -1
small O -1
, O -1
portable O -1
laptop O -1
to O -1
my O -1
writing O -1
and O -1
was O -1
surprised O -1
to O -1
learn O -1
that O -1
I O -1
needed O -1
to O -1
buy O -1
a O -1
word B-ASP 1
processing I-ASP 1
program I-ASP 1
to O -1
do O -1
so O -1
. O -1

This O -1
version O -1
of O -1
MacBook O -1
Pro O -1
runs O -1
on O -1
a O -1
third-generation B-ASP 1
CPU I-ASP 1
-LRB- I-ASP 1
`` I-ASP 1
Ivy I-ASP 1
Bridge I-ASP 1
'' I-ASP 1
-RRB- I-ASP 1
, O -1
not O -1
the O -1
latest O -1
fourth-generation B-ASP -1
Haswell I-ASP -1
CPU I-ASP -1
the O -1
2013 O -1
version O -1
has O -1
. O -1

This O -1
version O -1
of O -1
MacBook O -1
Pro O -1
runs O -1
on O -1
a O -1
third-generation B-ASP -1
CPU I-ASP -1
-LRB- I-ASP -1
`` I-ASP -1
Ivy I-ASP -1
Bridge I-ASP -1
'' I-ASP -1
-RRB- I-ASP -1
, O -1
not O -1
the O -1
latest O -1
fourth-generation B-ASP 1
Haswell I-ASP 1
CPU I-ASP 1
the O -1
2013 O -1
version O -1
has O -1
. O -1

No O -1
Cd B-ASP 1
Rom I-ASP 1
in O -1
the O -1
new O -1
version O -1
there O -1
's O -1
no O -1
way O -1
I O -1
'm O -1
spending O -1
that O -1
kind O -1
of O -1
money O -1
on O -1
something O -1
has O -1
less O -1
features B-ASP -1
than O -1
the O -1
older O -1
version O -1
. O -1

No O -1
Cd B-ASP -1
Rom I-ASP -1
in O -1
the O -1
new O -1
version O -1
there O -1
's O -1
no O -1
way O -1
I O -1
'm O -1
spending O -1
that O -1
kind O -1
of O -1
money O -1
on O -1
something O -1
has O -1
less O -1
features B-ASP 0
than O -1
the O -1
older O -1
version O -1
. O -1

the O -1
volume B-ASP 0
is O -1
really O -1
low O -1
to O -1
low O -1
for O -1
a O -1
laptopwas O -1
not O -1
expectin O -1
t O -1
volume B-ASP -1
to O -1
be O -1
so O -1
lowan O -1
i O -1
hate O -1
that O -1
about O -1
this O -1
computer O -1

the O -1
volume B-ASP -1
is O -1
really O -1
low O -1
to O -1
low O -1
for O -1
a O -1
laptopwas O -1
not O -1
expectin O -1
t O -1
volume B-ASP 0
to O -1
be O -1
so O -1
lowan O -1
i O -1
hate O -1
that O -1
about O -1
this O -1
computer O -1

and O -1
its O -1
not O -1
hard O -1
to O -1
accidentally O -1
bang O -1
it O -1
against O -1
something O -1
so O -1
i O -1
recommend O -1
getting O -1
a O -1
case B-ASP 1
to O -1
protect O -1
it O -1
. O -1

I O -1
got O -1
this O -1
at O -1
an O -1
amazing O -1
price B-ASP 2
from O -1
Amazon O -1
and O -1
it O -1
arrived O -1
just O -1
in O -1
time O -1
. O -1

Every O -1
time O -1
I O -1
log B-ASP 0
into I-ASP 0
the I-ASP 0
system I-ASP 0
after O -1
a O -1
few O -1
hours O -1
, O -1
there O -1
is O -1
this O -1
endlessly O -1
frustrating O -1
process O -1
that O -1
I O -1
have O -1
to O -1
go O -1
through O -1
. O -1

Put O -1
a O -1
SSD B-ASP -1
and O -1
use O -1
a O -1
21' B-ASP 1
LED I-ASP 1
screen I-ASP 1
, O -1
this O -1
set B-ASP -1
up I-ASP -1
is O -1
silky O -1
smooth O -1
! O -1

Put O -1
a O -1
SSD B-ASP 1
and O -1
use O -1
a O -1
21' B-ASP -1
LED I-ASP -1
screen I-ASP -1
, O -1
this O -1
set B-ASP -1
up I-ASP -1
is O -1
silky O -1
smooth O -1
! O -1

Put O -1
a O -1
SSD B-ASP -1
and O -1
use O -1
a O -1
21' B-ASP -1
LED I-ASP -1
screen I-ASP -1
, O -1
this O -1
set B-ASP 2
up I-ASP 2
is O -1
silky O -1
smooth O -1
! O -1

The O -1
case B-ASP 0
is O -1
now O -1
slightly O -1
larger O -1
than O -1
the O -1
previous O -1
generation O -1
, O -1
but O -1
the O -1
lack O -1
of O -1
an O -1
external B-ASP -1
power I-ASP -1
supply I-ASP -1
justifies O -1
the O -1
small O -1
increase O -1
in O -1
size O -1
. O -1

The O -1
case B-ASP -1
is O -1
now O -1
slightly O -1
larger O -1
than O -1
the O -1
previous O -1
generation O -1
, O -1
but O -1
the O -1
lack O -1
of O -1
an O -1
external B-ASP 1
power I-ASP 1
supply I-ASP 1
justifies O -1
the O -1
small O -1
increase O -1
in O -1
size O -1
. O -1

I O -1
had O -1
to O -1
buy O -1
a O -1
wireless B-ASP 1
mouse I-ASP 1
to O -1
go O -1
with O -1
it O -1
, O -1
as O -1
I O -1
am O -1
old O -1
school O -1
and O -1
hate O -1
the O -1
pad B-ASP -1
, O -1
but O -1
knew O -1
that O -1
before O -1
I O -1
bought O -1
it O -1
, O -1
now O -1
it O -1
works B-ASP -1
great O -1
, O -1
need O -1
to O -1
get O -1
adjusted O -1
to O -1
the O -1
key B-ASP -1
board I-ASP -1
, O -1
as O -1
I O -1
am O -1
used O -1
to O -1
a O -1
bigger O -1
one O -1
and O -1
pounding O -1
. O -1

I O -1
had O -1
to O -1
buy O -1
a O -1
wireless B-ASP -1
mouse I-ASP -1
to O -1
go O -1
with O -1
it O -1
, O -1
as O -1
I O -1
am O -1
old O -1
school O -1
and O -1
hate O -1
the O -1
pad B-ASP 0
, O -1
but O -1
knew O -1
that O -1
before O -1
I O -1
bought O -1
it O -1
, O -1
now O -1
it O -1
works B-ASP -1
great O -1
, O -1
need O -1
to O -1
get O -1
adjusted O -1
to O -1
the O -1
key B-ASP -1
board I-ASP -1
, O -1
as O -1
I O -1
am O -1
used O -1
to O -1
a O -1
bigger O -1
one O -1
and O -1
pounding O -1
. O -1

I O -1
had O -1
to O -1
buy O -1
a O -1
wireless B-ASP -1
mouse I-ASP -1
to O -1
go O -1
with O -1
it O -1
, O -1
as O -1
I O -1
am O -1
old O -1
school O -1
and O -1
hate O -1
the O -1
pad B-ASP -1
, O -1
but O -1
knew O -1
that O -1
before O -1
I O -1
bought O -1
it O -1
, O -1
now O -1
it O -1
works B-ASP 2
great O -1
, O -1
need O -1
to O -1
get O -1
adjusted O -1
to O -1
the O -1
key B-ASP -1
board I-ASP -1
, O -1
as O -1
I O -1
am O -1
used O -1
to O -1
a O -1
bigger O -1
one O -1
and O -1
pounding O -1
. O -1

I O -1
had O -1
to O -1
buy O -1
a O -1
wireless B-ASP -1
mouse I-ASP -1
to O -1
go O -1
with O -1
it O -1
, O -1
as O -1
I O -1
am O -1
old O -1
school O -1
and O -1
hate O -1
the O -1
pad B-ASP -1
, O -1
but O -1
knew O -1
that O -1
before O -1
I O -1
bought O -1
it O -1
, O -1
now O -1
it O -1
works B-ASP -1
great O -1
, O -1
need O -1
to O -1
get O -1
adjusted O -1
to O -1
the O -1
key B-ASP 1
board I-ASP 1
, O -1
as O -1
I O -1
am O -1
used O -1
to O -1
a O -1
bigger O -1
one O -1
and O -1
pounding O -1
. O -1

When O -1
considering O -1
a O -1
Mac O -1
, O -1
look O -1
at O -1
the O -1
total O -1
cost B-ASP 1
of I-ASP 1
ownership I-ASP 1
and O -1
not O -1
just O -1
the O -1
initial O -1
price B-ASP -1
tag I-ASP -1
. O -1

When O -1
considering O -1
a O -1
Mac O -1
, O -1
look O -1
at O -1
the O -1
total O -1
cost B-ASP -1
of I-ASP -1
ownership I-ASP -1
and O -1
not O -1
just O -1
the O -1
initial O -1
price B-ASP 1
tag I-ASP 1
. O -1

Has O -1
all O -1
the O -1
other O -1
features B-ASP 2
I O -1
wanted O -1
including O -1
a O -1
VGA B-ASP -1
port I-ASP -1
, O -1
HDMI B-ASP -1
, O -1
ethernet B-ASP -1
and O -1
3 O -1
USB B-ASP -1
ports I-ASP -1
. O -1

Has O -1
all O -1
the O -1
other O -1
features B-ASP -1
I O -1
wanted O -1
including O -1
a O -1
VGA B-ASP 1
port I-ASP 1
, O -1
HDMI B-ASP -1
, O -1
ethernet B-ASP -1
and O -1
3 O -1
USB B-ASP -1
ports I-ASP -1
. O -1

Has O -1
all O -1
the O -1
other O -1
features B-ASP -1
I O -1
wanted O -1
including O -1
a O -1
VGA B-ASP -1
port I-ASP -1
, O -1
HDMI B-ASP 1
, O -1
ethernet B-ASP -1
and O -1
3 O -1
USB B-ASP -1
ports I-ASP -1
. O -1

Has O -1
all O -1
the O -1
other O -1
features B-ASP -1
I O -1
wanted O -1
including O -1
a O -1
VGA B-ASP -1
port I-ASP -1
, O -1
HDMI B-ASP -1
, O -1
ethernet B-ASP 1
and O -1
3 O -1
USB B-ASP -1
ports I-ASP -1
. O -1

Has O -1
all O -1
the O -1
other O -1
features B-ASP -1
I O -1
wanted O -1
including O -1
a O -1
VGA B-ASP -1
port I-ASP -1
, O -1
HDMI B-ASP -1
, O -1
ethernet B-ASP -1
and O -1
3 O -1
USB B-ASP 1
ports I-ASP 1
. O -1

The O -1
only O -1
thing O -1
I O -1
dislike O -1
about O -1
this O -1
laptop O -1
are O -1
the O -1
rubber B-ASP 0
pads I-ASP 0
found O -1
on O -1
the O -1
bottom O -1
of O -1
the O -1
computer O -1
for O -1
grip O -1
. O -1

It O -1
's O -1
a O -1
decent O -1
computer O -1
for O -1
the O -1
price B-ASP 1
and O -1
hopefully O -1
it O -1
will O -1
last O -1
a O -1
long O -1
time O -1
. O -1

The O -1
nicest O -1
part O -1
is O -1
the O -1
low O -1
heat B-ASP 2
output I-ASP 2
and O -1
ultra O -1
quiet O -1
operation B-ASP -1
. O -1

The O -1
nicest O -1
part O -1
is O -1
the O -1
low O -1
heat B-ASP -1
output I-ASP -1
and O -1
ultra O -1
quiet O -1
operation B-ASP 2
. O -1

I O -1
will O -1
upgrade B-ASP 2
the I-ASP 2
ram I-ASP 2
myself O -1
-LRB- O -1
because O -1
with O -1
this O -1
model O -1
you O -1
can O -1
you O -1
can O -1
do O -1
it O -1
-RRB- O -1
later O -1
on O -1
. O -1

The O -1
price B-ASP 2
is O -1
200 O -1
dollars O -1
down O -1
. O -1

this O -1
Mac O -1
Mini O -1
does O -1
not O -1
have O -1
a O -1
built-in B-ASP 1
mic I-ASP 1
, O -1
and O -1
it O -1
would O -1
seem O -1
that O -1
its O -1
Mac B-ASP -1
OS I-ASP -1
10.9 I-ASP -1
does O -1
not O -1
handle O -1
external B-ASP -1
microphones I-ASP -1
properly O -1
. O -1

this O -1
Mac O -1
Mini O -1
does O -1
not O -1
have O -1
a O -1
built-in B-ASP -1
mic I-ASP -1
, O -1
and O -1
it O -1
would O -1
seem O -1
that O -1
its O -1
Mac B-ASP 0
OS I-ASP 0
10.9 I-ASP 0
does O -1
not O -1
handle O -1
external B-ASP -1
microphones I-ASP -1
properly O -1
. O -1

this O -1
Mac O -1
Mini O -1
does O -1
not O -1
have O -1
a O -1
built-in B-ASP -1
mic I-ASP -1
, O -1
and O -1
it O -1
would O -1
seem O -1
that O -1
its O -1
Mac B-ASP -1
OS I-ASP -1
10.9 I-ASP -1
does O -1
not O -1
handle O -1
external B-ASP 1
microphones I-ASP 1
properly O -1
. O -1

A O -1
lot O -1
of O -1
features B-ASP 1
and O -1
shortcuts B-ASP -1
on O -1
the O -1
MBP O -1
that O -1
I O -1
was O -1
never O -1
exposed O -1
to O -1
on O -1
a O -1
normal O -1
PC O -1
. O -1

A O -1
lot O -1
of O -1
features B-ASP -1
and O -1
shortcuts B-ASP 1
on O -1
the O -1
MBP O -1
that O -1
I O -1
was O -1
never O -1
exposed O -1
to O -1
on O -1
a O -1
normal O -1
PC O -1
. O -1

Was O -1
n't O -1
sure O -1
if O -1
I O -1
was O -1
going O -1
to O -1
like O -1
it O -1
much O -1
less O -1
love O -1
it O -1
so O -1
I O -1
went O -1
to O -1
a O -1
local O -1
best O -1
buy O -1
and O -1
played O -1
around O -1
with O -1
the O -1
IOS B-ASP 2
system I-ASP 2
on O -1
a O -1
Mac O -1
Pro O -1
and O -1
it O -1
was O -1
totally O -1
unique O -1
and O -1
different O -1
. O -1

air O -1
has O -1
higher O -1
resolution B-ASP 2
but O -1
the O -1
fonts B-ASP -1
are O -1
small O -1
. O -1

air O -1
has O -1
higher O -1
resolution B-ASP -1
but O -1
the O -1
fonts B-ASP 0
are O -1
small O -1
. O -1

working B-ASP 2
with O -1
Mac O -1
is O -1
so O -1
much O -1
easier O -1
, O -1
so O -1
many O -1
cool O -1
features B-ASP -1
. O -1

working B-ASP -1
with O -1
Mac O -1
is O -1
so O -1
much O -1
easier O -1
, O -1
so O -1
many O -1
cool O -1
features B-ASP 2
. O -1

I O -1
like O -1
the O -1
brightness B-ASP 2
and O -1
adjustments B-ASP -1
. O -1

I O -1
like O -1
the O -1
brightness B-ASP -1
and O -1
adjustments B-ASP 2
. O -1

I O -1
only O -1
wish O -1
this O -1
mac O -1
had O -1
a O -1
CD/DVD B-ASP 1
player I-ASP 1
built O -1
in O -1
. O -1

The O -1
only O -1
thing O -1
I O -1
miss O -1
is O -1
that O -1
my O -1
old O -1
Alienware O -1
laptop O -1
had O -1
backlit B-ASP 0
keys I-ASP 0
. O -1

The O -1
only O -1
thing O -1
I O -1
miss O -1
are O -1
the O -1
"Home/End" B-ASP 1
type I-ASP 1
keys I-ASP 1
and O -1
other O -1
things O -1
that O -1
I O -1
grew O -1
accustomed O -1
to O -1
after O -1
so O -1
long O -1
. O -1

So O -1
happy O -1
with O -1
this O -1
purchase O -1
, O -1
I O -1
just O -1
wish O -1
it O -1
came O -1
with O -1
Microsoft B-ASP 1
Word I-ASP 1
. O -1

It O -1
has O -1
enough O -1
memory B-ASP 2
and O -1
speed B-ASP -1
to O -1
run O -1
my O -1
business O -1
with O -1
all O -1
the O -1
flexibility B-ASP -1
that O -1
comes O -1
with O -1
a O -1
laptop O -1
. O -1

It O -1
has O -1
enough O -1
memory B-ASP -1
and O -1
speed B-ASP 2
to O -1
run O -1
my O -1
business O -1
with O -1
all O -1
the O -1
flexibility B-ASP -1
that O -1
comes O -1
with O -1
a O -1
laptop O -1
. O -1

It O -1
has O -1
enough O -1
memory B-ASP -1
and O -1
speed B-ASP -1
to O -1
run O -1
my O -1
business O -1
with O -1
all O -1
the O -1
flexibility B-ASP 2
that O -1
comes O -1
with O -1
a O -1
laptop O -1
. O -1

The O -1
speed B-ASP 2
, O -1
the O -1
simplicity B-ASP -1
, O -1
the O -1
design B-ASP -1
. O -1
. O -1
it O -1
is O -1
lightyears O -1
ahead O -1
of O -1
any O -1
PC O -1
I O -1
have O -1
ever O -1
owned O -1
. O -1

The O -1
speed B-ASP -1
, O -1
the O -1
simplicity B-ASP 2
, O -1
the O -1
design B-ASP -1
. O -1
. O -1
it O -1
is O -1
lightyears O -1
ahead O -1
of O -1
any O -1
PC O -1
I O -1
have O -1
ever O -1
owned O -1
. O -1

The O -1
speed B-ASP -1
, O -1
the O -1
simplicity B-ASP -1
, O -1
the O -1
design B-ASP 2
. O -1
. O -1
it O -1
is O -1
lightyears O -1
ahead O -1
of O -1
any O -1
PC O -1
I O -1
have O -1
ever O -1
owned O -1
. O -1

The O -1
battery B-ASP 2
life I-ASP 2
is O -1
excellent O -1
, O -1
the O -1
display B-ASP -1
is O -1
excellent O -1
, O -1
and O -1
downloading B-ASP -1
apps I-ASP -1
is O -1
a O -1
breeze O -1
. O -1

The O -1
battery B-ASP -1
life I-ASP -1
is O -1
excellent O -1
, O -1
the O -1
display B-ASP 2
is O -1
excellent O -1
, O -1
and O -1
downloading B-ASP -1
apps I-ASP -1
is O -1
a O -1
breeze O -1
. O -1

The O -1
battery B-ASP -1
life I-ASP -1
is O -1
excellent O -1
, O -1
the O -1
display B-ASP -1
is O -1
excellent O -1
, O -1
and O -1
downloading B-ASP 2
apps I-ASP 2
is O -1
a O -1
breeze O -1
. O -1

The O -1
screen B-ASP 2
, O -1
the O -1
software B-ASP -1
and O -1
the O -1
smoothness O -1
of O -1
the O -1
operating B-ASP -1
system I-ASP -1
. O -1

The O -1
screen B-ASP -1
, O -1
the O -1
software B-ASP 2
and O -1
the O -1
smoothness O -1
of O -1
the O -1
operating B-ASP -1
system I-ASP -1
. O -1

The O -1
screen B-ASP -1
, O -1
the O -1
software B-ASP -1
and O -1
the O -1
smoothness O -1
of O -1
the O -1
operating B-ASP 2
system I-ASP 2
. O -1

i O -1
have O -1
dropped O -1
mine O -1
a O -1
couple O -1
times O -1
with O -1
only O -1
a O -1
slim B-ASP 1
plastic I-ASP 1
case I-ASP 1
covering O -1
it O -1
. O -1

I O -1
also O -1
made O -1
a O -1
recovery B-ASP 1
USB I-ASP 1
stick I-ASP 1
. O -1

But O -1
with O -1
this O -1
laptop O -1
, O -1
the O -1
bass B-ASP 0
is O -1
very O -1
weak O -1
and O -1
the O -1
sound B-ASP -1
comes O -1
out O -1
sounding O -1
tinny O -1
. O -1

But O -1
with O -1
this O -1
laptop O -1
, O -1
the O -1
bass B-ASP -1
is O -1
very O -1
weak O -1
and O -1
the O -1
sound B-ASP 0
comes O -1
out O -1
sounding O -1
tinny O -1
. O -1

The O -1
built B-ASP 2
quality I-ASP 2
is O -1
really O -1
good O -1
, O -1
I O -1
was O -1
so O -1
Happy O -1
and O -1
excited O -1
about O -1
this O -1
Product O -1
. O -1

I O -1
am O -1
loving O -1
the O -1
fast O -1
performance B-ASP 2
also O -1
. O -1

Further O -1
, O -1
this O -1
Mac O -1
Mini O -1
has O -1
a O -1
sloppy O -1
Bluetooth B-ASP 0
interface I-ASP 0
-LRB- O -1
courtesy O -1
of O -1
the O -1
Mac B-ASP -1
OS I-ASP -1
-RRB- O -1
and O -1
the O -1
range B-ASP -1
is O -1
poor O -1
. O -1

Further O -1
, O -1
this O -1
Mac O -1
Mini O -1
has O -1
a O -1
sloppy O -1
Bluetooth B-ASP -1
interface I-ASP -1
-LRB- O -1
courtesy O -1
of O -1
the O -1
Mac B-ASP 0
OS I-ASP 0
-RRB- O -1
and O -1
the O -1
range B-ASP -1
is O -1
poor O -1
. O -1

Further O -1
, O -1
this O -1
Mac O -1
Mini O -1
has O -1
a O -1
sloppy O -1
Bluetooth B-ASP -1
interface I-ASP -1
-LRB- O -1
courtesy O -1
of O -1
the O -1
Mac B-ASP -1
OS I-ASP -1
-RRB- O -1
and O -1
the O -1
range B-ASP 0
is O -1
poor O -1
. O -1

If O -1
you O -1
start O -1
on O -1
the O -1
far O -1
right O -1
side O -1
and O -1
scroll O -1
to O -1
your O -1
left O -1
the O -1
start B-ASP 1
menu I-ASP 1
will O -1
automatically O -1
come O -1
up O -1
. O -1

My O -1
only O -1
gripe O -1
would O -1
be O -1
the O -1
need O -1
to O -1
add O -1
more O -1
RAM B-ASP 0
. O -1

Fine O -1
if O -1
you O -1
have O -1
a O -1
touch B-ASP 1
screen I-ASP 1
. O -1

As O -1
far O -1
as O -1
user O -1
type O -1
- O -1
I O -1
dabble O -1
in O -1
everything O -1
from O -1
games B-ASP 1
-LRB- O -1
WoW O -1
-RRB- O -1
to O -1
Photoshop B-ASP -1
, O -1
but O -1
nothing O -1
professionally O -1
. O -1

As O -1
far O -1
as O -1
user O -1
type O -1
- O -1
I O -1
dabble O -1
in O -1
everything O -1
from O -1
games B-ASP -1
-LRB- O -1
WoW O -1
-RRB- O -1
to O -1
Photoshop B-ASP 1
, O -1
but O -1
nothing O -1
professionally O -1
. O -1

I O -1
re-seated O -1
the O -1
`` B-ASP 1
WLAN I-ASP 1
'' I-ASP 1
card I-ASP 1
inside O -1
and O -1
re-installed O -1
the O -1
LAN B-ASP -1
device I-ASP -1
drivers I-ASP -1
. O -1

I O -1
re-seated O -1
the O -1
`` B-ASP -1
WLAN I-ASP -1
'' I-ASP -1
card I-ASP -1
inside O -1
and O -1
re-installed O -1
the O -1
LAN B-ASP 1
device I-ASP 1
drivers I-ASP 1
. O -1

This O -1
by O -1
far O -1
beats O -1
any O -1
computer O -1
out O -1
on O -1
the O -1
market O -1
today O -1
built B-ASP 2
well O -1
, O -1
battery B-ASP -1
life I-ASP -1
AMAZING O -1
. O -1

This O -1
by O -1
far O -1
beats O -1
any O -1
computer O -1
out O -1
on O -1
the O -1
market O -1
today O -1
built B-ASP -1
well O -1
, O -1
battery B-ASP 2
life I-ASP 2
AMAZING O -1
. O -1

The O -1
OS B-ASP 2
is O -1
easy O -1
, O -1
and O -1
offers O -1
all O -1
kinds O -1
of O -1
surprises O -1
. O -1

I O -1
had O -1
to O -1
get O -1
Apple B-ASP 1
Customer I-ASP 1
Support I-ASP 1
to O -1
correct O -1
the O -1
problem O -1
. O -1

A O -1
veryimportant O -1
feature O -1
is O -1
Firewire B-ASP 2
800 I-ASP 2
which O -1
in O -1
my O -1
experience O -1
works O -1
better O -1
then O -1
USB3 B-ASP -1
-LRB- O -1
in O -1
PC O -1
enabled O -1
with O -1
USB3 B-ASP -1
-RRB- O -1
I O -1
was O -1
not O -1
originally O -1
sold O -1
on O -1
the O -1
MAC B-ASP -1
OS I-ASP -1
I O -1
felt O -1
it O -1
was O -1
inferior O -1
in O -1
many O -1
ways O -1
To O -1
Windows B-ASP -1
7 I-ASP -1
. O -1

A O -1
veryimportant O -1
feature O -1
is O -1
Firewire B-ASP -1
800 I-ASP -1
which O -1
in O -1
my O -1
experience O -1
works O -1
better O -1
then O -1
USB3 B-ASP 0
-LRB- O -1
in O -1
PC O -1
enabled O -1
with O -1
USB3 B-ASP -1
-RRB- O -1
I O -1
was O -1
not O -1
originally O -1
sold O -1
on O -1
the O -1
MAC B-ASP -1
OS I-ASP -1
I O -1
felt O -1
it O -1
was O -1
inferior O -1
in O -1
many O -1
ways O -1
To O -1
Windows B-ASP -1
7 I-ASP -1
. O -1

A O -1
veryimportant O -1
feature O -1
is O -1
Firewire B-ASP -1
800 I-ASP -1
which O -1
in O -1
my O -1
experience O -1
works O -1
better O -1
then O -1
USB3 B-ASP -1
-LRB- O -1
in O -1
PC O -1
enabled O -1
with O -1
USB3 B-ASP 1
-RRB- O -1
I O -1
was O -1
not O -1
originally O -1
sold O -1
on O -1
the O -1
MAC B-ASP -1
OS I-ASP -1
I O -1
felt O -1
it O -1
was O -1
inferior O -1
in O -1
many O -1
ways O -1
To O -1
Windows B-ASP -1
7 I-ASP -1
. O -1

A O -1
veryimportant O -1
feature O -1
is O -1
Firewire B-ASP -1
800 I-ASP -1
which O -1
in O -1
my O -1
experience O -1
works O -1
better O -1
then O -1
USB3 B-ASP -1
-LRB- O -1
in O -1
PC O -1
enabled O -1
with O -1
USB3 B-ASP -1
-RRB- O -1
I O -1
was O -1
not O -1
originally O -1
sold O -1
on O -1
the O -1
MAC B-ASP 0
OS I-ASP 0
I O -1
felt O -1
it O -1
was O -1
inferior O -1
in O -1
many O -1
ways O -1
To O -1
Windows B-ASP -1
7 I-ASP -1
. O -1

A O -1
veryimportant O -1
feature O -1
is O -1
Firewire B-ASP -1
800 I-ASP -1
which O -1
in O -1
my O -1
experience O -1
works O -1
better O -1
then O -1
USB3 B-ASP -1
-LRB- O -1
in O -1
PC O -1
enabled O -1
with O -1
USB3 B-ASP -1
-RRB- O -1
I O -1
was O -1
not O -1
originally O -1
sold O -1
on O -1
the O -1
MAC B-ASP -1
OS I-ASP -1
I O -1
felt O -1
it O -1
was O -1
inferior O -1
in O -1
many O -1
ways O -1
To O -1
Windows B-ASP 2
7 I-ASP 2
. O -1

I O -1
like O -1
iTunes B-ASP 2
, O -1
the O -1
apparent O -1
security B-ASP -1
, O -1
the O -1
Mini B-ASP -1
form I-ASP -1
factor I-ASP -1
, O -1
all O -1
the O -1
nice O -1
graphics B-ASP -1
stuff I-ASP -1
. O -1

I O -1
like O -1
iTunes B-ASP -1
, O -1
the O -1
apparent O -1
security B-ASP 2
, O -1
the O -1
Mini B-ASP -1
form I-ASP -1
factor I-ASP -1
, O -1
all O -1
the O -1
nice O -1
graphics B-ASP -1
stuff I-ASP -1
. O -1

I O -1
like O -1
iTunes B-ASP -1
, O -1
the O -1
apparent O -1
security B-ASP -1
, O -1
the O -1
Mini B-ASP 2
form I-ASP 2
factor I-ASP 2
, O -1
all O -1
the O -1
nice O -1
graphics B-ASP -1
stuff I-ASP -1
. O -1

I O -1
like O -1
iTunes B-ASP -1
, O -1
the O -1
apparent O -1
security B-ASP -1
, O -1
the O -1
Mini B-ASP -1
form I-ASP -1
factor I-ASP -1
, O -1
all O -1
the O -1
nice O -1
graphics B-ASP 2
stuff I-ASP 2
. O -1

The O -1
first O -1
time O -1
I O -1
used O -1
the O -1
card B-ASP 0
reader I-ASP 0
it O -1
took O -1
half O -1
an O -1
hour O -1
and O -1
a O -1
pair O -1
of O -1
tweezers O -1
to O -1
remove B-ASP -1
the I-ASP -1
card I-ASP -1
. O -1

The O -1
first O -1
time O -1
I O -1
used O -1
the O -1
card B-ASP -1
reader I-ASP -1
it O -1
took O -1
half O -1
an O -1
hour O -1
and O -1
a O -1
pair O -1
of O -1
tweezers O -1
to O -1
remove B-ASP 0
the I-ASP 0
card I-ASP 0
. O -1

After O -1
replacing O -1
the O -1
spinning B-ASP 1
hard I-ASP 1
disk I-ASP 1
with O -1
an O -1
ssd B-ASP -1
drive I-ASP -1
, O -1
my O -1
mac O -1
is O -1
just O -1
flying O -1
. O -1

After O -1
replacing O -1
the O -1
spinning B-ASP -1
hard I-ASP -1
disk I-ASP -1
with O -1
an O -1
ssd B-ASP 2
drive I-ASP 2
, O -1
my O -1
mac O -1
is O -1
just O -1
flying O -1
. O -1

I O -1
know O -1
some O -1
people O -1
complained O -1
about O -1
HDMI B-ASP 1
issues O -1
but O -1
they O -1
released O -1
a O -1
firmware B-ASP -1
patch I-ASP -1
to O -1
address O -1
that O -1
issue O -1
. O -1

I O -1
know O -1
some O -1
people O -1
complained O -1
about O -1
HDMI B-ASP -1
issues O -1
but O -1
they O -1
released O -1
a O -1
firmware B-ASP 1
patch I-ASP 1
to O -1
address O -1
that O -1
issue O -1
. O -1

With O -1
the O -1
needs O -1
of O -1
a O -1
professional O -1
photographer O -1
I O -1
generally O -1
need O -1
to O -1
keep O -1
up O -1
with O -1
the O -1
best O -1
specs B-ASP 1
. O -1

packing B-ASP 2
and O -1
everything O -1
was O -1
perfect O -1

I O -1
called O -1
Toshiba O -1
where O -1
I O -1
gave O -1
them O -1
the O -1
serial O -1
number O -1
and O -1
they O -1
informed O -1
me O -1
that O -1
they O -1
were O -1
having O -1
issues O -1
with O -1
the O -1
mother B-ASP 1
boards I-ASP 1
. O -1

I O -1
seem O -1
to O -1
be O -1
having O -1
repeat O -1
problems O -1
as O -1
the O -1
Mother B-ASP 0
Board I-ASP 0
in O -1
this O -1
one O -1
is O -1
diagnosed O -1
as O -1
faulty O -1
, O -1
related O -1
to O -1
the O -1
graphics B-ASP -1
card I-ASP -1
. O -1

I O -1
seem O -1
to O -1
be O -1
having O -1
repeat O -1
problems O -1
as O -1
the O -1
Mother B-ASP -1
Board I-ASP -1
in O -1
this O -1
one O -1
is O -1
diagnosed O -1
as O -1
faulty O -1
, O -1
related O -1
to O -1
the O -1
graphics B-ASP 0
card I-ASP 0
. O -1

It O -1
also O -1
comes O -1
with O -1
4G B-ASP 1
of I-ASP 1
RAM I-ASP 1
but O -1
if O -1
you O -1
're O -1
like O -1
me O -1
you O -1
want O -1
to O -1
max O -1
that O -1
out O -1
so O -1
I O -1
immediately O -1
put O -1
8G B-ASP -1
of I-ASP -1
RAM I-ASP -1
in O -1
her O -1
and O -1
I O -1
've O -1
never O -1
used O -1
a O -1
computer O -1
that O -1
performs B-ASP -1
better O -1
. O -1

It O -1
also O -1
comes O -1
with O -1
4G B-ASP -1
of I-ASP -1
RAM I-ASP -1
but O -1
if O -1
you O -1
're O -1
like O -1
me O -1
you O -1
want O -1
to O -1
max O -1
that O -1
out O -1
so O -1
I O -1
immediately O -1
put O -1
8G B-ASP 1
of I-ASP 1
RAM I-ASP 1
in O -1
her O -1
and O -1
I O -1
've O -1
never O -1
used O -1
a O -1
computer O -1
that O -1
performs B-ASP -1
better O -1
. O -1

It O -1
also O -1
comes O -1
with O -1
4G B-ASP -1
of I-ASP -1
RAM I-ASP -1
but O -1
if O -1
you O -1
're O -1
like O -1
me O -1
you O -1
want O -1
to O -1
max O -1
that O -1
out O -1
so O -1
I O -1
immediately O -1
put O -1
8G B-ASP -1
of I-ASP -1
RAM I-ASP -1
in O -1
her O -1
and O -1
I O -1
've O -1
never O -1
used O -1
a O -1
computer O -1
that O -1
performs B-ASP 2
better O -1
. O -1

This O -1
computer O -1
is O -1
also O -1
awesome O -1
for O -1
my O -1
sons O -1
virtual B-ASP 2
home I-ASP 2
schooling I-ASP 2
. O -1

Cost B-ASP 0
is O -1
more O -1
as O -1
compared O -1
to O -1
other O -1
brands O -1
. O -1

also O -1
... O -1
- O -1
excellent O -1
operating B-ASP 2
system I-ASP 2
- O -1
size B-ASP -1
and O -1
weight B-ASP -1
for O -1
optimal O -1
mobility B-ASP -1
- O -1
excellent O -1
durability B-ASP -1
of I-ASP -1
the I-ASP -1
battery I-ASP -1
- O -1
the O -1
functions B-ASP -1
provided I-ASP -1
by I-ASP -1
the I-ASP -1
trackpad I-ASP -1
is O -1
unmatched O -1
by O -1
any O -1
other O -1
brand O -1
- O -1

also O -1
... O -1
- O -1
excellent O -1
operating B-ASP -1
system I-ASP -1
- O -1
size B-ASP 2
and O -1
weight B-ASP -1
for O -1
optimal O -1
mobility B-ASP -1
- O -1
excellent O -1
durability B-ASP -1
of I-ASP -1
the I-ASP -1
battery I-ASP -1
- O -1
the O -1
functions B-ASP -1
provided I-ASP -1
by I-ASP -1
the I-ASP -1
trackpad I-ASP -1
is O -1
unmatched O -1
by O -1
any O -1
other O -1
brand O -1
- O -1

also O -1
... O -1
- O -1
excellent O -1
operating B-ASP -1
system I-ASP -1
- O -1
size B-ASP -1
and O -1
weight B-ASP 2
for O -1
optimal O -1
mobility B-ASP -1
- O -1
excellent O -1
durability B-ASP -1
of I-ASP -1
the I-ASP -1
battery I-ASP -1
- O -1
the O -1
functions B-ASP -1
provided I-ASP -1
by I-ASP -1
the I-ASP -1
trackpad I-ASP -1
is O -1
unmatched O -1
by O -1
any O -1
other O -1
brand O -1
- O -1

also O -1
... O -1
- O -1
excellent O -1
operating B-ASP -1
system I-ASP -1
- O -1
size B-ASP -1
and O -1
weight B-ASP -1
for O -1
optimal O -1
mobility B-ASP 2
- O -1
excellent O -1
durability B-ASP -1
of I-ASP -1
the I-ASP -1
battery I-ASP -1
- O -1
the O -1
functions B-ASP -1
provided I-ASP -1
by I-ASP -1
the I-ASP -1
trackpad I-ASP -1
is O -1
unmatched O -1
by O -1
any O -1
other O -1
brand O -1
- O -1

also O -1
... O -1
- O -1
excellent O -1
operating B-ASP -1
system I-ASP -1
- O -1
size B-ASP -1
and O -1
weight B-ASP -1
for O -1
optimal O -1
mobility B-ASP -1
- O -1
excellent O -1
durability B-ASP 2
of I-ASP 2
the I-ASP 2
battery I-ASP 2
- O -1
the O -1
functions B-ASP -1
provided I-ASP -1
by I-ASP -1
the I-ASP -1
trackpad I-ASP -1
is O -1
unmatched O -1
by O -1
any O -1
other O -1
brand O -1
- O -1

also O -1
... O -1
- O -1
excellent O -1
operating B-ASP -1
system I-ASP -1
- O -1
size B-ASP -1
and O -1
weight B-ASP -1
for O -1
optimal O -1
mobility B-ASP -1
- O -1
excellent O -1
durability B-ASP -1
of I-ASP -1
the I-ASP -1
battery I-ASP -1
- O -1
the O -1
functions B-ASP 2
provided I-ASP 2
by I-ASP 2
the I-ASP 2
trackpad I-ASP 2
is O -1
unmatched O -1
by O -1
any O -1
other O -1
brand O -1
- O -1

This O -1
hardware B-ASP 2
seems O -1
to O -1
be O -1
better O -1
than O -1
the O -1
iMac O -1
in O -1
that O -1
it O -1
is O -1
n't O -1
$ O -1
1400 O -1
and O -1
smaller O -1
. O -1

I O -1
've O -1
had O -1
it O -1
for O -1
about O -1
2 O -1
months O -1
now O -1
and O -1
found O -1
no O -1
issues O -1
with O -1
software B-ASP 1
or O -1
updates B-ASP -1
. O -1

I O -1
've O -1
had O -1
it O -1
for O -1
about O -1
2 O -1
months O -1
now O -1
and O -1
found O -1
no O -1
issues O -1
with O -1
software B-ASP -1
or O -1
updates B-ASP 1
. O -1

the O -1
latest O -1
version O -1
does O -1
not O -1
have O -1
a O -1
disc B-ASP 1
drive I-ASP 1
. O -1


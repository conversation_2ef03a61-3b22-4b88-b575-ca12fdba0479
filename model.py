import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import BertModel, BertConfig
from config import *
from torchcrf import CRF
from transformers.models.bert.modeling_bert import BertAttention, BertPooler
from peft import LoraConfig, get_peft_model, TaskType

from transformers import logging
logging.set_verbosity_error()

config = BertConfig.from_pretrained(BERT_MODEL_NAME)
# 配置BERT最大长度
config.max_position_embeddings = BERT_MAX_LEN


class Model(nn.Module):
    def __init__(self):
        super().__init__()
        # 初始化BERT模型
        self.bert = BertModel.from_pretrained(BERT_MODEL_NAME, config=config)

        # 如果启用LoRA，应用LoRA配置
        if LORA_ENABLED:
            # 创建LoRA配置
            lora_config = LoraConfig(
                task_type=TaskType.FEATURE_EXTRACTION,  # 特征提取任务
                r=LORA_R,  # 低秩矩阵的维度
                lora_alpha=LORA_ALPHA,  # 缩放参数
                lora_dropout=LORA_DROPOUT,  # Dropout率
                target_modules=LORA_TARGET_MODULES,  # 目标模块
                bias=LORA_BIAS  # 偏置处理方式
            )
            # 应用LoRA到BERT模型
            self.bert = get_peft_model(self.bert, lora_config)
            # 打印LoRA参数信息
            self.bert.print_trainable_parameters()
        self.ent_linear = nn.Linear(BERT_DIM, ENT_SIZE)
        self.crf = CRF(ENT_SIZE, batch_first=True)

        # 动态创建情感分析线性层，根据LCF配置决定创建哪些层
        if LCF == 'fusion':
            # 只创建fusion模式需要的线性层
            self.pola_fusion_linear = nn.Linear(BERT_DIM * 3, BERT_DIM)
        elif LCF == 'fusion2':
            # 只创建fusion2模式需要的线性层
            self.pola_fusion_linear = nn.Linear(BERT_DIM * 2, BERT_DIM)

        # 根据配置创建特征处理器
        if FEATURE_PROCESSOR == 'attention':
            self.attention = BertAttention(config)
        elif FEATURE_PROCESSOR == 'linear':
            # 创建简单的线性特征处理器
            self.feature_linear1 = nn.Linear(BERT_DIM, LINEAR_HIDDEN_SIZE)
            self.feature_linear2 = nn.Linear(LINEAR_HIDDEN_SIZE, BERT_DIM)

        self.pola_linear = nn.Linear(BERT_DIM, POLA_DIM)
        self.pooler = BertPooler(config)

        # 简化dropout层，使用统一的dropout率
        self.dropout = nn.Dropout(DROPOUT_RATE)  # 通用dropout层，用于分类器前
        self.dropout_task = nn.Dropout(DROPOUT_TASK)  # 任务特定层的dropout

    def get_text_encoded(self, input_ids, mask):
        # 确保不超过BERT最大长度
        # 使用强制类型转换避免Tracer警告
        seq_len = input_ids.size(1)
        if seq_len > BERT_MAX_LEN:
            input_ids = input_ids[:, :BERT_MAX_LEN]
            mask = mask[:, :BERT_MAX_LEN]
        # 获取BERT输出
        bert_output = self.bert(input_ids, attention_mask=mask)[0]
        # 注意: BERT内部已有dropout，这里不再添加额外的dropout
        return bert_output

    def get_entity_fc(self, text_encoded):
        # 在实体识别特征提取前应用dropout
        text_encoded = self.dropout_task(text_encoded)
        return self.ent_linear(text_encoded)

    def get_entity_crf(self, entity_fc, mask):
        return self.crf.decode(entity_fc, mask)

    def get_entity(self, input_ids, mask):
        text_encoded = self.get_text_encoded(input_ids, mask)
        entity_fc = self.get_entity_fc(text_encoded)
        pred_ent_label = self.get_entity_crf(entity_fc, mask)
        return pred_ent_label

    def get_pola(self, input_ids, mask, ent_cdm, ent_cdw):
        text_encoded = self.get_text_encoded(input_ids, mask)

        # shape [b, c] -> [b, c, 768]
        ent_cdm_weight = ent_cdm.unsqueeze(-1).repeat(1, 1, BERT_DIM)
        ent_cdw_weight = ent_cdw.unsqueeze(-1).repeat(1, 1, BERT_DIM)
        cdm_feature = torch.mul(text_encoded, ent_cdm_weight)
        cdw_feature = torch.mul(text_encoded, ent_cdw_weight)

        # 根据配置，使用不同的策略，重新组合特征，在降维到768维
        if LCF == 'fusion':
            out = torch.cat([text_encoded, cdm_feature, cdw_feature], dim=-1)
            out = self.pola_fusion_linear(out)
        elif LCF == 'fusion2':
            out = torch.cat([text_encoded, cdw_feature], dim=-1)
            out = self.pola_fusion_linear(out)
        elif LCF == 'cdw':
            out = cdw_feature

        # 在情感分析特征提取前应用dropout
        out = self.dropout_task(out)

        # 根据配置选择不同的特征处理方式
        if FEATURE_PROCESSOR == 'attention':
            # 使用BertAttention处理特征
            # self-attension 结合上下文信息，增强语义
            # 注意: BertAttention内部已有dropout
            out = self.attention(out, None)
            # 使用attention输出，不再添加额外的dropout
            out = out[0]
        elif FEATURE_PROCESSOR == 'linear':
            # 使用简单的前馈神经网络处理特征
            out = F.relu(self.feature_linear1(out))
            out = self.dropout_task(out)  # 中间层dropout
            out = self.feature_linear2(out)
        # 如果是'none'，则不进行额外处理

        # pooler 取[CLS]标记位，作为整个句子的特征
        # 注意: BertPooler内部已有dropout
        out = torch.sigmoid(self.pooler(torch.tanh(out)))

        # 在最终分类器前应用dropout
        out = self.dropout(out)
        return self.pola_linear(out)

    def ent_loss_fn(self, input_ids, ent_label, mask):
        text_encoded = self.get_text_encoded(input_ids, mask)
        entity_fc = self.get_entity_fc(text_encoded)

        # 如果启用了CRF类别权重，则应用权重
        if CRF_CLASS_WEIGHTS_ENABLED:
            # 创建权重张量
            weights = torch.tensor(
                [CRF_CLASS_WEIGHTS[i] for i in range(ENT_SIZE)],
                device=entity_fc.device
            )

            # 对发射分数应用权重
            # 对每个类别的发射分数乘以相应的权重
            # 形状: [batch_size, seq_len, num_tags]
            weighted_entity_fc = entity_fc.clone()
            for i in range(ENT_SIZE):
                weighted_entity_fc[:, :, i] = entity_fc[:, :, i] * weights[i]

            # 使用加权后的发射分数计算CRF损失
            return -self.crf.forward(weighted_entity_fc, ent_label, mask, reduction='mean')
        else:
            # 不使用权重，直接计算CRF损失
            return -self.crf.forward(entity_fc, ent_label, mask, reduction='mean')

    def pola_loss_fn(self, pred_pola, pola_label):
        # 如果启用了情感分析类别权重，则应用权重
        if POLA_CLASS_WEIGHTS_ENABLED:
            # 创建权重张量
            weights = torch.tensor(
                [POLA_CLASS_WEIGHTS[i] for i in range(POLA_DIM)],
                device=pred_pola.device
            )
            # 使用加权交叉熵损失
            return F.cross_entropy(pred_pola, pola_label, weight=weights)
        else:
            # 不使用权重，直接计算交叉熵损失
            return F.cross_entropy(pred_pola, pola_label)

    def loss_fn(self, input_ids, ent_label, mask, pred_pola, pola_label):
        # 使用加权损失：从配置中读取权重
        return TASK_WEIGHT_ENT * self.ent_loss_fn(input_ids, ent_label, mask) + \
            TASK_WEIGHT_POLA * self.pola_loss_fn(pred_pola, pola_label)

    def get_bert_lora_params(self):
        """获取BERT的LoRA参数，用于单独设置学习率

        Returns:
            list: BERT的LoRA参数列表
        """
        if not LORA_ENABLED:
            return []

        # 获取所有可训练的LoRA参数
        bert_lora_params = []
        for name, param in self.bert.named_parameters():
            if 'lora' in name.lower() and param.requires_grad:
                bert_lora_params.append(param)

        return bert_lora_params

    def get_entity_params(self):
        """获取实体识别相关的参数，用于单独设置学习率

        Returns:
            list: 实体识别相关的参数列表
        """
        return list(self.ent_linear.parameters()) + list(self.crf.parameters())

    def get_pola_params(self):
        """获取情感分析相关的参数，用于单独设置学习率

        Returns:
            list: 情感分析相关的参数列表
        """
        # 基本参数：情感分类器和池化层
        pola_params = list(self.pola_linear.parameters()) + \
            list(self.pooler.parameters())

        # 根据特征处理器配置添加相应参数
        if FEATURE_PROCESSOR == 'attention' and hasattr(self, 'attention'):
            pola_params += list(self.attention.parameters())
        elif FEATURE_PROCESSOR == 'linear' and hasattr(self, 'feature_linear1'):
            pola_params += list(self.feature_linear1.parameters())
            pola_params += list(self.feature_linear2.parameters())

        # 如果存在融合层，添加其参数
        if hasattr(self, 'pola_fusion_linear'):
            pola_params += list(self.pola_fusion_linear.parameters())

        return pola_params

    def forward(self, input_ids, mask, ent_cdm=None, ent_cdw=None):
        """TensorBoard需要的forward方法，用于可视化模型结构

        Args:
            input_ids: 输入的token ids
            mask: 注意力掩码
            ent_cdm: 实体上下文动态掩码
            ent_cdw: 实体上下文动态权重

        Returns:
            pred_pola: 情感极性预测结果
        """
        # 如果没有提供ent_cdm和ent_cdw，创建默认值
        if ent_cdm is None or ent_cdw is None:
            batch_size, seq_len = input_ids.size()
            if ent_cdm is None:
                ent_cdm = torch.ones((batch_size, seq_len),
                                     device=input_ids.device)
            if ent_cdw is None:
                ent_cdw = torch.ones((batch_size, seq_len),
                                     device=input_ids.device)

        # 使用get_pola方法获取情感极性预测
        return self.get_pola(input_ids, mask, ent_cdm, ent_cdw)


if __name__ == '__main__':
    input_ids = torch.randint(0, 3000, (2, 30)).to(DEVICE)
    mask = torch.ones((2, 30)).bool().to(DEVICE)
    model = Model().to(DEVICE)
    # print(model.get_entity(input_ids, mask))
    ent_cdm = torch.rand((2, 30)).to(DEVICE)
    ent_cdw = torch.rand((2, 30)).to(DEVICE)
    print(model.get_pola(input_ids, mask, ent_cdm, ent_cdw))
